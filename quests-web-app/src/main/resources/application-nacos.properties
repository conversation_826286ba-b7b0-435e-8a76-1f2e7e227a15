###########################kweb application config########################
kweb.env=dev
kweb.saas-id=vibra
zipkin.host=jaeger-collector.vibra-dev.svc.kiki.local
zookeeper.url=zookeeper.vibra-dev.svc.kiki.local:2181
server.port=8080
management.server.port=9090
###########################dubbo config###################################
dubbo.reference.check=false
dubbo.consumer.check=false
dubbo.registry.check=false
dubbo.application.name=kweb
dubbo.name=kweb
dubbo.application.id=kweb
dubbo.application.version=1.0.0
dubbo.protocol.server=netty
dubbo.protocol.name=dubbo
dubbo.protocol.port=20887
dubbo.protocol.threadpool=fixed
dubbo.protocol.threads=50
dubbo.protocol.queues=1000
dubbo.registry.address=zookeeper://zookeeper.vibra-dev.svc.kiki.local:2181
dubbo.consumer.group=kktd
dubbo.provider.group=kktd
dubbo.provider.timeout=10000
dubbo.consumer.timeout=10000
dubbo.provider.retries=1
dubbo.consumer.retries=1
dubbo.consumer.filter=dtm
dubbo.provider.filter=dtm
###########################redis config####################################
spring.data.redis.host=@redis.host@
spring.data.redis.password=@redis.password@
spring.data.redis.port=@redis.port@
spring.data.redis.database=@redis.database@
spring.data.redis.lettuce.pool.max-active=@redis.max-active@
##################nacos config##############################################
#æ¬å°å¯å¨ spring.cloud.nacos.config.enabled=false
spring.cloud.nacos.config.enabled=false
##################ignite config#############################################
kiki.ignite.namespace=kikitrade
kiki.ignite.application-name=kweb
kiki.ignite.local=false
kiki.ignite.client=true
kiki.ignite.environment-namespace=vibra-dev
###########################jwt token########################################
# ots
kiki.ots.endpoint=https://KikiVibraBeta.ap-southeast-1.ots.aliyuncs.com
kiki.ots.instance-name=KikiVibraBeta

###########################dingtalk#########################################
dingtalk.app.root=http://dingtalk.dipbit.xyz:8081/manage
dingtalk.appId=dingeb4kipoasyzafitk
dingtalk.appSecret=kkkreonxWpPjYhFgRspCw4ujDwSp3cHaKnx5DJx10DjfcjW5RFsg2S3EPF1vOZIo
dingtalk.agentId=217974206


################################ons#########################################
kiki.ons.address=http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
kiki.ons.group-id=GID_WEB_VIBRA_DEV
kiki.ons.env=_DEV
kiki.ons.tag=dev
kweb.ons-topic-ding-talk-business=T_WEB_DINGTALK_VIBRA_DEV

kiki.ons.broadcast-consumer.enable=true
kiki.ons.broadcast-group-id=GID_WEB_BROADCAST_VIBRA_DEV
kweb.ons-topic-cache-refresh=T_WEB_CACHE_REFRESH_VIBRA_DEV
#################elasticjob config##########################################
### Elastic Job switch
elasticjob.enabled=false
elasticjob.reg-center.server-lists=@zookeeper.url@
elasticjob.reg-center.namespace=kweb/jobs
elasticjob.reg-center.base-sleep-time-milliseconds=1000
elasticjob.reg-center.max-sleep-time-milliseconds=3000
elasticjob.reg-center.max-retries=5
elasticjob.reg-center.session-timeout-milliseconds=10000
elasticjob.reg-center.connection-timeout-milliseconds=10000

# sharding refresh job
#elasticjob.jobs.syncDingProcessJob.elastic-job-class=com.kikitrade.kweb.schedule.SyncProcessJob
#elasticjob.jobs.syncDingProcessJob.cron=30 */2 * * * ?
#elasticjob.jobs.syncDingProcessJob.sharding-total-count=2
#elasticjob.jobs.syncDingProcessJob.sharding-item-parameters=0=0,1=1
#elasticjob.jobs.syncDingProcessRetryJob.elastic-job-class=com.kikitrade.kweb.schedule.SyncProcessRetryJob
#elasticjob.jobs.syncDingProcessRetryJob.cron=45 */2 * * * ?
#elasticjob.jobs.syncDingProcessRetryJob.sharding-total-count=2
#elasticjob.jobs.syncDingProcessRetryJob.sharding-item-parameters=0=0,1=1
###########################zendesk jwt######################################
zendesk.chat.sign=BE2387A4925E355E2B89807ADDDE1538695ECA7A821EFE2EDF65675924E8FB5F