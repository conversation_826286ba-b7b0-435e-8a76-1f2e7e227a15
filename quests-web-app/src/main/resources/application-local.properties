###########################kweb application config########################
#spring.profiles.active=local
kweb.env=@kweb.env@
kweb.saas-id=@kweb.saas-id@
kweb.enable-elastic-job=false
app.log.path=@app.log.path@
zookeeper.url=@zookeeper.url@
###########################dubbo config###################################
dubbo.reference.check=false
dubbo.consumer.check=false
dubbo.registry.check=false
dubbo.application.name=@dubbo.name@
dubbo.name=@dubbo.name@
dubbo.application.id=@dubbo.name@
dubbo.application.version=1.0.0
dubbo.protocol.server=netty
dubbo.protocol.name=dubbo
dubbo.protocol.port=@dubbo.protocol.port@
dubbo.protocol.threadpool=fixed
dubbo.protocol.threads=50
dubbo.protocol.queues=1000
dubbo.registry.address=zookeeper://@zookeeper.url@
dubbo.consumer.group=@dubbo.consumer.group@
dubbo.provider.group=@dubbo.provider.group@
#TODO REMOVE
#dubbo.protocol.host=@dubbo.protocol.host@
dubbo.provider.timeout=10000
dubbo.consumer.timeout=10000
dubbo.provider.retries=1
dubbo.consumer.retries=1
dubbo.consumer.filter=tracing
dubbo.provider.filter=tracing

spring.zipkin.baseUrl=http://api.dipbit.xyz:9411
spring.sleuth.sampler.probability=1.0
kiki.sleuth.enable-web=true
##################nacos config##############################################
spring.cloud.nacos.config.enabled=@nacos.config.enabled@
spring.cloud.nacos.config.group=@nacos.config.group@
spring.cloud.nacos.config.name=@nacos.config.name@
spring.cloud.nacos.config.file-extension=@nacos.config.file-extension@
##################ignite config#############################################
kiki.ignite.namespace=@ignite.namespace@
kiki.ignite.application-name=@ignite.application.name@
kiki.ignite.local=@ignite.environment.local@
kiki.ignite.environment-namespace=@ignite.environment.namespace@
###########################jwt token########################################
###########ots###########
kiki.ots.endpoint=@kiki.ots.endpoint@
kiki.ots.instance-name=@kiki.ots.instance-name@
#########dingtalk##########
dingtalk.app.root=@dingtalk.app.root@
dingtalk.appId=@dingtalk.appId@
dingtalk.appSecret=@dingtalk.appSecret@
dingtalk.agentId=@dingtalk.agentId@
##########ons##########
kiki.ons.address=@kiki.ons.address@
kiki.ons.group-id=@kiki.ons.group-id@
kiki.ons.env=@kiki.ons.env@
kiki.ons.tag=@kiki.ons.tag@
kiki.ons.enable-traffic=true
kweb.ons-topic-ding-talk-business=@kweb.ons-topic-ding-talk-business@
kiki.ons.broadcast-consumer.enable=@kiki.ons.broadcast-consumer.enable@
kiki.ons.broadcast-group-id=@kiki.ons.broadcast-group-id@
kweb.ons-topic-cache-refresh=@kweb.ons-topic-cache-refresh@
kweb.query-customer-open=@kweb.query-customer-open@
kiki.ons.cache-refresh-topic=@kiki.ons.cache-refresh-topic@

#################elasticjob config ##################
elasticjob.reg-center.server-lists=@zookeeper.url@
elasticjob.reg-center.namespace=kweb/jobs
elasticjob.reg-center.base-sleep-time-milliseconds=1000
elasticjob.reg-center.max-sleep-time-milliseconds=3000
elasticjob.reg-center.max-retries=5
elasticjob.reg-center.session-timeout-milliseconds=10000
elasticjob.reg-center.connection-timeout-milliseconds=10000
elasticjob.model=env
elasticjob.envs.green=close
# sharding refresh job
#elasticjob.jobs.syncDingProcessJob.elastic-job-class=com.kikitrade.kweb.schedule.SyncProcessJob
#elasticjob.jobs.syncDingProcessJob.cron=30 */2 * * * ?
#elasticjob.jobs.syncDingProcessJob.sharding-total-count=2
#elasticjob.jobs.syncDingProcessJob.sharding-item-parameters=0=0,1=1
#elasticjob.jobs.syncDingProcessRetryJob.elastic-job-class=com.kikitrade.kweb.schedule.SyncProcessRetryJob
#elasticjob.jobs.syncDingProcessRetryJob.cron=45 */2 * * * ?
#elasticjob.jobs.syncDingProcessRetryJob.sharding-total-count=2
#elasticjob.jobs.syncDingProcessRetryJob.sharding-item-parameters=0=0,1=1
#fail notify retry job
#elasticjob.jobs.failNotifyRetryJob.elastic-job-class=com.kikitrade.kweb.schedule.FailNotifyRetryJob
#elasticjob.jobs.failNotifyRetryJob.cron=0 */10 * * * ?
#elasticjob.jobs.failNotifyRetryJob.sharding-total-count=1
#elasticjob.jobs.failNotifyRetryJob.sharding-item-parameters=0=0

#################elasticjob config ##################
#zendeskä¸­èå¤©çæjwtçç­¾å
zendesk.chat.sign=@zendesk.chat.sign@

kiki.afs.open=false

system.status=upgrading
ip.whitelist=@ip.whitelist@
margin.function.open=@margin.function.open@

#äº¤æåæåäº«äº¤ææç¹æ°æ®redisç¼å­æææï¼åä½ï¼å¤©
trade.point.expired-days=15

# èµäº§ç»è®¡ç¼å­çæææï¼åä½ï¼åéï¼é»è®¤ï¼5
statistics.params.cache.timeout.minutes=5
#  èµäº§ç»è®¡ç¼å­çå¤§å°ï¼é»è®¤ï¼500æ¡æ°æ®
statistics.params.cache.size=500

dingtalk.webhook.aes=1
dingtalk.webhook.token=2
dingtalk.webhook.corpId=3

kweb.security-method=aliyun
kweb.recaptcha-secret=
kweb.internal-login-ip-white=192.168\.\d{1,3}\.\d{1,3}$
kweb.search-all-type=POST,STORY,CUSTOMER,TOPIC,MEDIA,EVENT


# kcomputeæå¡æ¯å¦å¯ç¨,trueï¼å¯ç¨ï¼falseï¼ä¸å¯ç¨ï¼é»è®¤false
kweb.enable.compute=@kweb.enable.compute@
quest-web.season-start-time=1719763200000