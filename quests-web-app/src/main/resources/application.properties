#debug=true
#for local test use
#spring.profiles.active=local
spring.application.name=quests-web
spring.application.group=quests-web
server.port=@server.port@

#spring default public config
spring.aop.auto=true
spring.output.ansi.enabled=always
spring.main.banner-mode=console
spring.banner.location=classpath:kweb.txt
spring.mvc.servlet.path=/
spring.mvc.servlet.load-on-startup=1
server.servlet.session.timeout=3600
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.force=true
server.servlet.encoding.force-request=true
server.servlet.encoding.force-response=true

# just for kiki
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT

logging.level.root=INFO
#logging.pattern.file=[%p][%t][%d{yyyy-MM-dd HH:mm:ss.SSS}][%c][%L][%X{traceId}][%X{spanId}]%m%n
logging.pattern.console=[%p][%t][%d{yyyy-MM-dd HH:mm:ss.SSS}][%c][%L][%X{traceId}][%X{spanId}]%m%n
#logging.file.path=./logs
#logging.file.name=kweb.log

management.server.port=@management.server.port@
management.health.defaults.enabled=true
management.health.db.enabled=false
management.health.elasticsearch.enabled=false
management.health.mongo.enabled=false
management.endpoints.enabled-by-default=true
management.endpoint.health.show-details=always
management.endpoints.migrate-legacy-ids=true
management.endpoints.web.exposure.include=env,health,info,httptrace,metrics,heapdump,threaddump,prometheus,dubbo
management.endpoints.web.exposure.exclude=members
management.metrics.tags.application=kweb
management.metrics.web.server.request.autotime.enabled=true
management.metrics.web.server.request.autotime.percentiles=0.9, 0.99
management.metrics.web.server.request.autotime.percentiles-histogram=true
management.metrics.dubbo.enabled=true
management.metrics.dubbo.percentiles-histogram=true
management.metrics.dubbo.percentiles=0.9, 0.99
management.metrics.druid.enable=false
spring.config.import=metrics.properties

#springdoc.version=v3
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.swagger-ui.use-root-path=true
springdoc.swagger-ui.display-request-duration=false
springdoc.swagger-ui.groups-order=asc
springdoc.swagger-ui.operations-sorter=method
springdoc.api-docs.version=openapi_3_0
springdoc.show-actuator=false
springdoc.api-docs.groups.enabled=true

springdoc.group-configs[0].group=Customer
springdoc.group-configs[0].paths-to-match=/v2/customer/**,/s2/customer/**
springdoc.group-configs[1].group=Task
springdoc.group-configs[1].paths-to-match=/v2/tasks/**, /v2/reward/**, /s2/tasks/**, /s2/reward/**
springdoc.group-configs[2].group=Member
springdoc.group-configs[2].paths-to-match=/v3/member/**, /v2/member/**, /v1/member/**, /s2/member/**
springdoc.group-configs[3].group=Goods
springdoc.group-configs[3].paths-to-match=/v2/goods/**
springdoc.group-configs[4].group=Lottery
springdoc.group-configs[4].paths-to-match=/v2/lottery/**
springdoc.group-configs[5].group=Question
springdoc.group-configs[5].paths-to-match=/v2/question/**

# close for production
# ????, ?????????
springdoc.api-docs.enabled=@springdoc.api-docs.enabled@
springdoc.swagger-ui.enabled=@springdoc.swagger-ui.enabled@

kiki.web.module=false



