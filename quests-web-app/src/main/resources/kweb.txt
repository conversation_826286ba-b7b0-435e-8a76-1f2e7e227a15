${AnsiColor.BLUE}
 __  ___ ____    __    ____  _______ .______
|  |/  / \   \  /  \  /   / |   ____||   _  \
|  '  /   \   \/    \/   /  |  |__   |  |_)  |
|    <     \            /   |   __|  |   _  <
|  .  \     \    /\    /    |  |____ |  |_)  |
|__|\__\     \__/  \__/     |_______||______/


     _______. _______ .______     ____    ____  __    ______  _______
    /       ||   ____||   _  \    \   \  /   / |  |  /      ||   ____|
   |   (----`|  |__   |  |_)  |    \   \/   /  |  | |  ,----'|  |__
    \   \    |   __|  |      /      \      /   |  | |  |     |   __|
.----)   |   |  |____ |  |\  \----.  \    /    |  | |  `----.|  |____
|_______/    |_______|| _| `._____|   \__/     |__|  \______||_______|

${AnsiColor.BLUE}
Application Name: ${spring.application.name}
Spring Boot Version: ${spring-boot.version}${spring-boot.formatted-version}
