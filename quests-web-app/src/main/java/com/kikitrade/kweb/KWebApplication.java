package com.kikitrade.kweb;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@EnableDubbo
@OpenAPIDefinition(info = @Info(
        title = "Kweb API", version = "1.0",
        description = "The Kweb API v1.0",
        license = @License(
                name = "Apache 2.0",
                url = "https://www.apache.org/licenses/LICENSE-2.0"))
)
@SpringBootApplication(scanBasePackages = {"com.kikitrade.kweb", "com.kikitrade"})
public class KWebApplication {
    public static void main(String[] args) {
        SpringApplication.run(KWebApplication.class, args);
    }
}
