<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.kikitrade</groupId>
    <artifactId>quests-web</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>

  <groupId>com.kikitrade</groupId>
  <artifactId>quests-web-app</artifactId>
  <version>1.0-SNAPSHOT</version>
  <name>quests-web-app</name>

  <properties>
    <java-version>17</java-version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.property.path>..</project.property.path>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
  </properties>

  <dependencies>

    <dependency>
      <groupId>com.kikitrade</groupId>
      <artifactId>quests-module-customer</artifactId>
    </dependency>

    <dependency>
      <groupId>com.kikitrade</groupId>
      <artifactId>quests-module-activity</artifactId>
    </dependency>

    <dependency>
      <groupId>com.kikitrade</groupId>
      <artifactId>quests-module-common</artifactId>
    </dependency>

    <dependency>
      <groupId>com.kikitrade</groupId>
      <artifactId>quests-module-member</artifactId>
    </dependency>

      <dependency>
        <groupId>com.kikitrade</groupId>
        <artifactId>kiki-core</artifactId>
      </dependency>

      <dependency>
        <groupId>com.kikitrade</groupId>
        <artifactId>kiki-observability-metrics-spring-boot-starter</artifactId>
      </dependency>

      <!--        内部依赖 结束   -->

      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
      </dependency>

      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-bootstrap</artifactId>
      </dependency>

      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-1.2-api</artifactId>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jcl</artifactId>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j-impl</artifactId>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-web</artifactId>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
      </dependency>

      <dependency>
        <groupId>org.jboss.netty</groupId>
        <artifactId>netty</artifactId>
      </dependency>

      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo</artifactId>
      </dependency>

      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>com.vaadin.external.google</groupId>
            <artifactId>android-json</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <!-- jwt -->
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
      </dependency>

      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-impl</artifactId>
        <scope>runtime</scope>
      </dependency>
      <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-jackson</artifactId>
        <scope>runtime</scope>
      </dependency>

      <!-- session -->
      <!--        <dependency>-->
      <!--            <groupId>org.springframework.session</groupId>-->
      <!--            <artifactId>spring-session-data-redis</artifactId>-->
      <!--        </dependency>-->

      <dependency>
        <groupId>com.kikitrade</groupId>
        <artifactId>kiki-redis-spring-boot-starter</artifactId>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
      </dependency>

      <dependency>
        <groupId>com.kikitrade</groupId>
        <artifactId>kiki-ons-spring-boot-starter</artifactId>
      </dependency>

      <!--  elastic job -->
      <dependency>
        <groupId>com.kikitrade</groupId>
        <artifactId>kiki-elasticjob-spring-boot-starter</artifactId>
      </dependency>

      <dependency>
        <groupId>com.google.auto.value</groupId>
        <artifactId>auto-value-annotations</artifactId>
        <version>1.7.4</version>
      </dependency>

      <dependency>
        <groupId>com.aliyun</groupId>
        <artifactId>aliyun-java-sdk-core</artifactId>
      </dependency>

      <dependency>
        <groupId>com.aliyun</groupId>
        <artifactId>aliyun-java-sdk-afs</artifactId>
      </dependency>

      <dependency>
        <groupId>com.kikitrade</groupId>
        <artifactId>kiki-dubbo-deployment-tag-aware-spring-boot-starter</artifactId>
      </dependency>

      <dependency>
        <groupId>com.kikitrade</groupId>
        <artifactId>kiki-observability-tracing-spring-boot-starter</artifactId>
      </dependency>

      <dependency>
        <groupId>com.auth0</groupId>
        <artifactId>java-jwt</artifactId>
        <version>3.10.3</version>
      </dependency>

    <dependency>
      <groupId>com.kikitrade</groupId>
      <artifactId>kevent-client</artifactId>
    </dependency>

      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>3.12.0</version>
      </dependency>

      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <scope>provided</scope>
      </dependency>

    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-commons</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-context</artifactId>
    </dependency>
  </dependencies>

  <build>
    <finalName>quests-web</finalName>
    <plugins>
      <plugin>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-maven-plugin</artifactId>
        <version>1.1</version>
        <configuration>
          <apiDocsUrl>http://localhost:8081/v3/api-docs.json</apiDocsUrl>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>

      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${spring.boot.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <includeSystemScope>true</includeSystemScope>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <configuration>
          <skip>false</skip>
        </configuration>
      </plugin>
    </plugins>

  </build>
</project>
