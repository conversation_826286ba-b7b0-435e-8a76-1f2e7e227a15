# Telegram 多机器人系统 - Bean注入方式实现指南

## 概述

本文档说明了如何使用Spring Bean注入的方式实现Telegram多机器人系统，确保Dubbo服务能够正确注入到消息处理服务中。

## 问题背景

在之前的实现中，我们使用`new`关键字直接创建`TrexTelegramMessageService`实例：

```java
// 问题代码 - Dubbo注入不会生效
this.messageService = new TrexTelegramMessageService(projectName, debugMode);
```

这种方式创建的对象不受Spring容器管理，因此`@DubboReference`注解的依赖注入不会生效，导致`remoteAuthService`为null。

## 解决方案

### 1. 架构设计

新的架构使用以下组件：

- **TrexTelegramMessageService** - Spring Bean，支持Dubbo注入
- **TelegramBotFactory** - 机器人工厂Bean，负责创建机器人实例
- **TelegramBotManager** - 机器人管理器，使用工厂创建机器人
- **MultiBotTelegramWebhook** - 控制器，使用@Autowired注入依赖

### 2. 核心实现

#### TrexTelegramMessageService (Spring Bean)

```java
@Service
@Slf4j
public class TrexTelegramMessageService {
    
    @DubboReference
    private RemoteAuthService remoteAuthService; // 现在可以正确注入
    
    private String projectName = "trex";
    private boolean debugMode = false;
    
    public void setProjectConfig(String projectName, boolean debugMode) {
        this.projectName = projectName;
        this.debugMode = debugMode;
    }
    
    // 其他方法...
}
```

#### TelegramBotFactory (工厂Bean)

```java
@Component
@Slf4j
public class TelegramBotFactory {
    
    @Autowired
    private TrexTelegramMessageService trexTelegramMessageService;
    
    public TelegramWebhookBot createBot(String projectName, 
                                       TelegramBotConfig.BotInfo botInfo, 
                                       boolean debugMode) {
        switch (projectName.toLowerCase()) {
            case "trex":
                return createTrexBot(projectName, botInfo, debugMode);
            default:
                return createTrexBot(projectName, botInfo, debugMode);
        }
    }
    
    private TelegramWebhookBot createTrexBot(String projectName, 
                                           TelegramBotConfig.BotInfo botInfo, 
                                           boolean debugMode) {
        // 设置消息服务的项目配置
        trexTelegramMessageService.setProjectConfig(projectName, debugMode);
        
        // 创建机器人实例，注入Spring管理的消息服务
        return new TrexTelegramBot(projectName, botInfo, trexTelegramMessageService);
    }
}
```

#### TelegramBotManager (使用工厂)

```java
@Component
@Slf4j
public class TelegramBotManager {
    
    @Autowired
    private TelegramBotConfig telegramBotConfig;
    
    @Autowired
    private TelegramBotFactory telegramBotFactory;
    
    private TelegramWebhookBot createBotInstance(String projectName, 
                                               TelegramBotConfig.BotInfo botInfo) {
        return telegramBotFactory.createBot(projectName, botInfo, telegramBotConfig.isDebugMode());
    }
}
```

### 3. 依赖注入流程

1. **Spring容器启动**
   - 创建`TrexTelegramMessageService` Bean
   - 注入`RemoteAuthService`等Dubbo服务

2. **机器人初始化**
   - `TelegramBotManager`调用`TelegramBotFactory`
   - 工厂使用已注入的`TrexTelegramMessageService` Bean
   - 创建`TrexTelegramBot`实例，传入Spring管理的消息服务

3. **消息处理**
   - Webhook请求到达时，机器人调用消息服务
   - 消息服务中的Dubbo服务已正确注入，可以正常使用

## 配置示例

### application.properties

```properties
# Telegram 多机器人配置
telegram.debug-mode=true

# Trex项目机器人配置
telegram.bots.trex.bot-username=TrexBot
telegram.bots.trex.bot-token=YOUR_BOT_TOKEN
telegram.bots.trex.webhook-path=/tg-webhook-trex
telegram.bots.trex.description=Trex项目专用机器人
telegram.bots.trex.enabled=true
```

### Dubbo服务使用示例

```java
@Service
@Slf4j
public class TrexTelegramMessageService {
    
    @DubboReference
    private RemoteAuthService remoteAuthService;
    
    private BotApiMethod<?> handleStartCommand(Message message, String args) {
        log.info("[{}] 处理start命令 - 参数: {}", projectName, args);
        
        AuthRequest authRequest = new AuthRequest();
        authRequest.setSaasId("trex");
        authRequest.setPlatform("telegram");
        authRequest.setCustomerId(args);
        authRequest.setSocialId(message.getFrom().getId().toString());
        authRequest.setSocialName(message.getFrom().getFirstName() + " " + message.getFrom().getLastName());
        
        try {
            log.info("[{}] authRequest:{}", projectName, authRequest);
            remoteAuthService.auth(authRequest); // 现在可以正常调用
        } catch (Exception e) {
            log.error("[{}] authRequest error:{}", projectName, authRequest, e);
        }
        
        return null;
    }
}
```

## 优势

### 1. 正确的依赖注入
- Dubbo服务能够正确注入到消息处理服务中
- 支持Spring的所有依赖注入功能

### 2. 更好的可测试性
- 可以使用Spring Test框架进行单元测试
- 可以Mock Dubbo服务进行测试

### 3. 更好的扩展性
- 可以轻松添加新的项目机器人
- 每个项目可以有独立的消息处理服务

### 4. 生命周期管理
- Spring容器管理Bean的生命周期
- 支持@PostConstruct、@PreDestroy等注解

## 注意事项

### 1. 单例问题
由于`TrexTelegramMessageService`是单例Bean，多个项目共享同一个实例。通过`setProjectConfig`方法设置项目配置，但在并发情况下可能有线程安全问题。

**解决方案**：
- 将项目配置作为方法参数传递，而不是存储在实例变量中
- 或者为每个项目创建独立的消息处理服务Bean

### 2. 配置更新
项目配置通过`setProjectConfig`方法设置，需要确保在处理消息前正确设置。

### 3. 错误处理
确保Dubbo服务调用的异常处理，避免影响整个机器人系统。

## 未来改进

### 1. 项目专用服务
为每个项目创建独立的消息处理服务：

```java
@Service("trexTelegramMessageService")
public class TrexTelegramMessageService { ... }

@Service("vibraTelegramMessageService") 
public class VibraTelegramMessageService { ... }
```

### 2. 配置驱动
通过配置文件动态创建不同项目的机器人和服务。

### 3. 监控和指标
添加机器人运行状态监控和消息处理指标。
