package com.kikitrade.kweb.interceptor;

import com.kikitrade.kweb.constants.WebConstants;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.model.common.WebResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.Base64;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/24 11:32
 */
@Component
@Slf4j
public class QuestsAuthInterceptor extends AbstractInterceptor{

    private final String ISSUER = "quests";
    private final String ISSUED_AT = "1716521918";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String saasId = request.getHeader(WebConstants.SAAS_ID);
        String bearer = request.getHeader(WebConstants.AUTHORIZATION);

        String s1 = new String(Base64.getDecoder().decode(bearer.getBytes()));
        String[] arr = s1.split(":");
        long now = System.currentTimeMillis();
        if(now - Long.parseLong(arr[0]) > 1000 * 60){
            log.warn("bearer not match:{},{}", saasId, arr);
            interceptRespond(response, WebResponse.result(WebResponseEnum.AUTH_CODE_INVALID).toJSONString());
            return false;
        }
        BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
        boolean match = bCryptPasswordEncoder.matches(String.format("%s:%s:%s", saasId, arr[0], ISSUER), s1.substring(s1.indexOf(":") + 1));
        if(!match){
            log.warn("bearer not match:{},{}", saasId, bearer);
            interceptRespond(response, WebResponse.result(WebResponseEnum.AUTH_CODE_INVALID).toJSONString());
            return false;
        }
        return true;
    }
}
