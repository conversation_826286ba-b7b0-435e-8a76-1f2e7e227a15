package com.kikitrade.kweb.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import com.kikitrade.kweb.constants.WebConstants;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.model.common.WebResponse;
import com.kikitrade.kweb.utils.IpUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/08/02 11:18
 */
@Slf4j
@Component
public class QuestsLoginInterceptor extends AbstractInterceptor {

    @DubboReference
    private RemoteCustomerService remoteCustomerService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        log.info("QuestsLoginInterceptor request urL[{}] ,request ip[{}] ,request params[{}] ...", request.getRequestURI(), IpUtil.getRemoteIP(request), JSONObject.toJSONString(request.getParameterMap()));
        CustomerBindDTO customer = CustomerHolder.qcustomer();
        if (customer != null) {
            try {
                String saasId = customer.getSaasId();
                if ("opensocial".equals(saasId) && StringUtils.isNotBlank(customer.getTwitterName())) {
                    //记录活跃用户信息
                    remoteCustomerService.recordActiveCustomer(saasId, customer.getTwitterName());
                }
            } catch (Exception e) {
                log.error("recordActiveCustomer error", e);
            }
            return true;
        }

        interceptRespond(response, WebResponse.result(WebResponseEnum.CUSTOMER_LOGIN_3RD_TOKEN_ERROR).toJSONString());
        return false;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        CustomerHolder.unbind();
        log.info("LoginInterceptor afterCompletion");
    }

}

