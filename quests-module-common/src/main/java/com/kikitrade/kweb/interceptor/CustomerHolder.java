package com.kikitrade.kweb.interceptor;

import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kweb.model.customer.CustomerLoginContext;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/07/30 18:00
 */
public class CustomerHolder {

    private static final ThreadLocal<CustomerLoginContext> holder = new ThreadLocal<CustomerLoginContext>();

    public static void bind(CustomerLoginContext context) {
        if (null == context) {
            return;
        }
        holder.set(context);
    }

    public static CustomerLoginContext customerContext() {
        return holder.get();
    }

    public static <T extends CustomerDTO> T customer() {
        CustomerLoginContext context = holder.get();
        return context != null ? (T) context.getCustomer() : null;
    }

    public static <K extends CustomerBindDTO> K qcustomer() {
        CustomerLoginContext context = holder.get();
        return context != null ? (K) context.getQcustomer() : null;
    }

    public static void unbind() {
        holder.remove();
    }
}
