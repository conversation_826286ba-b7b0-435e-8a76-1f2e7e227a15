package com.kikitrade.kweb.interceptor;

import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.kikitrade.kweb.config.QuestWebProperties;
import com.kikitrade.kweb.constants.WebConstants;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.model.common.WebResponse;
import com.kikitrade.kweb.model.customer.QCustomer;
import com.kikitrade.kweb.service.KwebBaseService;
import com.kikitrade.kweb.utils.IpUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/**
 * JwtTokenInterceptor
 *
 * <AUTHOR>
 * @create 2021/10/28 6:34 下午
 * @modify
 */
@Slf4j
@Component
public class QuestsJwtTokenInterceptor extends AbstractInterceptor {

    @Resource
    private KwebBaseService kwebBaseService;
    @Resource
    private QuestWebProperties questWebProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        try{
            String jwtToken = request.getHeader(WebConstants.JWT_TOKEN);
            String saasId = request.getHeader(WebConstants.SAAS_ID);
            String idToken = request.getHeader(WebConstants.ID_TOKEN);
            String publicKey = request.getHeader(WebConstants.PUBLIC_KEY);

            MDC.put("customerId", "not login");

            if(StringUtils.isNotBlank(idToken)){
                JWT jwt = JWTUtil.parseToken(idToken);
                log.info("QuestsJwtTokenInterceptor request urL[{}] ,request ip[{}] ,saasId[{}], idToken[{}],idTokenBody[{}], publicKey[{}]]", request.getRequestURI(), IpUtil.getRemoteIP(request),
                        saasId, idToken, jwt.getPayloads(), publicKey);
            }else{
                log.info("QuestsJwtTokenInterceptor request urL[{}] ,request ip[{}] ,jwt[{}], saasId[{}], publicKey[{}]]", request.getRequestURI(), IpUtil.getRemoteIP(request),
                        jwtToken, saasId, publicKey);
            }
            QCustomer customer = null;
            if (StringUtils.isNotBlank(idToken) && !"undefined".equals(idToken) && questWebProperties.getTWebCustomerIdtoken().get(saasId) != null
                    && questWebProperties.getTWebCustomerIdtoken().get(saasId).equals("inner")) {
                customer = kwebBaseService.getByIdInner(saasId, idToken, publicKey);
            }else if(StringUtils.isNotBlank(idToken) && !"undefined".equals(idToken) && questWebProperties.getTWebCustomerIdtoken().get(saasId) != null){
                customer = kwebBaseService.getByIdToken(saasId, idToken, publicKey);
            }else if(StringUtils.isNotBlank(jwtToken) && !"undefined".equals(jwtToken) && questWebProperties.getTWebCustomerJwt().get(saasId) != null){
                customer = kwebBaseService.getByJwt(saasId, jwtToken);
            }
            if(customer != null){
                String ip = IpUtil.getRemoteIP(request);
                ip = ip.split(",")[0];
                kwebBaseService.storeToContext(request, jwtToken, idToken, publicKey ,customer, ip);
            }
            return true;
        }catch (Exception ex){
            log.error("JwtTokenInterceptor exception", ex);
            interceptRespond(response, WebResponse.result(WebResponseEnum.PARAMS_JWT_CHECK_INVALID).toJSONString());
            return false;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        CustomerHolder.unbind();
        log.info("JwtTokenInterceptor afterCompletion");
    }
}

