package com.kikitrade.kweb.interceptor;

import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import com.kikitrade.kweb.constants.WebConstants;
import com.kikitrade.kweb.model.common.BusiContext;
import com.kikitrade.kweb.model.common.SaasContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class QuestsSaasInterceptor extends AbstractInterceptor {


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        log.info("QuestsSaasInterceptor handle saas id {}", request.getHeader(WebConstants.SAAS_ID));
        // fetch it from session first, then request
        String saasId = request.getHeader(WebConstants.SAAS_ID);

        request.setAttribute(WebConstants.SAAS_ID, saasId);
        SaasContext.setDubboSaasId(saasId);
        SaasContext.SAAS_HOLDER.set(saasId);

        SaasContext.setRpcContext(request);

        CustomerBindDTO customer = CustomerHolder.qcustomer();
        if (customer != null) {
            BusiContext busiContext = new BusiContext();
            busiContext.setCustomerId(customer.getUid());
            SaasContext.setBusiContext(busiContext);
            SaasContext.BUSI_CONTEXT.set(busiContext);

        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        String saasId = (String) request.getAttribute(WebConstants.SAAS_ID);
        if (StringUtils.isNotBlank(saasId)) {
            response.setHeader(WebConstants.SAAS_ID, saasId);
        }

        SaasContext.SAAS_HOLDER.remove();
        SaasContext.BUSI_CONTEXT.remove();
    }
}
