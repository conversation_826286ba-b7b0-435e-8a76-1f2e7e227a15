package com.kikitrade.kweb.constants;

public interface RedisKeyConstants {

    //最后24小时的价格列表
    String LAST_24H_END_PRICE = "last24HEndPrice";

    String KEY_CUSTOMER_LOGOUT_TOKEN = "LOGOUT:TOKEN:";

    String CUSTOMER_REFRESH_TOKEN = "CUSTOMER:REFRESH:TOKEN:";

    String USER_OPERATION = "USER";

    // 客户当前资产统计数据
    String STATISTICS_PARAMS = "STATISTICS:PARAMS:";
    String STATISTICS_TIMESTAMP = "STATISTICS:TIMESTAMP"; // 存储上面的key值，减少按照时间戳扫描的时耗

    // 行情全局通知用户集合
    String GLOBAL_MARKET_USER = "GLB:MKT:USER";

    // 每小时的成本价
    String COST_PRICE = "COST_PRICE:";
    String COST_PRICE_TIMESTAMP = "COST_PRICE_TIMESTAMP"; // 存储上面的key值，减少按照时间戳扫描的时耗
}
