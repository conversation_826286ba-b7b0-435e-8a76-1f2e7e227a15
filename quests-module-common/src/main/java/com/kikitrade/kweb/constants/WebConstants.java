package com.kikitrade.kweb.constants;

import java.util.Arrays;

public class WebConstants {

    public static final String JWT_TOKEN = "JWT_TOKEN";
    public static final String REFRESH_JWT_TOKEN = "REFRESH_JWT_TOKEN";

    public static final String COMMA = ",";
    public static final String UNDERLINE = "_";

    /**
     * header
     */
    public static final String SAAS_ID = "saas_id";
    public static final String DEVICE_ID = "device_id";
    public static final String DEVICE_TYPE = "device_type";
    public static final String CUSTOMER_ID = "customer_id";
    public static final String VERSION = "version";
    public static final String RISK_TOKEN = "risk_token";
    public static final String DEVICE_TOKEN = "device_token";
    public static final String AUTHORIZATION = "Authorization";

    public static final String AUTHORIZATION_PREFIX = "Bearer ";

    public static final String CUSTOMER = "Customer";
    public static final String WS_KEY = "wsKey";
    public static final String JWT_TOKEN_KEY = "jwtKey";
    public static final String TOKEN_ID = "tokenId";
    public static final String BUSI_CONTEXT = "BUSI_CONTEXT";
    public static final String FIAT = "fiat";
    public static final String CRYPTO = "crypto";
    public static final String MARKET_PUSH = "market_push";
    public static final String ID_TOKEN = "idToken";
    public static final String PUBLIC_KEY = "publicKey";
    public static final String USER_ID = "userId";

    public enum SaasId {
        kiki,
        vibra;

        public static SaasId get(String name) {
            return Arrays.stream(values()).filter(f -> f.name().equalsIgnoreCase(name)).findFirst().orElse(kiki);
        }

    }


    public enum CacheRefreshType {
        customer,
        sysConfig,
        memberLadder,
        assetStatistics;

        public static CacheRefreshType get(String name) {
            return Arrays.stream(values()).filter(f -> f.name().equalsIgnoreCase(name)).findFirst().orElse(customer);
        }
    }

}
