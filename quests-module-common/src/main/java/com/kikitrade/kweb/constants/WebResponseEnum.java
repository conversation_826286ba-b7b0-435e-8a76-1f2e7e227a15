package com.kikitrade.kweb.constants;

/**
 * <AUTHOR>
 * @create 2021/7/30 4:03 下午
 * @modify
 */
public enum WebResponseEnum {
    // 0 开头的统一为成功代码
    SUCCESS("0000", "success", true),
    // 00 开头，系统相关成功代码
    SYSTEM_VERIFY_CLOSE("0001", "success", true), //系统未开启二次验证 301
    SYSTEM_VERIFY_OPEN_AND_OK("0002", "success", true), //系统开启二次验证，并且绑定了手机号 302
    SYSTEM_VERIFY_OPEN_NEED_PHONE("0003", "customer.verify.phone.unbind", true), //系统开启二次验证，但是需要绑定手机 303
    // 1000 开始，用户相关错误代码
    CUSTOMER_LOGIN_REQUIRED("1000", "customer.login.required"),
    CUSTOMER_NEW_LOGIN("1043", "customer.login.new"),

    // 01 开头，用户相关成功代码
    CUSTOMER_REGISTRATION_SUCCESS("0100", "customer.registration.success", true),
    CUSTOMER_PHONE_SUCCESS("0101", "customer.phone.success", true),
    CUSTOMER_VERIFY_TWICE_GOOGLE("0102", "customer.verify.twice", true),
    CUSTOMER_VERIFY_TWICE_PHONE("0103", "customer.verify.twice", true),
    CUSTOMER_VERIFY_TWICE_GOOGLE_PHONE("0104", "customer.verify.twice", true),
    CUSTOMER_VERIFY_CODE_SENT("0105", "customer.verify.code.sent", true),
    CUSTOMER_VERIFY_EMAIL_INACTIVE("0106", "customer.verify.email.inactive", true),
    CUSTOMER_VERIFY_INACTIVE("0107", "customer.verify.inactive", true),
    CUSTOMER_GOOGLE_ON("0108", "customer.google.on", true),
    CUSTOMER_GOOGLE_OFF("0109", "customer.google.off", true),
    CUSTOMER_LOGIN_SUCCESS("0110", "customer.login.success", true),
    CUSTOMER_GOOGLE_SUCCESS("0111", "customer.google.success", true),
    CUSTOMER_GOOGLE_UNBIND_SUCCESS("0112", "customer.google.unbind.success", true),
    CUSTOMER_PHONE_ON_SUCCESS("0113", "customer.phone.on.success", true),
    CUSTOMER_PHONE_OFF_SUCCESS("0114", "customer.phone.off.success", true),
    CUSTOMER_PASSWORD_RESET_SUCCESS("0115", "customer.password.reset.success", true),
    CUSTOMER_GOOGLE_BIND_SUCCESS("0116", "customer.google.bind.success", true),
    CUSTOMER_QUERY_SUCCESS("0117", "customer.query.success", true),
    CUSTOMER_VERIFY_CODE_SENT_OFTEN("0118", "customer.verify.code.sent.often", true),
    CUSTOMER_VERIFY_TWICE_GOOGLE_MAIL("0119", "customer.phone.verify.twice", true),
    CUSTOMER_VERIFY_TWICE_MAIL("0120", "customer.mail.verify.twice", true),
    CUSTOMER_REFRESH_TOKEN_EXPIRED("0121", "customer.refresh.token.expired", false),
    INCORRECT_INVITATION_CODE("4000", "incorrect.invitation.code", true),

    /***********************
     * ERROR Message
     **********************/

    // 1000 开始，用户相关错误代码
    CUSTOMER_EMAIL_REQUIRED("1001", "customer.email.required"),
    CUSTOMER_EMAIL_FORMAT_INCORRECT("1002", "customer.email.format.incorrect"),
    CUSTOMER_PASSWORD_REQUIRED("1003", "customer.password.required"),
    CUSTOMER_PASSWORD_DIFFERENT("1004", "customer.password.different"),
    CUSTOMER_PASSWORD_OLD_REQUIRED("1005", "customer.password.old.required"),
    CUSTOMER_PASSWORD_NEW_REQUIRED("1006", "customer.password.new.required"),
    CUSTOMER_PASSWORD_SAME("1007", "customer.password.same"),
    CUSTOMER_PHONE_CODE_REQUIRED("1008", "customer.phone.code.required"),
    CUSTOMER_PHONE_CODE_INCORRECT("1009", "customer.phone.code.incorrect"),
    CUSTOMER_PHONE_SEND_FAIL("1010", "customer.phone.send.fail"),
    CUSTOMER_PHONE_FORMAT_INCORRECT("1011", "customer.phone.format.incorrect"),
    CUSTOMER_PHONE_REQUIRED("1012", "customer.phone.required"),
    CUSTOMER_PHONE_CODE_LIMIT("1013", "customer.phone.code.limit"),
    CUSTOMER_PHONE_CODE_OFTEN("1014", "customer.phone.code.often"),
    CUSTOMER_PHONE_DUPLICATE("1015", "customer.phone.duplicate"),
    CUSTOMER_PHONE_NEW_DUPLICATE("1016", "customer.phone.new.duplicate"),
    CUSTOMER_PHONE_ERROR("1017", "customer.phone.error"),
    CUSTOMER_VERIFY_PASSWORD_FREEZE("1132", "customer.verify.password.freeze"),

    CUSTOMER_VERIFY_CODE_EXPIRE("1018", "customer.verify.code.expire"),
    CUSTOMER_VERIFY_PHONE_UNBIND("1019", "customer.verify.phone.unbind"),
    CUSTOMER_EXIST_NONE("1020", "customer.exist.none"),
    CUSTOMER_FORBIDDEN("1021", "customer.forbidden"),
    CUSTOMER_LOCKED("1022", "customer.locked"),
    CUSTOMER_REG_EMAIL_DUPLICATE("1023", "customer.reg.email.duplicate"),
    CUSTOMER_REG_FAIL("1024", "customer.reg.fail"),

    CUSTOMER_GOOGLE_OFF_ERROR("1025", "customer.google.off.error"),
    CUSTOMER_GOOGLE_ON_ERROR("1026", "customer.google.on.error"),
    CUSTOMER_GOOGLE_AUTH_FAIL("1027", "customer.google.auth.fail"),
    CUSTOMER_GOOGLE_BIND_FAIL("1028", "customer.google.bind.fail"),
    CUSTOMER_GOOGLE_UNBIND_FAIL("1029", "customer.google.unbind.fail"),

    CUSTOMER_KYC_ID_USED("1030", "customer.kyc.id.used"),
    CUSTOMER_KYC_DUPLICATE("1031", "customer.kyc.duplicate"),
    CUSTOMER_KYC_EXPIRE("1032", "customer.kyc.expire"),
    CUSTOMER_KYC_INFO_INSUFFICIENT("1033", "customer.kyc.info.insufficient"),
    //    CUSTOMER_KYC_REJECT_DUPLICATE_ID("1034", "customer.kyc.reject.duplicate.id"),
    CUSTOMER_KYC_REJECT_IMAGE_NOT_READABLE("1035", "customer.kyc.reject.image.not.readable"),
    CUSTOMER_KYC_REJECT_UNSUPPORTED_ID_COUNTRY("1036", "customer.kyc.reject.unsupported.id.country"),
    CUSTOMER_KYC_REJECT_HANDHELD_FRAUD("1037", "customer.kyc.reject.handheld.fraud"),
    CUSTOMER_KYC_SOURCE_INVALID("1038", "customer.kyc.source.invalid"),
    CUSTOMER_KYC_MORE_THAN_USAGE("1039", "customer.kyc.more.than.usage"),
    CUSTOMER_KYC_NOT_SUBMIT("1040", "customer.kyc.not.submit"),
    CUSTOMER_KYC1_NOT_SUBMIT("1041", "customer.kyc1.not.submit"),
    CUSTOMER_KYC_CREAT_CHECK_LIMIT("1142", "customer.kyc.check.limit"),
    CUSTOMER_KYC_APP_VERSION_TOO_OLD("1131", "customer.kyc.app.version.too.old"),

    CUSTOMER_KYC_IDCARD_INFO_IN_VALID("1066", "customer.kyc.id.expire"),//身份证已过期
    CUSTOMER_KYC_LIVENESS_RESULT_FAIL("1067", "customer.kyc.live.result.check.fail"),//活体验证失败
    CUSTOMER_KYC_NO_SUCH_ID_NUMBER("1068", "customer.kyc.id.not.exist"),//身份证号码不存在
    CUSTOMER_KYC_INVALID_IDCARD_NUMBER("1069", "customer.kyc.id.invalid"),//无效的身份证号码
    CUSTOMER_KYC_ID_NUMBER_NAME_NOT_MATCH("1070", "customer.kyc.id.name.not.match"),//身份证号码与姓名不匹配
    CUSTOMER_KYC_VERIFY_ERROR("1071", "customer.kyc.verify.fail"),//未能通过证件与活体的比对审核

    CUSTOMER_KYC_VERIFY_PROCESSING("1500", "customer.kyc.verify.processing"),//kyc 处理中/认证中
    CUSTOMER_KYC_REJECT_UNSUPPORTED_ID_TYPE("1501", "customer.kyc.reject.unsupported.id.type"),

    CUSTOMER_PHONE_ON_FAIL("1040", "customer.phone.on.fail"),
    CUSTOMER_PHONE_OFF_FAIL("1041", "customer.phone.off.fail"),

    CUSTOMER_PASSWORD_RESET_FAIL("1042", "customer.password.reset.fail"),
    CUSTOMER_VIRTUAL_ACCOUNT_NONEXIST("1044", "customer.virtual.account.nonexist"),
    CUSTOMER_VERIFY_CODE_INCORRECT("1045", "customer.verify.code.incorrect"),
    CUSTOMER_VERIFY_TYPE_INVALID("1046", "customer.verify.type.invalid"),
    CUSTOMER_GOOGLE_VERIFY_CODE_INCORRECT("1047", "customer.google.verify.code.incorrect"),
    CUSTOMER_PASSWORD_FORMAT_INCORRECT("1048", "customer.password.format.incorrect"),
    CUSTOMER_EMAIL_OR_PHONE_REQUIRED("1049", "customer.email.or.phone.required"),
    CUSTOMER_VERIFY_EMAIL_UNBIND("1050", "customer.verify.email.unbind"),
    CUSTOMER_VERIFY_EMAIL_OR_PHONE_UNBIND("1051", "customer.verify.email.or.phone.unbind"),
    CUSTOMER_MAIL_CODE_REQUIRED("1055", "customer.email.code.required"),
    CUSTOMER_MAIL_DUPLICATE("1056", "customer.email.duplicate"),
    CUSTOMER_MAIL_CODE_INCORRECT("1057", "customer.email.code.incorrect"),

    CUSTOMER_MAIL_BIND_SUCCESS("1058", "customer.email.bind.success", true),
    CUSTOMER_MAIL_UNBIND_SUCCESS("1059", "customer.email.unbind.success", true),
    CUSTOMER_PHONE_UNBIND_SUCCESS("1060", "customer.phone.unbind.success", true),
    CUSTOMER_MAIL_VERIFY_SUCCESS("1061", "customer.email.verify.success", true),
    CUSTOMER_MAIL_UNBIND_FAIL("1062", "customer.email.unbind.fail", false),
    CUSTOMER_PHONE_UNBIND_FAIL("1063", "customer.phone.unbind.fail", false),
    CUSTOMER_MAIL_UNBIND_NOT_ALLOWED("1064", "customer.email.unbind.not.allowed", false),
    CUSTOMER_PHONE_UNBIND_NOT_ALLOWED("1065", "customer.phone.unbind.not.allowed", false),
    CUSTOMER_REFERENCE_REQUIRED("1066", "customer.reference.required"),
    CUSTOMER_API_INVALID_SIGNATURE("1067", "customer.invalid.signature"),
    CUSTOMER_PHONE_BIND_SUCCESS("1168", "customer.phone.bind.success", true),

    CUSTOMER_LOGIN_PARAMETER_INCORRECT("1099", "customer.login.incorrect"),
    CUSTOMER_LOGIN_INCORRECT("1100", "customer.login.incorrect"),
    CUSTOMER_LOGIN_PASSWORD_ERROR("1101", "customer.login.password.error"),
    CUSTOMER_RESET_PASSWORD_ERROR("1102", "customer.reset.password.error"),
    CUSTOMER_LOGIN_EMAIL_PASSWORD_ERROR("1103", "customer.login.password.error.email"),
    CUSTOMER_LOGIN_PHONE_PASSWORD_ERROR("1104", "customer.login.password.error.mobile"),

    CUSTOMER_LOGIN_3RD_TOKEN_ERROR("1105", "customer.login.3rd.token.error"),
    CUSTOMER_LOGIN_3RD_EMAIL_NOT_BIND("1106", "customer.login.3rd.email.not.bind"),
    CUSTOMER_LOGIN_3RD_EMAIL_EXISTED("1107", "customer.login.3rd.email.exist"), //open auth 的邮箱，kiki 已经注册
    CUSTOMER_LOGIN_3RD_FAIL("1108", "customer.login.3rd.fail"),
    CUSTOMER_LOGIN_3RD_BIND_FAIL("1112", "customer.login.3rd.bind.fail"),
    CUSTOMER_BIND_WALLET_ADDRESS_FAIL("1113", "customer.3rd.bind.wallet.fail"),

    CUSTOMER_QUERY_INVITE_FAIL("1200", "customer.query.invite.fail"),
    CUSTOMER_INVITE_BIND_FAIL("1201", "customer.invite.bind.fail"),

    COIN_CODE_NONEXIST("1301", "coin.code.nonexist", false),
    SYMBOL_NONEXIST("1302", "symbol.nonexist", false),

    ACTIVITY_OFFLINE("1401", "activity.offline", false),
    ACTIVITY_INVALID_PARAMETER("1402", "activity.parameter.invalid", false),
    QUESTS_LADDER_CALCULATING("1403", "quests.ladder.calculating", false),
    QUESTS_SEASON_SETTLING("1404", "quests.season.settling", false),

    QUESTS_QUESTION_ACQUIRE_FAIL("1451", "quests.question.acquire.fail", false),
    QUESTS_QUESTION_SUBMIT_FAIL("1452", "quests.question.submit.fail", false),

    COIN_INVALID_PARAMETER("2019", "coin.invalid.parameter"),
    COIN_AMOUNT_KEEP_DECIMAL_ERROR("2020", "coin.amount.keep.decimal.error"),
    COIN_ADDRESS_OUTER_REQUIRED("2011", "coin.address.outer.required"),
    COIN_ADDRESS_OUTER_MISMATCH("2012", "coin.address.outer.mismatch"),
    ACCOUNT_INVALID_CATEGORY("2013", "account.invalid.category"),

    CUSTOMER_INSUFFICIENT_BALANCE("4004", "customer.insufficient.balance", false),

    OTC_ORDER_INVALID_PARAMETER("5038", "otc.order.invalid.parameter", false),

    PAYMENT_INVALID_PARAMETER("6001", "payment.term.invalid.parameter", false),

    //vpay c2c notify unvibra
    NOTIFY_THIRDPARTY_RECEIVE_FAILED("6002", "notify.thirdparty.receive.failed" ,false),
    NOTIFY_THIRDPARTY_RECEIVE_SUCCESS("6003", "notify.thirdparty.receive.success" ,true),
    TRANSFER_BUSINESS_FAILED("6004" ,"transfer.business.failed" ,true),

    P2P_APPLY_DUPLICATE("7206", "p2p.apply.duplicate", false),
    P2P_INVALID_OPERATION("7207", "p2p.invalid.operation", false),
    P2P_INVALID_PARAMETER("7208", "p2p.invalid.parameter", false),
    P2P_ORDERMAX_CANNOT_LESS_THAN_ORDERMIN("7210", "p2p.ordermax.cannot.less.than.ordermin", false),
    P2P_AMOUNT_CANNOT_LESS_THAN_ORDERMAX("7211", "p2p.amount.cannot.less.than.ordermax", false),
    P2P_ONLY_MERCHANT_CAN_OPERATE("7216", "p2p.only.merchants.can.operate", false),
    P2P_OPERATION_WITHOUT_PERMISSION("7225", "p2p.operation.without.permission", false),
    P2P_PARAMETER_LENGTH_INVALID("7299", "p2p.parameter.length.invalid", false),


    CUSTOMER_PAYSTACK_KYC_UPDATE_BVN_ERROR("7301", "customer.paystack.kyc.update_bvn_fail"),
    CUSTOMER_PAYSTACK_KYC_BVN_AREA_NULL("7302", "customer.paystack.kyc.bvn.area.null", false),
    CUSTOMER_PAYSTACK_KYC_UPDATE_BANKNUM_ERROR("7303", "customer.paystack.kyc.update_bankNum_fail"),
    CUSTOMER_PAYSTACK_KYC_BVN_VERIFY_ERROR("7304", "customer.paystack.kyc.bvn.verify.fail"),//未能通过 BVN 认证
    CUSTOMER_PAYSTACK_KYC_BANK_CARD_NUM_VERIFY_ERROR("7305", "customer.paystack.kyc.bank.card.num.verify.fail"),//未能通过银行卡号认证
    CUSTOMER_PAYSTACK_KYC_BANK_NOT_FOUND_ERROR("7306", "customer.paystack.kyc.bank.not.found"),
    SHARE_ORDER_INVALID_PARAM("7363", "share.order.status.parameter.invalid", false),

    COIN_ADDRESS_SAVE_SUCCESS("0200", "coin.address.save.success", true),
    COIN_WITHDRAW_SUCCESS("0201", "coin.withdraw.success", true),

    CANCEL_WITHDRAW_SUCCESS("0202","cancel.withdraw.success",true),

    DING_TALK_PROCESS_NOT_EXIST("0203","dingtalk.process.not.exist",false),
    DING_TALK_PROCESS_CANCEL_FAIL("0204","dingtalk.process.cancel.fail",false),
    DING_TALK_PROCESS_NOT_CANCEL("0205","try.again.later",false),
    DING_TALK_PROCESS_FINISHED("0206","dingtalk.process.already.finished",false),
    DING_TALK_PROCESS_NOT_RECEIVED("0207","not.received.try.again.later",false),

    QUICK_PAY_ORDER_INVALID_PARAMETER("7400","quick.pay.order.invalid.parameter",false),

    USER_OPERATE_FREQUENT("8000","user.operate.frequent",false),


    // 9000 开始，系统相关错误代码
    SYSTEM_OPERATION_FREQUENT("9000", "system.operation.frequent"),

    SYSTEM_SECURITY_PARAM_REQUIRED("9001", "system.security.param.required"),

    SYSTEM_SECURITY_CHECK_FAIL("9002", "system.security.check.fail"),

    SYSTEM_PARAMETER_INVALID("9003", "system.parameter.invalid"),

    SYSTEM_PARAMETER_REQUIRED("9004", "system.parameter.required"),

    SYSTEM_PARAMETER_FORMAT_INCORRECT("9005", "system.parameter.format.incorrect"),

    SYSTEM_PARAMETER_TYPE_MISMATCH("9006", "system.parameter.type.mismatch"),

    SYSTEM_PARAMETER_NEED_DIGITAL("9007", "system.parameter.digital.required"),

    SYSTEM_DATA_NOT_FOUND("9007", "system.data.not.found"),

    SYSTEM_CACHE_REFRESH_FAIL("9008", "system.cache.refresh.fail"),
    /**
     * 接口下线
     */
    SYSTEM_ACCESS_INTERFACE_OFFLINE("9009", "system.access.interface.offline", false),
    /**
     * 需要升级版本
     */
    SYSTEM_UPGRADE_VERSION("9010", "system.upgrade.version", false),
    /**
     * 非内测访问
     */
    SYSTEM_ACCESS_RESTRICTED("9011", "system.access.restricted", false),

    COMPLIANCE_KYC1_REQUIRED("9012", "compliance.kyc1.required", false),
    COMPLIANCE_KYC1_AUDITING("9013", "compliance.kyc1.auditing", false),
    COMPLIANCE_KYC1_REJECTED("9014", "compliance.kyc1.rejected", false),
    COMPLIANCE_KYC2_REQUIRED("9015", "compliance.kyc2.required", false),
    COMPLIANCE_KYC2_AUDITING("9016", "compliance.kyc2.auditing", false),
    COMPLIANCE_KYC2_REJECTED("9017", "compliance.kyc2.rejected", false),
    COMPLIANCE_ASSESSMENT_REQUIRED("9018", "compliance.assessment.required", false),

    SYSTEM_HEADER_DEVICE_ID_REQUIRED("9019", "system.header.deviceId.required", false),
    SYSTEM_HEADER_DEVICE_TYPE_REQUIRED("9020", "system.header.deviceType.required", false),
    SYSTEM_HEADER_VERSION_REQUIRED("9021", "system.header.version.required", false),

    API_RATE_LIMITING("9022", "api.rate.limiting", false),

    PARAMS_JWT_DECRYPT_FAIL("9100", "params.jwt.decrypt.fail", false),
    PARAMS_JWT_CHECK_INVALID("9101", "params.jwt.check.invalid", false),

    SYSTEM_CONFIG_ERROR("9994", "system.config.error", false),
    SYSTEM_P2P_UNAVAILABLE("9995", "system.p2p.unavailable", false),
    SYSTEM_TRADE_UNAVAILABLE("9996", "system.trade.unavailable", false),
    SYSTEM_WITHDRAW_UNAVAILABLE("9997", "system.withdraw.unavailable", false),
    SYSTEM_UNAVAILABLE("9998", "system.unavailable", false),
    SYSTEM_ERROR("9999", "system.error", false),
    SYSTEM_UPGRADING("9993", "system.status.upgrading", false),
    APP_FORBIDDEN("9992", "app.forbidden"),

    //没有发放红包权限
    LUCK_FORTUNE_RELEASE_PERMISSION("100000", "luck.fortune.release.permission denied", false),
    //参数为空
    LUCK_FORTUNE_PARAM_NULL("100001", "luck.fortune.param.null", false),
    //红包数量无效
    LUCK_FORTUNE_NUM_INVALID("100002", "luck.fortune.num.invalid", false),
    //红包金额无效
    LUCK_FORTUNE_AMOUNT_INVALID("100003", "luck.fortune.amount.invalid", false),
    //红包金额无效
    LUCK_FORTUNE_SINGLE_AMOUNT_INVALID("100004", "luck.fortune.single.amount.invalid", false),
    //红包无效
    LUCK_FORTUNE_INVALID("100005", "luck.fortune.invalid", false),
    //操作过于频繁
    LUCK_FORTUNE_OPERATION_FAST("100006", "luck.fortune.operation.faster", false),
    //红包已经被领空
    LUCK_FORTUNE_EMPTY_ERROR("100007", "luck.fortune.empty", false),
    //已经领取过
    LUCK_FORTUNE_RECEIVE_REPEAT_ERROR("10008", "luck.fortune.repeat", false),
    //领取 or 次数金额已达上限
    LUCK_FORTUNE_RECEIVE_LIMIT_ERROR("10009", "luck.fortune.receive.limit", false),
    //发放次数达上限
    LUCK_FORTUNE_RELEASE_LIMIT_ERROR("100012", "luck.fortune.release.limit", false),
    //红包已过期
    LUCK_FORTUNE_EXPIRED("100010", "luck.fortune.expired",false),
    LUCK_FORTUNE_CURRENCY_INVALID("100011", "luck.fortune.currency", false),
    //抽奖已达上限
    LOTTERY_PRODUCT_LIMIT("100013", "lottery.product.limit", false),
    //领取次数达日上限，
    LUCK_FORTUNE_RECEIVE_LIMIT_DAY_ERROR("100015", "luck.fortune.receive.limit.day", false),
    //领取次数月上限，
    LUCK_FORTUNE_RECEIVE_LIMIT_MONTH_ERROR("100016", "luck.fortune.receive.limit.month", false),
    //领取金额达月上限
    LUCK_FORTUNE_RECEIVE_LIMIT_AMOUNT_MONTH_ERROR("100017", "luck.fortune.receive.limit.amount.month", false),
    //领取金额达日上限
    LUCK_FORTUNE_RECEIVE_LIMIT_AMOUNT_DAY_ERROR("100018", "luck.fortune.receive.limit.amount.day", false),
    //未参与过空投新人活动
    LUCK_FORTUNE_ACTIVITY_INVALID("100019", "luck.fortune.activity.invalid"),
    AUTH_CODE_INVALID("100020", "auth.code.invalid"),
    TASK_VERIFY_NO_PASS("100021", "task.verify.no.pass"),
    CLAIM_REWARD_FAIL("100022", "claim.reward.fail"),
    TASK_VERIFY_ALREADY("100023", "task.verify.already"),
    AUTH_BIND_FAIL("100024", "auth.bind.fail"),
    POINT_NOT_ENOUGH("100025", "point.not.enough"),
    TASK_PRE_CHECK_NOT_PASS("100026", "task.pre.check.not.pass"),
    STOCK_NOT_ENOUGH("100027", "stock.not.enough"),
    ACTIVITY_HOT("100028", "activity.hot"),
    AUTH_REPEAT("100029", "auth.repeat"),
    NOT_SUPPORT_SCENE("100030", "not.support.scene"),

    CUSTOMER_NOT_FOUND("100030", "customer.not.found"),
    CUSTOMER_NOT_AUTH("100031", "customer.not.auth"),

    MEMBER_HARVESTING("100032", "member.harvesting"),
    MEMBER_UPGRADING("100033", "member.upgrading"),
    MEMBER_EXPANDING("100035", "member.expanding"),
    NOT_ALLOW_CLAIM_NFT("100036", "not.allow.claim"),
    CUSTOMER_AUTH_NOT_SAME_WITH_LOGIN("100038", "customer.auth.mot.same.with.login"),
    CLAIM_ITEM_ALREADY("100039", "claim.item.already"),
    CHECK_CRATED_DAYS_FAIL("100040", "check.created.days.fail"),

    SOCIAL_CALL_BACK_WAIT("100041", "social.call.back.wait"),


    ;


    private String key;
    private String code;
    private boolean success;

    private WebResponseEnum(String code, String key, boolean success) {
        this.key = key;
        this.code = code;
        this.success = success;
    }

    private WebResponseEnum(String code, String key) {
        this.key = key;
        this.code = code;
        this.success = false;
    }

    public String getKey() {
        return this.key;
    }

    public String getCode() {
        return this.code;
    }

    public boolean isSuccess() {
        return this.success;
    }

}
