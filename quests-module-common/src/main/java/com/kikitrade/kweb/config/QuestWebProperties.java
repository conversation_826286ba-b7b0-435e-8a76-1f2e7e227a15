package com.kikitrade.kweb.config;

import com.kikitrade.kweb.model.common.SaasContext;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: penuel
 * @date: 2021/7/29 17:33
 */
@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "quest-web")
public class QuestWebProperties {

    /*
     登陆地址
     */
    public Map<String, String> tWebCustomerLogin;
    /**
     * 根据idtoken当前用户查询地址
     */
    public Map<String, String> tWebCustomerIdtoken;

    /**
     * 当前用户返回数据模版
     */
    public Map<String, String> tWebCustomerTemp;
    /**
     * 当前用户请求url模版
     */
    public Map<String, String> tWebCustomerUrlTemp;
    /**
     * 其他平台公钥地址
     */
    public Map<String, String> tWebCustomerSign;
    /**
     * 根据钱包地址查询当前用户
     */
    public Map<String, String> tWebCustomerAddress;
    /**
     * 根据jwt查询当前用户
     */
    public Map<String, String> tWebCustomerJwt;

    public Map<String, Map<Integer, Integer>> memberAddPercent;

    public String web3DefaultAddressType = "evm";

    public String privateKey;
    public String publicKey;

    public Map<String, String> jwtUid;
    public Map<String, String> jwtUrl;

    public Long rankStartSecond;

    public Long seasonStartTime;

    public Map<String, SeasonTime> breadSeasonTime;

    public Map<String, SeasonTime> zeekSeasonTime;

    public Integer httpMaxPoolSize = 200;

    public String OrderStatusTopic;
    public String CustomerEventTopic;

    /**
     * x-manager-service-url
     */
    public String xManagerServiceUrl;

    /**
     * credentials rise limit
     * eg: quest-web.credentials-rise-limit.opensocial=150
     */
    public Map<String, Long> credentialsRiseLimit;

    public Map<String, String> getTWebCustomerLogin() {
        return tWebCustomerLogin == null ? new HashMap<>() : tWebCustomerLogin;
    }

    public Map<String, String> getTWebCustomerIdtoken() {
        return tWebCustomerIdtoken == null ? new HashMap<>() : tWebCustomerIdtoken;
    }

    public Map<String, String> getTWebCustomerTemp() {
        return tWebCustomerTemp == null ? new HashMap<>() : tWebCustomerTemp;
    }

    public Map<String, String> getTWebCustomerSign() {
        return tWebCustomerSign == null ? new HashMap<>() : tWebCustomerSign;
    }

    public Map<String, String> getTWebCustomerAddress() {
        return tWebCustomerAddress == null ? new HashMap<>() : tWebCustomerAddress;
    }

    public Map<String, String> getTWebCustomerJwt() {
        return tWebCustomerJwt == null ? new HashMap<>() : tWebCustomerJwt;
    }

    public Map<String, String> getJwtUid() {
        return jwtUid == null ? new HashMap<>() : jwtUid;
    }

    public Map<String, String> getJwtUrl() {
        return jwtUrl;
    }

    public Map<String, String> getTWebCustomerUrlTemp() {
        return tWebCustomerUrlTemp == null ? new HashMap<>() : tWebCustomerUrlTemp;
    }

    @Data
    public static class SeasonTime{
        private Long startTime;
        private Long endTime;
    }
}
