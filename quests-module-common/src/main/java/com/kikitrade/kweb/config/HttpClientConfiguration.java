package com.kikitrade.kweb.config;

import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/4/26 18:01
 */
@Configuration
public class HttpClientConfiguration {

    @Bean("okHttpClient")
    public OkHttpClient okHttpClient(QuestWebProperties questWebProperties){
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        client.dispatcher().setMaxRequests(questWebProperties.getHttpMaxPoolSize());
        client.dispatcher().setMaxRequestsPerHost(questWebProperties.getHttpMaxPoolSize());
        return client;
    }
}
