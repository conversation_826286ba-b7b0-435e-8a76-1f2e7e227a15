package com.kikitrade.kweb.config;

import com.kikitrade.kweb.interceptor.QuestsAuthInterceptor;
import com.kikitrade.kweb.interceptor.QuestsJwtTokenInterceptor;
import com.kikitrade.kweb.interceptor.QuestsLoginInterceptor;
import com.kikitrade.kweb.interceptor.QuestsSaasInterceptor;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class QuestsWebMvcConfig extends WebMvcConfig {

    private static final Logger log = LoggerFactory.getLogger(WebMvcConfig.class);
    @Resource
    private KWebProperties kWebProperties;
    @Value("${spring.jackson.date-format}")
    private String dateFormatPattern;
    @Value("${spring.jackson.time-zone}")
    private String timeZone;
    @Resource
    private QuestsJwtTokenInterceptor questsJwtTokenInterceptor;
    @Resource
    private QuestsLoginInterceptor questsLoginInterceptor;
    @Resource
    private QuestsSaasInterceptor questsSaasInterceptor;
    @Resource
    private QuestsAuthInterceptor questsAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registerInterceptor(registry, questsSaasInterceptor, saasIdAddPatterns(), saasIdExcludePatterns());
        registerInterceptor(registry, questsJwtTokenInterceptor, jwtAddPatterns(), jwtExcludePatterns());
        registerInterceptor(registry, questsLoginInterceptor, loginAddPatterns(), loginExcludePatterns());
        registerInterceptor(registry, questsAuthInterceptor, addS2SAuthPatterns(), new ArrayList<>());
    }

    private void registerInterceptor(InterceptorRegistry registry, HandlerInterceptor interceptor, List<String> addPatterns, List<String> excludePatterns) {
        if (interceptor != null) {
            InterceptorRegistration registration = registry.addInterceptor(interceptor);
            if (!CollectionUtils.isEmpty(addPatterns)) {
                registration = registration.addPathPatterns(addPatterns);
            }
            if (!CollectionUtils.isEmpty(excludePatterns)) {
                log.info("registry{}, excludePatterns:{}", registry.getClass(), excludePatterns);
                registration.excludePathPatterns(excludePatterns);
            }
        }
    }

    @Override
    protected List<String> jwtExcludePatterns() {
        List<String> list = super.jwtExcludePatterns();
        list.add("/s2/**");
        list.add("/telegram/webhook/**");
        return list;
    }

    @Override
    protected List<String> saasIdExcludePatterns() {
        List<String> list = super.saasIdExcludePatterns();
        list.add("/s2/**");
        list.add("/telegram/webhook/**");
        return list;
    }

    protected List<String> loginExcludePatterns() {
        List<String> paths = super.loginExcludePatterns();
        paths.add("/v1/customer/auth/token");
        paths.add("/v2/tasks/list");
        paths.add("/v2/tasks/detail");
        paths.add("/v2/goods/list");
        paths.add("/v2/goods/detail");
        paths.add("/v2/tasks/doquests");
        paths.add("/v2/reward/grant");
        paths.add("/v2/tasks/twitter/tweets/*");
        paths.add("/s2/**");
        paths.add("/dingtalk/webhook/**");
        paths.add("/v2/customer/me");
        paths.add("/v3/member/points/daily");
        paths.add("/v3/member/top/ladder");
        paths.add("/v3/member/points/extension");
        paths.add("/telegram/webhook/**");
        return paths;
    }

    private List<String> addS2SAuthPatterns(){
        return List.of("/s2/**");
    }
}
