package com.kikitrade.kweb.utils;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.RSA;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PemUtils {

    private static RSA rsa;

    public PemUtils(){}

    public PemUtils(String privateKeyString, String publicKeyString){
        try{
            rsa = SecureUtil.rsa(privateKeyString, publicKeyString);
        }catch (Exception ex){
            log.error("PemUtils init error:{}", publicKeyString, ex);
        }
    }

    public RSA getRsa() {
        return rsa;
    }
}