package com.kikitrade.kweb.model.customer;

import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kweb.model.common.WebResult;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 第三方平台的用户
 * @date 2024/1/29 18:46
 */
@Data
@ToString(callSuper = true)
public class QCustomer extends CustomerBindDTO {

    public QCustomer(){}

    private String id;
    private Map<String, String> address;
    private String vipLevel;
    private String twitterId;
    /**
      Json object, address on all platform
     */
    private String addressAll;

    private String publicKey;

    private Boolean ospVerified = false;

    private Boolean xVerified = false;

    private String avatar;

    private List<String> addressObjects;

    public QCustomer buildCustomerBind(CustomerBindDTO customerBindDTO){
        this.setUid(customerBindDTO.getUid());
        this.setApp(customerBindDTO.getApp());
        this.setCid(customerBindDTO.getCid());
        this.setSaasId(customerBindDTO.getSaasId());
        this.setTwitterName(customerBindDTO.getTwitterName());
        this.setDiscordName(customerBindDTO.getDiscordName());
        this.setOspVerified(customerBindDTO.getOspVerified());
        this.setXVerified(customerBindDTO.getXVerified());
        return this;
    }
}
