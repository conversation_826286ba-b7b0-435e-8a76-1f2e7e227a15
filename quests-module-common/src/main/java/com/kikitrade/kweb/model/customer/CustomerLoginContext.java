package com.kikitrade.kweb.model.customer;

import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

@Data
@AllArgsConstructor
public class CustomerLoginContext<T extends CustomerDTO, K extends CustomerBindDTO> implements Serializable {
    public static final String LOGIN_TYPE_WEB = "com/kikitrade/kweb";
    public static final String LOGIN_TYPE_ANDROID = "android";
    public static final String LOGIN_TYPE_IOS = "ios";
    private String ip;
    private String locale;
    private String jwtToken;
    private String idToken;
    private String publicKey;
    T customer;
    K qcustomer;
}
