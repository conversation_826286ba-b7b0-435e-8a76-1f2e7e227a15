package com.kikitrade.kweb.service.impl;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.model.CustomerExtraDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.kcustomer.common.constants.CustomerConstants;
import com.kikitrade.kweb.config.QuestWebProperties;
import com.kikitrade.kweb.constants.WebConstants;
import com.kikitrade.kweb.interceptor.CustomerHolder;
import com.kikitrade.kweb.model.customer.CustomerLoginContext;
import com.kikitrade.kweb.model.customer.QCustomer;
import com.kikitrade.kweb.service.KwebBaseService;
import com.kikitrade.kweb.utils.LocaleUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.Serializable;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/08/13 15:54
 **/

@Slf4j
@Service
public class KwebBaseServiceImpl<T extends CustomerExtraDTO> implements KwebBaseService {

    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;
    @Resource
    private QuestWebProperties questWebProperties;
    @Resource
    private OkHttpClient okHttpClient;
    private static final String ADDRESS_URL = "?address=";
    private static final String USER_ID_URL = "?id=";

    @Override
    public void storeToContext(HttpServletRequest request, String jwtToken, String idToken, String publicKey, QCustomer qCustomer, String ip) {
        CustomerDTO customerDTO = new CustomerDTO();
        customerDTO.setId(qCustomer.getUid());
        customerDTO.setSaasId(qCustomer.getSaasId());
        CustomerLoginContext context = new CustomerLoginContext(
                ip,
                LocaleUtils.getLocaleStr(),
                jwtToken,
                idToken,
                publicKey,
                customerDTO,
                qCustomer);
        CustomerHolder.bind(context);
        request.setAttribute(WebConstants.CUSTOMER, qCustomer);
    }

    @Override
    public QCustomer getByIdToken(String saasId, String idToken, String publicKey) {
        Request request = buildRequest(saasId, idToken, publicKey);
        Response response;
        try {
            response = okHttpClient.newCall(request).execute();
            QCustomer customer = buildResponse(saasId, response);
            if(customer == null){
                return null;
            }
            QCustomer currentUser = new QCustomer();
            currentUser.setVipLevel("NORMAL");
            if(customer.getAddress() != null){
                currentUser.setAddress(customer.getAddress());
            }
            currentUser.setAddressAll(customer.getAddressAll());
            CustomerBindDTO customerBindDTO = remoteCustomerBindService.findById(saasId, customer.getId());
            if(customerBindDTO != null){
                currentUser.setId(customer.getId());
                currentUser.buildCustomerBind(customerBindDTO);
                return currentUser;
            }
            CustomerDTO customerDTO = new CustomerDTO();
            customerDTO.setSaasId(saasId);
            customerDTO.setId(customer.getId());
            customerBindDTO = remoteCustomerBindService.register(customerDTO);
            currentUser.buildCustomerBind(customerBindDTO);
            return currentUser;
        } catch (IOException e) {
            log.error("getByIdToken error,{},{}", saasId, idToken, e);
            return null;
        }
    }

    @Override
    public QCustomer getByAddress(String saasId, String address) {
        try {
            QCustomer currentUser = new QCustomer();
            currentUser.setVipLevel("NORMAL");
            if(!address.startsWith("0x")){
                CustomerBindDTO customerBindDTO = remoteCustomerBindService.findById(saasId, address);
                if(customerBindDTO != null){
                    currentUser.setId(address);
                    currentUser.buildCustomerBind(customerBindDTO);
                    return currentUser;
                }
            }

            String url = questWebProperties.getTWebCustomerAddress().get(saasId);
            String id = address;
            Long vipEndTime = null;
            if(url != null){
                Request request = new Request.Builder()
                        .url(String.format("%s%s%s", url, address.startsWith("0x") ? ADDRESS_URL : USER_ID_URL ,address))
                        .get()
                        .build();
                Response response = okHttpClient.newCall(request).execute();
                JSONObject jsonObject = JSON.parseObject(response.body().string());
                log.info("getByAddress response:{}", jsonObject);
                if(!jsonObject.getBoolean("success")){
                    return null;
                }
                JSONObject data = jsonObject.getJSONObject("obj");
                String rows = data.getString("rows");
                List<AddressVO> maps = JSON.parseArray(rows, AddressVO.class);
                if(CollectionUtils.isEmpty(maps)){
                    return null;
                }
                id = maps.get(0).getId();
            }

            CustomerBindDTO customerBindDTO = remoteCustomerBindService.findById(saasId, address);
            if(customerBindDTO != null){
                currentUser.setId(address);
                currentUser.buildCustomerBind(customerBindDTO);
                currentUser.setVipLevel("NORMAL");
                return currentUser;
            }

            CustomerDTO customerDTO = new CustomerDTO();
            customerDTO.setSaasId(saasId);
            customerDTO.setId(id);
            customerBindDTO = remoteCustomerBindService.register(customerDTO);
            currentUser.setVipLevel("NORMAL");
            currentUser.buildCustomerBind(customerBindDTO);
            return currentUser;
        } catch (IOException e) {
            return null;
        }
    }

    private Request buildRequest(String saasId, String idToken, String publicKey){
        String temp = questWebProperties.getTWebCustomerUrlTemp().get(saasId);
        String url = questWebProperties.getTWebCustomerIdtoken().get(saasId);
        if ("t1".equals(temp)) {
            //osp 为代表的数据结构
            return new Request.Builder()
                    .url(url)
                    .get()
                    .addHeader("id_token", idToken)
                    .build();
        }else{
            return new Request.Builder()
                    .url(String.format("%s?idToken=%s&publicKey=%s",
                            url,
                            idToken,
                            publicKey))
                    .get()
                    .addHeader("saas_id", saasId)
                    .build();
        }
    }

    @Override
    public QCustomer getByJwt(String saasId, String jwt) {
        QCustomer currentUser = new QCustomer();
        String url = questWebProperties.getTWebCustomerJwt().get(saasId);
        Request.Builder builder = new Request.Builder()
                .url(url)
                .get();
        if("trex".equals(saasId)){
            builder.addHeader("Authorization", jwt.startsWith("Bearer ") ? jwt : "Bearer " + jwt);
        }else{
            builder.addHeader("JWT_TOKEN", jwt);
        }
        String appId = getAppId(saasId);
        if(appId != null){
            builder.addHeader("app_id", appId);
        }
        Request request = builder.build();

        try{
            Response response = okHttpClient.newCall(request).execute();
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            log.info("getByJwt:{}", jsonObject);
            if(!jsonObject.getBoolean("success")){
                return null;
            }
            JSONObject data = jsonObject.getJSONObject("obj");
            String id = data.containsKey("id") ? data.getString("id") : data.getString("passportId");
            if(data.getJSONObject("addressAll") != null
                    && data.getJSONObject("addressAll").getJSONObject("address") != null
                    && data.getJSONObject("addressAll").getJSONObject("address").getJSONObject("osp") != null
                && data.getJSONObject("addressAll").getJSONObject("address").getJSONObject("osp").getJSONArray("osp_aa") != null){
                currentUser.setAddressAll(
                        data.getJSONObject("addressAll").getJSONObject("address").getJSONObject("osp").getJSONArray("osp_aa").get(0).toString());
            }

            if(data.getJSONObject("addressAll") != null
                    && data.getJSONObject("addressAll").getJSONObject("address") != null
                    && data.getJSONObject("addressAll").getJSONObject("address").getJSONObject("web3auth") != null
                    && !data.getJSONObject("addressAll").getJSONObject("address").getJSONObject("web3auth").getJSONArray("evm").isEmpty()){
                Object evmAddress = data.getJSONObject("addressAll").getJSONObject("address").getJSONObject("web3auth").getJSONArray("evm").get(0);
                currentUser.setPublicKey(String.valueOf(evmAddress));
            }

            if(data.containsKey("addressObjects") && data.getJSONArray("addressObjects") != null){
                JSONArray addressObjects = data.getJSONArray("addressObjects");
                addressObjects.sort(Comparator.comparingLong(o -> ((JSONObject) o).getLong("bindTime")));
                String firstAddress = addressObjects.getJSONObject(0).getString("address");
                currentUser.setPublicKey(String.valueOf(firstAddress));
                List<String> address = addressObjects.stream().map(o -> ((JSONObject) o).getString("address")).toList();
                currentUser.setAddressObjects(new ArrayList<>(address));
            }

            log.info("currentUser:{}", currentUser);
            CustomerBindDTO customerBindDTO = remoteCustomerBindService.findById(saasId, id);
            if(customerBindDTO != null){
                currentUser.setId(id);
                currentUser.buildCustomerBind(customerBindDTO);
                return currentUser;
            }
            CustomerDTO customerDTO = new CustomerDTO();
            customerDTO.setSaasId(saasId);
            customerDTO.setId(id);
            customerBindDTO = remoteCustomerBindService.register(customerDTO);
            currentUser.buildCustomerBind(customerBindDTO);
            return currentUser;
        }catch (Exception ex){
            log.error("getByJwt:{}", jwt, ex);
            return null;
        }
    }

    @Override
    public QCustomer getByIdInner(String saasId, String idToken, String publicKey) {
        Response response;
        try {
            QCustomer currentUser = new QCustomer();
            JWT jwt = JWTUtil.parseToken(idToken);
            currentUser.setAddressAll(jwt.getPayloads().getStr("ospAddress"));
            String ownerId = jwt.getPayloads().getStr("ospOwnerId");
            CustomerBindDTO customerBindDTO = remoteCustomerBindService.findById(saasId, ownerId);
            if(customerBindDTO != null){
                currentUser.setId(ownerId);
                currentUser.buildCustomerBind(customerBindDTO);
                return currentUser;
            }
            CustomerDTO customerDTO = new CustomerDTO();
            customerDTO.setSaasId(saasId);
            customerDTO.setId(ownerId);
            customerBindDTO = remoteCustomerBindService.register(customerDTO);
            currentUser.buildCustomerBind(customerBindDTO);
            return currentUser;
        } catch (Exception e) {
            log.error("getByInnerToken error,{},{}", saasId, idToken, e);
            return null;
        }
    }

    @Override
    public CustomerBindDTO getCustomerById(String saasId, String cid) {

        CustomerBindDTO customerBindDTO = remoteCustomerBindService.findById(saasId, cid);
        if(customerBindDTO == null){
            CustomerDTO customerDTO = new CustomerDTO();
            customerDTO.setSaasId(saasId);
            customerDTO.setId(cid);
            return remoteCustomerBindService.register(customerDTO);
        }
        return customerBindDTO;
    }

    @Override
    public CustomerBindDTO getCustomerByHandleName(String saasId, String appId, String handleName, String chainId) {
        try {
            if(appId == null || handleName == null){
                return null;
            }
            URL url = new URL(questWebProperties.getTWebCustomerAddress().get(saasId));
            Request request = new Request.Builder()
                    .url(url.toString().substring(0, url.toString().indexOf(url.getPath())) + "/v2/profiles/handle/" + handleName)
                    .get()
                    .addHeader("os-app-id", appId)
                    .addHeader("os-chain-id", chainId)
                    .build();
            Response response = okHttpClient.newCall(request).execute();
            log.info("getCustomerByHandleName:{}", response);
            QCustomer customer = buildHandleResponse(saasId, response);
            CustomerBindDTO customerBindDTO = remoteCustomerBindService.findById(saasId, customer.getId());
            if(customerBindDTO == null){
                return null;
            }
            customerBindDTO.setOspAvatar(customer.getAvatar());
            return customerBindDTO;
        } catch (Exception e) {
            log.error("getCustomerByHandleName error,{},{},{}", saasId, appId, handleName, e);
            return null;
        }
    }

    private QCustomer buildResponse(String saasId, Response response) throws IOException {
        JSONObject jsonObject = JSON.parseObject(response.body().string());
        log.info("getByToken response:{}", jsonObject);
        QCustomer customer = new QCustomer();
        if(!jsonObject.getBoolean("success")){
            return null;
        }
        JSONObject data = jsonObject.getJSONObject("obj");
        String id = data.getString("id");
        String address = data.getString("osp_address");
        customer.setId(id);
        customer.setAddress(Map.of(questWebProperties.getWeb3DefaultAddressType(), address));
        customer.setAddressAll(address);
        customer.setVipLevel("NORMAL");
        return customer;
    }

    private QCustomer buildHandleResponse(String saasId, Response response) throws IOException {
        JSONObject jsonObject = JSON.parseObject(response.body().string());
        JSONObject dataObject = jsonObject.getJSONObject("data");
        if(dataObject == null){
            return null;
        }
        String owner = dataObject.getString("owner");
        String avatar = dataObject.getString("avatar");
        QCustomer customer = new QCustomer();
        customer.setId(owner);
        customer.setAvatar(avatar);
        return customer;
    }

    @Data
    public static class AddressVO implements Serializable {
        private String id;
        private String osp_address;
        private Long vipEndTime;
    }

    private String getAppId(String saasId){
        if(saasId == null){
            return null;
        }
        if(saasId.equalsIgnoreCase("mugen") || saasId.equalsIgnoreCase("slg")){
            return "slg";
        }
        return null;
    }
}
