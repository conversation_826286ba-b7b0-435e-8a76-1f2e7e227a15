package com.kikitrade.kweb.service;

import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kweb.model.customer.QCustomer;
import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @create 2021/8/2 11:57 上午
 * @modify
 */
public interface KwebBaseService<T extends CustomerDTO> {

    void storeToContext(HttpServletRequest request, String jwtToken, String idToken, String publicKey, QCustomer qCustomer, String ip);

    QCustomer getByIdToken(String saasId, String idToken, String publicKey);

    QCustomer getByAddress(String saasId, String address);

    QCustomer getByJwt(String saasId, String jwt);

    QCustomer getByIdInner(String saasId, String idToken, String publicKey);

    CustomerBindDTO getCustomerById(String saasId, String cid);

    CustomerBindDTO getCustomerByHandleName(String saasId, String app_id, String handleName, String chainId);
}
