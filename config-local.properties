###########################kweb application config########################
kweb.env=local
kweb.saas-id=kiki
debug=true
app.log.path=/Users/<USER>/Dev/New/kweb/logs
zipkin.host=127.0.0.1
server.port=8081
management.server.port=9090
###########################dubbo config###################################
dubbo.name=quests-web
dubbo.protocol.port=20887
dubbo.consumer.group=kktd
dubbo.provider.group=kktd
dubbo.protocol.host=**************
#zookeeper.url=zookeeper://api.dev.dipbit.xyz:2181
zookeeper.url=127.0.0.1:2181
###########################redis config####################################
redis.host=exkikidevo.redis.singapore.rds.aliyuncs.com
redis.port=6379
redis.password=Credit2021Admin
redis.maxTotal=32
redis.dbIndex=7
##################nacos config##############################################
#æ¬å°å¯å¨ spring.cloud.nacos.config.enabled=false
nacos.config.enabled=false
cloud.nacos.config.group=quests-web
cloud.nacos.config.name=quests-web
nacos.config.file-extension=properties
##################ignite config#############################################
ignite.namespace=kikitrade
ignite.environment.local=true
ignite.application.name=quests-web
ignite.environment.namespace=kikitrade-local
###########################jwt token########################################
jwt.secret.upgrade=0XtdLsYulHv0XCDB+Rm0l5dDt8le1xoONjFUSkX6GsA=
###########################ots##############################################
kiki.ots.endpoint=https://KikiTradeTest.ap-southeast-1.ots.aliyuncs.com
kiki.ots.instance-name=KikiTradeTest
###########################dingtalk#########################################
dingtalk.app.root=http://dingtalk.dipbit.xyz:8081/manage
dingtalk.appId=dingeb4kipoasyzafitk
dingtalk.appSecret=kkkreonxWpPjYhFgRspCw4ujDwSp3cHaKnx5DJx10DjfcjW5RFsg2S3EPF1vOZIo
dingtalk.agentId=217974206
###########################ons##############################################
kiki.ons.address=http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
kiki.ons.group-id=GID_WEB_QUESTS_DEV
kiki.ons.env=_DEV
kiki.ons.tag=dev
kweb.ons-topic-ding-talk-business=T_WEB_DINGTALK_QUESTS_DEV

kiki.ons.broadcast-consumer.enable=true
kiki.ons.broadcast-group-id=GID_WEB_BROADCAST_QUESTS_DEV
kweb.ons-topic-cache-refresh=T_WEB_CACHE_REFRESH_QYESTS_DEV
kweb.query-customer-open = true
kiki.ons.cache-refresh-topic=T_CACHE_REFRESH_KIKI_DEV
###########################zendesk jwt######################################
zendesk.chat.sign=BE2387A4925E355E2B89807ADDDE1538695ECA7A821EFE2EDF65675924E8FB5F


ip.whitelist=*************,127.0.0.1
margin.function.open=false

springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
# kcomputeæå¡æ¯å¦å¯ç¨,trueï¼å¯ç¨ï¼falseï¼ä¸å¯ç¨ï¼é»è®¤false
kweb.enable.compute=true

kmarket.client.metadata.cache.consumes=currency,symbol,zone