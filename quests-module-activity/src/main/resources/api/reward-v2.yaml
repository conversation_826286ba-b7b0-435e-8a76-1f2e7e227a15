openapi: 3.0.3
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
tags:
  - name: RewardV2
paths:
  /v2/reward/allow/claim/{type}:
    get:
      summary: 判断NFT是否claim过
      description: ''
      operationId: allowClaim
      tags:
        - RewardV2
      parameters:
        - name: type
          in: path
          description: '支持的值 nft'
          required: true
          schema:
            type: string
        - name: saas_id
          in: header
          description: ''
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResult'
  /v2/reward/claim/{type}:
    post:
      summary: 使用nft/兑换码兑换积分
      description: ''
      operationId: claim
      tags:
        - RewardV2
      parameters:
        - name: type
          in: path
          description: '支持的值 nft/redeem'
          required: true
          schema:
            type: string
        - name: saas_id
          in: header
          description: ''
          required: true
          schema:
            type: string
        - name: code
          in: query
          description: '兑换码'
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultClaimVO'
  /v2/reward/receive:
    post:
      summary: 领取奖励
      description: ''
      tags:
        - RewardV2
      parameters:
        - name: taskId
          in: query
          description: ''
          required: true
          schema:
            type: string
        - name: saas_id
          in: header
          description: ''
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResult'
  /v2/reward/grant:
    post:
      summary: 发放积分
      description: ''
      tags:
        - RewardV2
      parameters:
        - name: address
          in: query
          description: ''
          required: true
          schema:
            type: array
            items:
              description: 被发奖用户钱包地址
              type: string
        - name: price
          in: query
          description: ''
          required: true
          schema:
            type: string
            description: 发放的积分数
        - name: businessId
          in: query
          description: ''
          required: false
          schema:
            type: string
            description: 唯一标识
        - name: desc
          in: query
          description: ''
          required: true
          schema:
            type: string
            description: 发放理由
        - name: saas_id
          in: header
          description: ''
          required: true
          schema:
            type: string
        - name: sign
          in: header
          description: app授权码
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultRewardResultVO'
components:
  schemas: 
    WebResult:
      type: object
      discriminator:
        propertyName: code
    WebResultRewardResultVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              type: array
              items:
                $ref: '#/components/schemas/RewardResultVO'
    RewardResultVO:
      type: object
      properties:
        address:
          type: string
        reason:
          type: number

    WebResultClaimVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/ClaimVO'
    ClaimVO:
      type: object
      properties:
        reward:
          description: 奖励金额
          type: number
        currency:
          description: 奖励币种
          type: string
        orderId:
          description: 订单id，如果需要支付时产生订单id
          type: string


servers:
  - url: 'http://api.dev.quests.com'
    description: play-test
