openapi: 3.0.3
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
tags:
  - name: RewardS2
paths:
  /s2/reward/grant:
    post:
      summary: 发放积分
      description: ''
      tags:
        - RewardS2
      parameters:
        - name: customerId
          in: query
          description: ''
          required: true
          schema:
            description: 被发奖用户的用户 id
            type: string
        - name: amount
          in: query
          description: ''
          required: true
          schema:
            type: string
            description: 发放的积分数
        - name: desc
          in: query
          description: ''
          required: true
          schema:
            type: string
            description: 发放理由
        - name: businessId
          in: query
          description: ''
          required: false
          schema:
            type: string
            description: 业务 id
        - name: saas_id
          in: header
          description: ''
          required: true
          schema:
            type: string
        - schema:
            type: string
          in: header
          name: Authorization
          required: true
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultRewardResultVO'
  /s2/reward/receive:
    post:
      summary: 领取奖励
      description: ''
      tags:
        - RewardS2
      parameters:
        - name: saas_id
          in: header
          description: ''
          required: true
          schema:
            type: string
        - name: Authorization
          in: header
          required: true
          schema:
            type: string
        - name: customerId
          in: query
          description: ''
          required: true
          schema:
            description: 领取奖励的用户 id
            type: string
        - name: taskId
          in: query
          description: ''
          required: true
          schema:
            type: string
        - name: extendAttr
          in: query
          description: 事件附加属性
          required: false
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResult'
components:
  schemas: 
    WebResult:
      type: object
      discriminator:
        propertyName: code
    WebResultRewardResultVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/RewardResultVO'
    RewardResultVO:
      type: object
      properties:
        customerId:
          type: string
        reason:
          type: string

servers:
  - url: 'http://api.dev.quests.com'
    description: play-test
