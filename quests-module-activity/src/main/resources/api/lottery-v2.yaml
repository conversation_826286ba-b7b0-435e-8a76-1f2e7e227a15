openapi: 3.0.1
info:
  license:
    name: Apache 2.0
    url: 'https://www.apache.org/licenses/LICENSE-2.0'
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
tags:
  - name: LotteryV2
    description: 抽奖相关接口

servers:
  - description: Generated server url
    url: 'http://api.quests.dev.dipbit.xyz'
paths:
  '/v2/lottery/{code}/open':
    post:
      summary: 抽奖
      description: 抽奖
      operationId: drew
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultLotteryVO'
      parameters:
        - schema:
            type: string
          in: path
          name: code
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          required: true
      tags:
        - LotteryV2
  /v2/lottery/info:
    get:
      summary: 奖池信息
      description: 奖池信息
      operationId: lotteryInfo
      parameters:
        - schema:
            type: string
          in: header
          name: saas_id
          required: true
        - schema:
            type: string
          in: query
          name: code
          required: true
          description: 多个code，以逗号分隔
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultLotteryInfoVO'
      tags:
        - LotteryV2
  /v2/lottery/winners:
    get:
      summary: 中奖名单查询
      operationId: lotteryWinners
      parameters:
        - schema:
            type: string
          in: header
          name: saas_id
          required: true
        - schema:
            type: string
          in: query
          name: lotteryCode
          required: true
          description: 抽奖活动code
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultLotteryWinnersVO'
      tags:
        - LotteryV2
components:
  schemas:
    WebResult:
      type: object
      discriminator:
        propertyName: code
    Date:
      type: object
    WebResultLotteryVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/LotteryVO'
    LotteryVO:
      type: object
      properties:
        reward:
          description: 奖励金额
          type: number
        currency:
          description: 奖励币种
          type: string
        orderId:
          description: 订单id，如果需要支付时产生订单id
          type: string

    WebResultLotteryInfoVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              type: array
              items:
                $ref: '#/components/schemas/LotteryInfoVO'

    WebResultLotteryWinnersVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              type: array
              items:
                $ref: '#/components/schemas/LotteryWinnersVO'

    LotteryInfoVO:
      type: object
      properties:
        code:
          description: 宝箱的业务标识
          type: string
        type:
          description: 宝箱类型，gold-box(黄金宝箱)/goldest-box(紫金宝箱)
          type: string
        total:
          description: 宝箱总数
          type: integer
        opened:
          description: 已开启宝箱数量
          type: integer
        nextOpenTime:
          description: 下次开启宝箱时间，utc时间
          type: integer
          format: int64
        cumulateTotal:
          description: 累积开启的宝箱总数
          type: integer
        nextCumulateCnt:
          description: 累积开启的宝箱总数
          type: integer
        nextCumulateAward:
          description: 扩展字段
          type: number

    LotteryWinnersVO:
      type: object
      properties:
        poolCode:
          type: string
          description: 奖池标识
        poolWinners:
          type: array
          description: 各奖池的中奖者列表，key为奖池标识(poolCode)，value为该奖池的中奖者列表
          items:
            $ref: '#/components/schemas/Winner'
    Winner:
      type: object
      properties:
        address:
          type: string
          description: 用户地址，用于标识中奖者
        order:
          type: integer
          description: 中奖排名
      required:
        - address
        - order
