openapi: 3.0.3
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
tags:
  - name: GoodsV2
paths:
  /v2/goods/list:
    get:
      summary: 商品列表
      description: ''
      tags:
        - GoodsV2
      parameters:
        - name: offset
          in: query
          description: ''
          required: false
          example: '0'
          schema:
            type: integer
        - name: limit
          in: query
          description: ''
          required: false
          example: '8'
          schema:
            type: integer
            default: 8
        - name: saas_id
          in: header
          description: saas_id
          required: false
          example: mugen
          schema:
            type: string
        - name: exclude
          in: query
          description: exclude
          required: false
          example: '0'
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultGoodsListVO'
  /v2/goods/detail:
    get:
      summary: 商品详情
      description: ''
      tags:
        - GoodsV2
      parameters:
        - name: goodsId
          in: query
          description: 商品id
          required: false
          example: '1000001'
          schema:
            type: string
        - name: saas_id
          in: header
          description: saas_id
          required: false
          example: mugen
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultGoodsVO'
  /v2/goods/redeem:
    post:
      summary: 兑换商品
      description: ''
      tags:
        - GoodsV2
      parameters:
        - name: goodsId
          in: query
          description: ''
          required: true
          example: 商品id
          schema:
            type: string
        - name: totalPrice
          in: query
          description: ''
          required: true
          example: 总价格
          schema:
            type: integer
        - name: quality
          in: query
          description: ''
          required: true
          example: 购买数量
          schema:
            type: integer
        - name: saas_id
          in: header
          description: saas_id
          required: false
          example: mugen
          schema:
            type: string
        - name: timestamp
          in: query
          description: ''
          required: true
          example: 当前时间
          schema:
            type: integer
            format: int64
        - name: sign
          in: query
          description: ''
          required: true
          example: 签名
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResult'
components:
  schemas:
    WebResult:
      type: object
      discriminator:
        propertyName: code
    WebResultGoodsVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/GoodsDetailVO'
      title: 商品详情
    GoodsVO:
      type: object
      properties:
        goodsId:
          type: string
          title: 商品id
        outId:
          type: string
          title: 外部商品id
        name:
          type: string
          title: 商品名称
        desc:
          type: string
          title: 商品描述
        type:
          type: string
          title: 商品类型
        blockchain:
          type: string
          title: 商品所属链
        price:
          type: number
          title: 商品价格
        currency:
          type: string
          title: 币种
        currencyType:
          type: string
          title: 币种类型
        stock:
          type: integer
          title: 库存
        startTime:
          type: integer
          format: int64
          title: 商品售卖时间
        endTime:
          type: integer
          format: int64
          title: 商品售卖时间
        image:
          type: string
          title: 商品图片
        labelName:
          type: string
          title: 标签名称
        labelColor:
          type: string
          title: 标签颜色
        status:
          type: integer
          title: '0:上架 1:下架'
        param:
          type: string
          title: 其他属性
    GoodsDetailVO:
      type: object
      properties:
        goodsId:
          type: string
          title: 商品id
        outId:
          type: string
          title: 外部商品id
        name:
          type: string
          title: 商品名称
        desc:
          type: string
          title: 商品描述
        type:
          type: string
          title: 商品类型
        blockchain:
          type: string
          title: 商品所属链
        price:
          type: number
          title: 商品价格
        currency:
          type: string
          title: 币种
        currencyType:
          type: string
          title: 币种类型
        stock:
          type: integer
          title: 库存
        startTime:
          type: integer
          format: int64
          title: 商品售卖时间
        endTime:
          type: integer
          format: int64
          title: 商品售卖时间
        image:
          type: object
          $ref: '#/components/schemas/Map'
          title: 商品图片
        labelName:
          type: string
          title: 标签名称
        labelColor:
          type: string
          title: 标签颜色
        status:
          type: integer
          title: '0:上架 1:下架'
        param:
          type: string
          title: 其他属性
      title: 商品
    WebResultGoodsListVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              type: array
              items:
                $ref: '#/components/schemas/GoodsVO'
      title: 商品列表
servers:
  - url: 'http://api.quests.dev.dipbit.xyz'
    description: play-test
