openapi: 3.0.3
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
tags:
  - name: TaskS2
paths:
  /s2/tasks/fulfill/status:
    get:
      summary: 任务状态查询
      deprecated: false
      description: ''
      tags:
        - TaskS2
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          required: true
        - name: saas_id
          in: header
          description: saasId
          required: true
          schema:
            type: string
        - name: cid
          in: query
          description: 其他平台用户ID
          required: true
          schema:
            type: string
        - name: taskId
          in: query
          description: 任务id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultTaskFulfillVO'
  /s2/tasks/fulfill:
    post:
      summary: 做任务
      deprecated: false
      description: ''
      operationId: 'fulfillTask'
      tags:
        - TaskS2
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          required: true
        - name: saas_id
          in: header
          description: saasId
          required: true
          schema:
            type: string
        - name: cid
          in: query
          description: 用户id、address
          required: true
          schema:
            type: string
        - name: name
          in: query
          description: 事件名称
          required: true
          schema:
            type: string
        - name: contentId
          in: query
          description: 当前事件对应的资源id
          required: true
          schema:
            type: string
        - name: targetUserId
          in: query
          description: 当前事件对应的资源的原资源用户id
          required: false
          schema:
            type: string
        - name: targetContentId
          in: query
          description: 当前事件对应的资源的原资源id
          required: false
          schema:
            type: string
        - name: extendAttr
          in: query
          description: 当前事件附加属性
          required: false
          schema:
            type: string
        - name: eventTime
          in: query
          description: 当前事件对应的时间戳
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResult'
        '400':
          description: 失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResult'
  /s2/tasks/verify:
    get:
      summary: 验证用户是否完成某个操作
      deprecated: false
      description: '验证用户是否完成某个操作'
      tags:
        - TaskS2
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          required: true
        - name: saas_id
          in: header
          description: saasId
          required: true
          schema:
            type: string
        - name: cid
          in: query
          description: 其他平台用户ID
          required: true
          schema:
            type: string
        - name: scene
          in: query
          description: 待验证的场景，目前只支持 follow_x
          required: true
          schema:
            type: string
        - name: ext
          in: query
          description: 其他额外参数，json格式
          required: false
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultVerifyVO'
components:
  schemas:
    WebResult:
      type: object
      discriminator:
        propertyName: code
    Map:
      type: object
    WebResultTaskFulfillVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/TaskFulfillVO'
    TaskFulfillVO:
      properties:
        status:
          type: string
          description: 任务状态
        completeTime:
          type: integer
          format: int64
          description: 完成时间
        completeDays:
          type: integer
          description: 完成天数
        consecutiveDays:
          type: integer
          description: 连续完成天数
    WebResultVerifyVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
servers:
  - url: 'http://api.quests.dev.dipbit.xyz'
    description: play-test
