openapi: 3.0.3
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
tags:
  - name: TaskV2
paths:
  /v2/tasks/list:
    get:
      summary: 获取任务列表
      deprecated: false
      description: ''
      tags:
        - TaskV2
      parameters:
        - name: saas_id
          in: header
          description: saas_id
          required: false
          example: mugen
          schema:
            type: string
        - name: channel
          in: query
          description: channel
          required: false
          example: app、pc
          schema:
            type: string
            default: app
        - name: position
          in: query
          description: home
          required: false
          example: home
          schema:
            type: string
        - name: clientType
          in: query
          description: ios/android
          required: false
          example: ios
          schema:
            type: string
        - name: appVersion
          in: query
          description: app version
          required: false
          example: 1.0
          schema:
            type: string

      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultTaskVO'
  /v2/tasks/detail:
    get:
      summary: 任务详情
      deprecated: false
      description: ''
      tags:
        - TaskV2
      parameters:
        - name: taskId
          in: query
          description: 任务id
          required: false
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultTaskDetailVO'
  /v2/tasks/verify:
    post:
      summary: 做任务
      deprecated: false
      description: ''
      tags:
        - TaskV2
      parameters:
        - name: taskId
          in: query
          description: 任务id
          required: true
          schema:
            type: string
        - name: ext
          in: query
          description: 其他参数，如邀请链接
          required: false
          schema:
            type: string
        - name: saas_id
          in: header
          description: saasId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultDoTaskVO'
  /v2/tasks/dotask:
    post:
      summary: 做任务
      deprecated: false
      description: ''
      tags:
        - TaskV2
      parameters:
        - name: taskId
          in: query
          description: 任务id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultActiveTaskVO'
  /v2/tasks/sync/doquests:
    post:
      summary: 同步做任务
      deprecated: false
      description: ''
      tags:
        - TaskV2
      parameters:
        - name: taskId
          in: query
          description: 任务id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultActiveTaskVOS'
  /v2/tasks/doquests:
    post:
      summary: 做任务
      deprecated: false
      description: ''
      tags:
        - TaskV2
      parameters:
        - name: userId
          in: query
          description: 用户id、address
          required: true
          schema:
            type: string
        - name: name
          in: query
          description: 事件名称
          required: true
          schema:
            type: string
        - name: contentId
          in: query
          description: 当前事件对应的资源id
          required: true
          schema:
            type: string
        - name: targetUserId
          in: query
          description: 当前事件对应的资源的原资源用户id
          required: false
          schema:
            type: string
        - name: targetContentId
          in: query
          description: 当前事件对应的资源的原资源id
          required: false
          schema:
            type: string
        - name: extendAttr
          in: query
          description: 当前事件附加属性
          required: false
          schema:
            type: string
        - name: eventTime
          in: query
          description: 当前事件对应的资源价钱
          required: true
          schema:
            type: integer
            format: int64
        - name: sign
          in: header
          description: app授权码
          required: true
          schema:
            type: string
        - name: saas_id
          in: header
          description: saasId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultTaskRewardVO'
  /v2/tasks/code/{code}:
    get:
      summary: 根据code查询任务详情
      deprecated: false
      description: ''
      tags:
        - TaskV2
      parameters:
        - name: code
          in: path
          description: ''
          required: true
          example: ''
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultSeriesTaskVO'
  /v2/tasks/twitter/tweets/{taskId}:
    get:
      summary: 查询twitter最近的帖子
      deprecated: false
      description: ''
      tags:
        - TaskV2
      parameters:
        - name: saas_id
          in: header
          description: saasId
          required: true
          schema:
            type: string
        - name: taskId
          in: path
          description: taskId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultTaskTweetVO'
  /v2/tasks/reward/by/code:
    get:
      summary:
      deprecated: false
      description: ''
      tags:
        - TaskV2
      parameters:
        - name: saas_id
          in: header
          description: saasId
          required: true
          schema:
            type: string
        - name: code
          in: query
          description: code
          required: true
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultTaskRewardStatVO'
  /v2/tasks/ladder/reward:
    get:
      summary:
      deprecated: false
      description: ''
      tags:
        - TaskV2
      parameters:
        - name: saas_id
          in: header
          description: saasId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultLadderRewardVO'
  /v2/tasks/progress:
    get:
      summary:
      deprecated: false
      description: ''
      tags:
        - TaskV2
      parameters:
        - name: saas_id
          in: header
          description: saasId
          required: true
          schema:
            type: string
        - name: taskId
          in: query
          description: 任务id
          required: true
          schema:
            type: string
        - name: type
          in: query
          description: task or reward, 暂时只支持reward
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultTaskProgressVO'
  /v2/tasks/early/pop:
    get:
      summary:
      deprecated: false
      description: ''
      tags:
        - TaskV2
      parameters:
        - name: saas_id
          in: header
          description: saasId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultTaskPopVO'
  /v2/tasks/checkin:
    post:
      summary: check in
      deprecated: false
      description: 'check in'
      operationId: checkIn
      tags:
        - TaskV2
      parameters:
        - name: saas_id
          in: header
          description: saasId
          required: true
          schema:
            type: string
        - name: idToken
          in: header
          description: idToken
          required: false
          schema:
            type: string
        - name: jwt_token
          in: header
          description: jwt_token
          required: false
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultActiveTaskVO'
  /v2/tasks/checkin/status:
    get:
      summary: check in
      deprecated: false
      description: 'check in'
      operationId: checkInStatus
      tags:
        - TaskV2
      parameters:
        - name: saas_id
          in: header
          description: saasId
          required: true
          schema:
            type: string
        - name: idToken
          in: header
          description: idToken
          required: false
          schema:
            type: string
        - name: jwt_token
          in: header
          description: jwt_token
          required: false
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultCheckInStatusVO'
components:
  schemas:
    WebResult:
      type: object
      discriminator:
        propertyName: code
    Map:
      type: object
    WebResultTaskPopVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/TaskPopVO'
    TaskPopVO:
      type: object
      properties:
        pop:
          type: boolean
        taskId:
          type: string
          description: 任务id
    WebResultTaskProgressVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/TaskProgressVO'
    WebResultTaskRewardStatVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/RewardStatVO'
    WebResultLadderRewardVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              type: array
              items:
                $ref: '#/components/schemas/LadderRewardVO'
    WebResultTaskKolVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              type: array
              items:
                $ref: '#/components/schemas/KolVO'
    WebResultTaskTweetVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              type: array
              items:
                type: string
    WebResultTaskRewardVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/Award'
    WebResultActiveTaskVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/Award'
    WebResultActiveTaskVOS:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              type: array
              items:
                $ref: '#/components/schemas/Award'
    WebResultDoTaskVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/DoTaskVO'
    TaskProgressVO:
      type: object
      properties:
        status:
          type: integer
        limit:
          type: integer
          description: 暂时无效
        process:
          type: integer
          description: 暂时无效
    RewardStatVO:
      type: object
      properties:
        expectReward:
          type: number
    LadderRewardVO:
      type: object
      properties:
        level:
          type: integer
          format: int64
        minScoreForRankReward:
          type: integer
          format: int64
        rankReward:
          type: string
        rankRewardCurrency:
          type: string
        minScoreForWeeklyReward:
          type: integer
          format: int64
        weeklyReward:
          type: string
        weeklyRewardCurrency:
          type: string
    KolVO:
      type: object
      properties:
        userName:
          type: string
        icon:
          type: string
        profileUrl:
          type: string
    DoTaskVO:
      type: object
      properties:
        accessToken:
          type: string
        refreshToken:
          type: string
        postUrl:
          type: string
    WebResultTaskVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              type: array
              items:
                $ref: '#/components/schemas/TaskListResponse'
    TaskListResponse:
      type: object
      properties:
        taskId:
          type: string
        groupId:
          type: string
        title:
          type: string
        desc:
          type: string
        image:
          $ref: '#/components/schemas/Map'
        labelName:
          type: string
        labelColor:
          type: string
        url:
          type: string
        connectUrl:
          type: string
        dataUrl:
          type: string
        limit:
          type: integer
          format: int32
        progress:
          type: integer
          format: int32
        status:
          type: integer
          format: int32
        completeTime:
          type: integer
          format: int64
        endTime:
          type: integer
          format: int64
        domain:
          type: string
        btn:
          type: integer
          description: 0:不显示 1:go 2:verify
        showProgress:
          type: boolean
          description: 是否显示进度条
        link:
          $ref: '#/components/schemas/Map'
        code:
          type: string
        reward:
          type: number
        position:
          type: string
        cycle:
          type: string
        icon:
          type: string
        postTaskDesc:
          type: string
        postTaskReward:
          type: number
        postTaskCode:
          type: string
        postTaskStatus:
          type: integer
        predictJoinedCount:
          description: 未来预计参与人数
          type: integer
        joinedRecentUsers:
          description: 最近参与用户
          type: array
          items:
            type: string
        subTask:
          type: array
          items:
            $ref: '#/components/schemas/TaskSubVO'
        rewardFrequency:
          type: integer
          format: int32
        todayTaskStatus:
          type: integer
          format: int32

    Award:
      type: object
      properties:
        desc:
          type: string
          description: 奖品描述
        type:
          type: string
          description: NFT,TOKEN,POINT
        amount:
          type: string
          description: 奖励金额
        currency:
          type: string
        index:
          type: integer
        status:
          type: integer
          format: int32
          description: 1:已发放 0：未发放
    WebResultTaskDetailVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/TaskDetailVO'
    TaskDetailVO:
      type: object
      properties:
        taskId:
          type: string
        title:
          type: string
        desc:
          type: string
        login_url:
          type: string
        image:
          $ref: '#/components/schemas/Map'
        platform:
          type: string
        rewardType:
          type: string
        rewardWay:
          type: string
        subTasks:
          type: array
          items:
            $ref: '#/components/schemas/TaskSubVO'
        rewards:
          type: array
          items:
            $ref: '#/components/schemas/Award'
        labelName:
          type: string
        labelColor:
          type: string
        startTime:
          type: integer
          format: int64
        endTime:
          type: integer
          format: int64
        total:
          type: integer
        process:
          type: integer
        rewardStatus:
          type: integer
        attr:
          $ref: '#/components/schemas/Map'
        status:
          type: integer
          description: 任务完成状态 1-已完成 0-未完成
    TaskSubVO:
      type: object
      properties:
        taskId:
          type: string
        title:
          type: string
        desc:
          type: string
        reward:
          type: string
        domain:
          type: string
        url:
          type: string
        image:
          type: string
        status:
          type: integer
        platform:
          type: string
        code:
          type: string
        connectUrl:
          type: string
        rewardStatus:
          type: integer
        startTime:
          type: integer
          format: int64
        endTime:
          type: integer
          format: int64
        rewards:
          type: array
          items:
            $ref: '#/components/schemas/Award'

    WebResultSeriesTaskVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/TaskCodeVO'
    TaskCodeVO:
      type: object
      properties:
        checkIn:
          type: boolean
          title: 是否签过到
        taskId:
          type: string
          title: 任务id
        title:
          type: string
          title: 任务标题
        desc:
          type: string
          title: 任务描述
        nodes:
          type: array
          items:
            $ref: '#/components/schemas/NodeVO'
        process:
          type: integer
        image:
          type: array
          items:
            type: string
    NodeVO:
      type: object
      properties:
        index:
          type: integer
          title: 1
        nodeLogo:
          type: integer
          title: '0:无奖品 1:普通奖品'
    WebResultCheckInStatusVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/CheckInStatusVO'
    CheckInStatusVO:
      type: object
      properties:
        todayReward:
          type: string
          title: 今日签到奖励
          description: 今日签到奖励
        todayRewardType:
          type: string
          title: 今日签到奖励类型
          description: 今日签到奖励类型
        title:
          type: string
          title: 任务标题
          description: 任务标题
        desc:
          type: string
          title: 任务描述
        icon:
          type: string
          title: 任务图标
        checkIn:
          type: boolean
          title: 是否签到
          description: true 已签到，false 未签到
          example: true
          default: false
        #签到时间
        checkInTime:
          type: integer
          format: int64
          title: 签到时间
          example: 1640995200000
        #签到天数
        checkInDays:
          type: integer
          format: int32
          title: 签到天数
          example: 7
          default: 0
        #签到奖励
        checkInReward:
          type: array
          items:
            $ref: '#/components/schemas/Award'
servers:
  - url: 'http://api.quests.dev.dipbit.xyz'
    description: play-test
