openapi: 3.0.3
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Question API v2.0
  title: Question API
  version: '1.0'
tags:
  - name: QuestionV2
paths:
  /v2/question/me:
    get:
      summary: User sets details
      deprecated: false
      description: 'User details'
      tags:
        - QuestionV2
      parameters:
        - name: JWT_TOKEN
          in: header
          required: true
          description: JWT_TOKEN
          schema:
            type: string
        - name: saas_id
          in: header
          required: true
          description: saasId
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultUserVO'
  /v2/question/acquire:
    get:
      summary: Acquire a group of questions
      deprecated: false
      description: 'Acquire a group of questions'
      tags:
        - QuestionV2
      parameters:
        - name: JWT_TOKEN
          in: header
          required: true
          description: JWT_TOKEN
          schema:
            type: string
        - name: saas_id
          in: header
          required: true
          description: saasId
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultQuestionVO'
  /v2/question/submit:
    post:
      summary: Submit a group of questions
      deprecated: false
      description: 'Submit a group of questions'
      tags:
        - QuestionV2
      parameters:
        - name: JWT_TOKEN
          in: header
          required: true
          description: JWT_TOKEN
          schema:
            type: string
        - name: saas_id
          in: header
          required: true
          description: saasId
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserAnswerVO'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultSettleVO'
components:
  schemas:
    WebResult:
      type: object
      discriminator:
        propertyName: code
    Map:
      type: object
    WebResultUserVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/UserSetsVO'
    UserSetsVO:
      type: object
      properties:
        userId:
          type: string
        usedSets:
          type: integer
        availableSets:
          type: integer
        rewardRemainTime:
          type: integer
          format: int64
        inviteSucceedCount:
          type: integer
        todayCheckIn:
          type: boolean
        seriesCheckIn:
          type: boolean
        seriesCheckInCount:
          type: integer
    WebResultQuestionVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              type: array
              items:
                $ref: '#/components/schemas/QuestionListResponse'
    QuestionListResponse:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        option1:
          type: string
        option2:
          type: string
        imgUrl:
          type: string
        categories:
          type: array
          items:
            type: string
        userOption:
          type: string
        correctOption:
          type: string
        correct:
          type: boolean
    UserAnswerVO:
      type: object
      properties:
        spendTime:
          type: number
          description: Time spent answering the questions
        questions:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                description: The ID of the question
              title:
                type: string
                description: The question title
              option1:
                type: string
                description: The question option1
              option2:
                type: string
                description: The question option2
              userOption:
                type: string
                description: The userOption to the question
      required:
        - spendTime
        - questions
    WebResultSettleVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/SettleResponse'
    SettleResponse:
      type: object
      properties:
        correctCount:
          type: string
        correctScore:
          type: string
        errorCount:
          type: string
        errorScore:
          type: string
        spendTime:
          type: integer
          format: int64
        scoreAdditionMultiple:
          type: number
        score:
          type: number
        beforeScore:
          type: number
        totalScore:
          type: number
        usedSets:
          type: integer
        rewardSets:
          type: integer
        availableSets:
          type: integer
        rewardExp:
          type: number
        beforeExp:
          type: number
        totalExp:
          type: number
        questions:
          type: array
          items:
            $ref: '#/components/schemas/QuestionListResponse'

servers:
  - url: 'https://api.quests.dev.dipbit.xyz/'
    description: api-quests-dev