package com.kikitrade.activity.generated.model.s2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TaskFulfillVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TaskFulfillVO {

  @JsonProperty("status")
  private String status;

  @JsonProperty("completeTime")
  private Long completeTime;

  @JsonProperty("completeDays")
  private Integer completeDays;

  @JsonProperty("consecutiveDays")
  private Integer consecutiveDays;

  public TaskFulfillVO status(String status) {
    this.status = status;
    return this;
  }

  /**
   * 任务状态
   * @return status
  */
  
  @Schema(name = "status", description = "任务状态", required = false)
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public TaskFulfillVO completeTime(Long completeTime) {
    this.completeTime = completeTime;
    return this;
  }

  /**
   * 完成时间
   * @return completeTime
  */
  
  @Schema(name = "completeTime", description = "完成时间", required = false)
  public Long getCompleteTime() {
    return completeTime;
  }

  public void setCompleteTime(Long completeTime) {
    this.completeTime = completeTime;
  }

  public TaskFulfillVO completeDays(Integer completeDays) {
    this.completeDays = completeDays;
    return this;
  }

  /**
   * 完成天数
   * @return completeDays
  */
  
  @Schema(name = "completeDays", description = "完成天数", required = false)
  public Integer getCompleteDays() {
    return completeDays;
  }

  public void setCompleteDays(Integer completeDays) {
    this.completeDays = completeDays;
  }

  public TaskFulfillVO consecutiveDays(Integer consecutiveDays) {
    this.consecutiveDays = consecutiveDays;
    return this;
  }

  /**
   * 连续完成天数
   * @return consecutiveDays
  */
  
  @Schema(name = "consecutiveDays", description = "连续完成天数", required = false)
  public Integer getConsecutiveDays() {
    return consecutiveDays;
  }

  public void setConsecutiveDays(Integer consecutiveDays) {
    this.consecutiveDays = consecutiveDays;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskFulfillVO taskFulfillVO = (TaskFulfillVO) o;
    return Objects.equals(this.status, taskFulfillVO.status) &&
        Objects.equals(this.completeTime, taskFulfillVO.completeTime) &&
        Objects.equals(this.completeDays, taskFulfillVO.completeDays) &&
        Objects.equals(this.consecutiveDays, taskFulfillVO.consecutiveDays);
  }

  @Override
  public int hashCode() {
    return Objects.hash(status, completeTime, completeDays, consecutiveDays);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskFulfillVO {\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    completeTime: ").append(toIndentedString(completeTime)).append("\n");
    sb.append("    completeDays: ").append(toIndentedString(completeDays)).append("\n");
    sb.append("    consecutiveDays: ").append(toIndentedString(consecutiveDays)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

