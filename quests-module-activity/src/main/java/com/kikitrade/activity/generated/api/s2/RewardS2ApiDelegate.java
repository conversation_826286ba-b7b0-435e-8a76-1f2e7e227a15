package com.kikitrade.activity.generated.api.s2;

import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.activity.generated.model.s2.WebResultRewardResultVO;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link RewardS2ApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface RewardS2ApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * POST /s2/reward/grant : 发放积分
     * 
     *
     * @param customerId  (required)
     * @param amount  (required)
     * @param desc  (required)
     * @param saasId  (required)
     * @param authorization  (required)
     * @param businessId  (optional)
     * @return 成功 (status code 200)
     * @see RewardS2Api#s2RewardGrantPost
     */
    default ResponseEntity<WebResultRewardResultVO> s2RewardGrantPost(String customerId,
        String amount,
        String desc,
        String saasId,
        String authorization,
        String businessId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /s2/reward/receive : 领取奖励
     * 
     *
     * @param saasId  (required)
     * @param authorization  (required)
     * @param customerId  (required)
     * @param taskId  (required)
     * @param extendAttr 事件附加属性 (optional)
     * @return 成功 (status code 200)
     * @see RewardS2Api#s2RewardReceivePost
     */
    default ResponseEntity<WebResult> s2RewardReceivePost(String saasId,
        String authorization,
        String customerId,
        String taskId,
        String extendAttr) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
