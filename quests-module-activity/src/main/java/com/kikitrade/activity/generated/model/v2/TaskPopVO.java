package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TaskPopVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TaskPopVO {

  @JsonProperty("pop")
  private Boolean pop;

  @JsonProperty("taskId")
  private String taskId;

  public TaskPopVO pop(Boolean pop) {
    this.pop = pop;
    return this;
  }

  /**
   * Get pop
   * @return pop
  */
  
  @Schema(name = "pop", required = false)
  public Boolean getPop() {
    return pop;
  }

  public void setPop(Boolean pop) {
    this.pop = pop;
  }

  public TaskPopVO taskId(String taskId) {
    this.taskId = taskId;
    return this;
  }

  /**
   * 任务id
   * @return taskId
  */
  
  @Schema(name = "taskId", description = "任务id", required = false)
  public String getTaskId() {
    return taskId;
  }

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskPopVO taskPopVO = (TaskPopVO) o;
    return Objects.equals(this.pop, taskPopVO.pop) &&
        Objects.equals(this.taskId, taskPopVO.taskId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(pop, taskId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskPopVO {\n");
    sb.append("    pop: ").append(toIndentedString(pop)).append("\n");
    sb.append("    taskId: ").append(toIndentedString(taskId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

