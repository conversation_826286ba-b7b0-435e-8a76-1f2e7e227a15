package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * LadderRewardVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class LadderRewardVO {

  @JsonProperty("level")
  private Long level;

  @JsonProperty("minScoreForRankReward")
  private Long minScoreForRankReward;

  @JsonProperty("rankReward")
  private String rankReward;

  @JsonProperty("rankRewardCurrency")
  private String rankRewardCurrency;

  @JsonProperty("minScoreForWeeklyReward")
  private Long minScoreForWeeklyReward;

  @JsonProperty("weeklyReward")
  private String weeklyReward;

  @JsonProperty("weeklyRewardCurrency")
  private String weeklyRewardCurrency;

  public LadderRewardVO level(Long level) {
    this.level = level;
    return this;
  }

  /**
   * Get level
   * @return level
  */
  
  @Schema(name = "level", required = false)
  public Long getLevel() {
    return level;
  }

  public void setLevel(Long level) {
    this.level = level;
  }

  public LadderRewardVO minScoreForRankReward(Long minScoreForRankReward) {
    this.minScoreForRankReward = minScoreForRankReward;
    return this;
  }

  /**
   * Get minScoreForRankReward
   * @return minScoreForRankReward
  */
  
  @Schema(name = "minScoreForRankReward", required = false)
  public Long getMinScoreForRankReward() {
    return minScoreForRankReward;
  }

  public void setMinScoreForRankReward(Long minScoreForRankReward) {
    this.minScoreForRankReward = minScoreForRankReward;
  }

  public LadderRewardVO rankReward(String rankReward) {
    this.rankReward = rankReward;
    return this;
  }

  /**
   * Get rankReward
   * @return rankReward
  */
  
  @Schema(name = "rankReward", required = false)
  public String getRankReward() {
    return rankReward;
  }

  public void setRankReward(String rankReward) {
    this.rankReward = rankReward;
  }

  public LadderRewardVO rankRewardCurrency(String rankRewardCurrency) {
    this.rankRewardCurrency = rankRewardCurrency;
    return this;
  }

  /**
   * Get rankRewardCurrency
   * @return rankRewardCurrency
  */
  
  @Schema(name = "rankRewardCurrency", required = false)
  public String getRankRewardCurrency() {
    return rankRewardCurrency;
  }

  public void setRankRewardCurrency(String rankRewardCurrency) {
    this.rankRewardCurrency = rankRewardCurrency;
  }

  public LadderRewardVO minScoreForWeeklyReward(Long minScoreForWeeklyReward) {
    this.minScoreForWeeklyReward = minScoreForWeeklyReward;
    return this;
  }

  /**
   * Get minScoreForWeeklyReward
   * @return minScoreForWeeklyReward
  */
  
  @Schema(name = "minScoreForWeeklyReward", required = false)
  public Long getMinScoreForWeeklyReward() {
    return minScoreForWeeklyReward;
  }

  public void setMinScoreForWeeklyReward(Long minScoreForWeeklyReward) {
    this.minScoreForWeeklyReward = minScoreForWeeklyReward;
  }

  public LadderRewardVO weeklyReward(String weeklyReward) {
    this.weeklyReward = weeklyReward;
    return this;
  }

  /**
   * Get weeklyReward
   * @return weeklyReward
  */
  
  @Schema(name = "weeklyReward", required = false)
  public String getWeeklyReward() {
    return weeklyReward;
  }

  public void setWeeklyReward(String weeklyReward) {
    this.weeklyReward = weeklyReward;
  }

  public LadderRewardVO weeklyRewardCurrency(String weeklyRewardCurrency) {
    this.weeklyRewardCurrency = weeklyRewardCurrency;
    return this;
  }

  /**
   * Get weeklyRewardCurrency
   * @return weeklyRewardCurrency
  */
  
  @Schema(name = "weeklyRewardCurrency", required = false)
  public String getWeeklyRewardCurrency() {
    return weeklyRewardCurrency;
  }

  public void setWeeklyRewardCurrency(String weeklyRewardCurrency) {
    this.weeklyRewardCurrency = weeklyRewardCurrency;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LadderRewardVO ladderRewardVO = (LadderRewardVO) o;
    return Objects.equals(this.level, ladderRewardVO.level) &&
        Objects.equals(this.minScoreForRankReward, ladderRewardVO.minScoreForRankReward) &&
        Objects.equals(this.rankReward, ladderRewardVO.rankReward) &&
        Objects.equals(this.rankRewardCurrency, ladderRewardVO.rankRewardCurrency) &&
        Objects.equals(this.minScoreForWeeklyReward, ladderRewardVO.minScoreForWeeklyReward) &&
        Objects.equals(this.weeklyReward, ladderRewardVO.weeklyReward) &&
        Objects.equals(this.weeklyRewardCurrency, ladderRewardVO.weeklyRewardCurrency);
  }

  @Override
  public int hashCode() {
    return Objects.hash(level, minScoreForRankReward, rankReward, rankRewardCurrency, minScoreForWeeklyReward, weeklyReward, weeklyRewardCurrency);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LadderRewardVO {\n");
    sb.append("    level: ").append(toIndentedString(level)).append("\n");
    sb.append("    minScoreForRankReward: ").append(toIndentedString(minScoreForRankReward)).append("\n");
    sb.append("    rankReward: ").append(toIndentedString(rankReward)).append("\n");
    sb.append("    rankRewardCurrency: ").append(toIndentedString(rankRewardCurrency)).append("\n");
    sb.append("    minScoreForWeeklyReward: ").append(toIndentedString(minScoreForWeeklyReward)).append("\n");
    sb.append("    weeklyReward: ").append(toIndentedString(weeklyReward)).append("\n");
    sb.append("    weeklyRewardCurrency: ").append(toIndentedString(weeklyRewardCurrency)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

