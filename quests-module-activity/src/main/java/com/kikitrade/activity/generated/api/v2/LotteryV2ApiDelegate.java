package com.kikitrade.activity.generated.api.v2;

import com.kikitrade.activity.generated.model.v2.WebResultLotteryInfoVO;
import com.kikitrade.activity.generated.model.v2.WebResultLotteryVO;
import com.kikitrade.activity.generated.model.v2.WebResultLotteryWinnersVO;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link LotteryV2ApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface LotteryV2ApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * POST /v2/lottery/{code}/open : 抽奖
     * 抽奖
     *
     * @param code  (required)
     * @param saasId  (required)
     * @return Successful operation (status code 200)
     * @see LotteryV2Api#drew
     */
    default ResponseEntity<WebResultLotteryVO> drew(String code,
        String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/lottery/info : 奖池信息
     * 奖池信息
     *
     * @param saasId  (required)
     * @param code 多个code，以逗号分隔 (required)
     * @return Successful operation (status code 200)
     * @see LotteryV2Api#lotteryInfo
     */
    default ResponseEntity<WebResultLotteryInfoVO> lotteryInfo(String saasId,
        String code) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/lottery/winners : 中奖名单查询
     *
     * @param saasId  (required)
     * @param lotteryCode 抽奖活动code (required)
     * @return Successful operation (status code 200)
     * @see LotteryV2Api#lotteryWinners
     */
    default ResponseEntity<WebResultLotteryWinnersVO> lotteryWinners(String saasId,
        String lotteryCode) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
