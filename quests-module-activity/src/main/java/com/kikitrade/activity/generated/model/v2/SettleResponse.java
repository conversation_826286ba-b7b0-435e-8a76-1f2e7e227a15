package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.activity.generated.model.v2.QuestionListResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * SettleResponse
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class SettleResponse {

  @JsonProperty("correctCount")
  private String correctCount;

  @JsonProperty("correctScore")
  private String correctScore;

  @JsonProperty("errorCount")
  private String errorCount;

  @JsonProperty("errorScore")
  private String errorScore;

  @JsonProperty("spendTime")
  private Long spendTime;

  @JsonProperty("scoreAdditionMultiple")
  private BigDecimal scoreAdditionMultiple;

  @JsonProperty("score")
  private BigDecimal score;

  @JsonProperty("beforeScore")
  private BigDecimal beforeScore;

  @JsonProperty("totalScore")
  private BigDecimal totalScore;

  @JsonProperty("usedSets")
  private Integer usedSets;

  @JsonProperty("rewardSets")
  private Integer rewardSets;

  @JsonProperty("availableSets")
  private Integer availableSets;

  @JsonProperty("rewardExp")
  private BigDecimal rewardExp;

  @JsonProperty("beforeExp")
  private BigDecimal beforeExp;

  @JsonProperty("totalExp")
  private BigDecimal totalExp;

  @JsonProperty("questions")
  @Valid
  private List<QuestionListResponse> questions = null;

  public SettleResponse correctCount(String correctCount) {
    this.correctCount = correctCount;
    return this;
  }

  /**
   * Get correctCount
   * @return correctCount
  */
  
  @Schema(name = "correctCount", required = false)
  public String getCorrectCount() {
    return correctCount;
  }

  public void setCorrectCount(String correctCount) {
    this.correctCount = correctCount;
  }

  public SettleResponse correctScore(String correctScore) {
    this.correctScore = correctScore;
    return this;
  }

  /**
   * Get correctScore
   * @return correctScore
  */
  
  @Schema(name = "correctScore", required = false)
  public String getCorrectScore() {
    return correctScore;
  }

  public void setCorrectScore(String correctScore) {
    this.correctScore = correctScore;
  }

  public SettleResponse errorCount(String errorCount) {
    this.errorCount = errorCount;
    return this;
  }

  /**
   * Get errorCount
   * @return errorCount
  */
  
  @Schema(name = "errorCount", required = false)
  public String getErrorCount() {
    return errorCount;
  }

  public void setErrorCount(String errorCount) {
    this.errorCount = errorCount;
  }

  public SettleResponse errorScore(String errorScore) {
    this.errorScore = errorScore;
    return this;
  }

  /**
   * Get errorScore
   * @return errorScore
  */
  
  @Schema(name = "errorScore", required = false)
  public String getErrorScore() {
    return errorScore;
  }

  public void setErrorScore(String errorScore) {
    this.errorScore = errorScore;
  }

  public SettleResponse spendTime(Long spendTime) {
    this.spendTime = spendTime;
    return this;
  }

  /**
   * Get spendTime
   * @return spendTime
  */
  
  @Schema(name = "spendTime", required = false)
  public Long getSpendTime() {
    return spendTime;
  }

  public void setSpendTime(Long spendTime) {
    this.spendTime = spendTime;
  }

  public SettleResponse scoreAdditionMultiple(BigDecimal scoreAdditionMultiple) {
    this.scoreAdditionMultiple = scoreAdditionMultiple;
    return this;
  }

  /**
   * Get scoreAdditionMultiple
   * @return scoreAdditionMultiple
  */
  @Valid 
  @Schema(name = "scoreAdditionMultiple", required = false)
  public BigDecimal getScoreAdditionMultiple() {
    return scoreAdditionMultiple;
  }

  public void setScoreAdditionMultiple(BigDecimal scoreAdditionMultiple) {
    this.scoreAdditionMultiple = scoreAdditionMultiple;
  }

  public SettleResponse score(BigDecimal score) {
    this.score = score;
    return this;
  }

  /**
   * Get score
   * @return score
  */
  @Valid 
  @Schema(name = "score", required = false)
  public BigDecimal getScore() {
    return score;
  }

  public void setScore(BigDecimal score) {
    this.score = score;
  }

  public SettleResponse beforeScore(BigDecimal beforeScore) {
    this.beforeScore = beforeScore;
    return this;
  }

  /**
   * Get beforeScore
   * @return beforeScore
  */
  @Valid 
  @Schema(name = "beforeScore", required = false)
  public BigDecimal getBeforeScore() {
    return beforeScore;
  }

  public void setBeforeScore(BigDecimal beforeScore) {
    this.beforeScore = beforeScore;
  }

  public SettleResponse totalScore(BigDecimal totalScore) {
    this.totalScore = totalScore;
    return this;
  }

  /**
   * Get totalScore
   * @return totalScore
  */
  @Valid 
  @Schema(name = "totalScore", required = false)
  public BigDecimal getTotalScore() {
    return totalScore;
  }

  public void setTotalScore(BigDecimal totalScore) {
    this.totalScore = totalScore;
  }

  public SettleResponse usedSets(Integer usedSets) {
    this.usedSets = usedSets;
    return this;
  }

  /**
   * Get usedSets
   * @return usedSets
  */
  
  @Schema(name = "usedSets", required = false)
  public Integer getUsedSets() {
    return usedSets;
  }

  public void setUsedSets(Integer usedSets) {
    this.usedSets = usedSets;
  }

  public SettleResponse rewardSets(Integer rewardSets) {
    this.rewardSets = rewardSets;
    return this;
  }

  /**
   * Get rewardSets
   * @return rewardSets
  */
  
  @Schema(name = "rewardSets", required = false)
  public Integer getRewardSets() {
    return rewardSets;
  }

  public void setRewardSets(Integer rewardSets) {
    this.rewardSets = rewardSets;
  }

  public SettleResponse availableSets(Integer availableSets) {
    this.availableSets = availableSets;
    return this;
  }

  /**
   * Get availableSets
   * @return availableSets
  */
  
  @Schema(name = "availableSets", required = false)
  public Integer getAvailableSets() {
    return availableSets;
  }

  public void setAvailableSets(Integer availableSets) {
    this.availableSets = availableSets;
  }

  public SettleResponse rewardExp(BigDecimal rewardExp) {
    this.rewardExp = rewardExp;
    return this;
  }

  /**
   * Get rewardExp
   * @return rewardExp
  */
  @Valid 
  @Schema(name = "rewardExp", required = false)
  public BigDecimal getRewardExp() {
    return rewardExp;
  }

  public void setRewardExp(BigDecimal rewardExp) {
    this.rewardExp = rewardExp;
  }

  public SettleResponse beforeExp(BigDecimal beforeExp) {
    this.beforeExp = beforeExp;
    return this;
  }

  /**
   * Get beforeExp
   * @return beforeExp
  */
  @Valid 
  @Schema(name = "beforeExp", required = false)
  public BigDecimal getBeforeExp() {
    return beforeExp;
  }

  public void setBeforeExp(BigDecimal beforeExp) {
    this.beforeExp = beforeExp;
  }

  public SettleResponse totalExp(BigDecimal totalExp) {
    this.totalExp = totalExp;
    return this;
  }

  /**
   * Get totalExp
   * @return totalExp
  */
  @Valid 
  @Schema(name = "totalExp", required = false)
  public BigDecimal getTotalExp() {
    return totalExp;
  }

  public void setTotalExp(BigDecimal totalExp) {
    this.totalExp = totalExp;
  }

  public SettleResponse questions(List<QuestionListResponse> questions) {
    this.questions = questions;
    return this;
  }

  public SettleResponse addQuestionsItem(QuestionListResponse questionsItem) {
    if (this.questions == null) {
      this.questions = new ArrayList<>();
    }
    this.questions.add(questionsItem);
    return this;
  }

  /**
   * Get questions
   * @return questions
  */
  @Valid 
  @Schema(name = "questions", required = false)
  public List<QuestionListResponse> getQuestions() {
    return questions;
  }

  public void setQuestions(List<QuestionListResponse> questions) {
    this.questions = questions;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SettleResponse settleResponse = (SettleResponse) o;
    return Objects.equals(this.correctCount, settleResponse.correctCount) &&
        Objects.equals(this.correctScore, settleResponse.correctScore) &&
        Objects.equals(this.errorCount, settleResponse.errorCount) &&
        Objects.equals(this.errorScore, settleResponse.errorScore) &&
        Objects.equals(this.spendTime, settleResponse.spendTime) &&
        Objects.equals(this.scoreAdditionMultiple, settleResponse.scoreAdditionMultiple) &&
        Objects.equals(this.score, settleResponse.score) &&
        Objects.equals(this.beforeScore, settleResponse.beforeScore) &&
        Objects.equals(this.totalScore, settleResponse.totalScore) &&
        Objects.equals(this.usedSets, settleResponse.usedSets) &&
        Objects.equals(this.rewardSets, settleResponse.rewardSets) &&
        Objects.equals(this.availableSets, settleResponse.availableSets) &&
        Objects.equals(this.rewardExp, settleResponse.rewardExp) &&
        Objects.equals(this.beforeExp, settleResponse.beforeExp) &&
        Objects.equals(this.totalExp, settleResponse.totalExp) &&
        Objects.equals(this.questions, settleResponse.questions);
  }

  @Override
  public int hashCode() {
    return Objects.hash(correctCount, correctScore, errorCount, errorScore, spendTime, scoreAdditionMultiple, score, beforeScore, totalScore, usedSets, rewardSets, availableSets, rewardExp, beforeExp, totalExp, questions);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SettleResponse {\n");
    sb.append("    correctCount: ").append(toIndentedString(correctCount)).append("\n");
    sb.append("    correctScore: ").append(toIndentedString(correctScore)).append("\n");
    sb.append("    errorCount: ").append(toIndentedString(errorCount)).append("\n");
    sb.append("    errorScore: ").append(toIndentedString(errorScore)).append("\n");
    sb.append("    spendTime: ").append(toIndentedString(spendTime)).append("\n");
    sb.append("    scoreAdditionMultiple: ").append(toIndentedString(scoreAdditionMultiple)).append("\n");
    sb.append("    score: ").append(toIndentedString(score)).append("\n");
    sb.append("    beforeScore: ").append(toIndentedString(beforeScore)).append("\n");
    sb.append("    totalScore: ").append(toIndentedString(totalScore)).append("\n");
    sb.append("    usedSets: ").append(toIndentedString(usedSets)).append("\n");
    sb.append("    rewardSets: ").append(toIndentedString(rewardSets)).append("\n");
    sb.append("    availableSets: ").append(toIndentedString(availableSets)).append("\n");
    sb.append("    rewardExp: ").append(toIndentedString(rewardExp)).append("\n");
    sb.append("    beforeExp: ").append(toIndentedString(beforeExp)).append("\n");
    sb.append("    totalExp: ").append(toIndentedString(totalExp)).append("\n");
    sb.append("    questions: ").append(toIndentedString(questions)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

