package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.kikitrade.activity.generated.model.v2.WebResultQuestionVO;
import com.kikitrade.activity.generated.model.v2.WebResultSettleVO;
import com.kikitrade.activity.generated.model.v2.WebResultUserVO;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * WebResult
 */

@JsonIgnoreProperties(
  value = "code", // ignore manually set code, it will be automatically generated by <PERSON> during serialization
  allowSetters = true // allows the code to be set during deserialization
)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "code", visible = true)
@JsonSubTypes({
  @JsonSubTypes.Type(value = WebResultQuestionVO.class, name = "WebResultQuestionVO"),
  @JsonSubTypes.Type(value = WebResultSettleVO.class, name = "WebResultSettleVO"),
  @JsonSubTypes.Type(value = WebResultUserVO.class, name = "WebResultUserVO")
})

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class WebResult {

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    return true;
  }

  @Override
  public int hashCode() {
    return Objects.hash();
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WebResult {\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

