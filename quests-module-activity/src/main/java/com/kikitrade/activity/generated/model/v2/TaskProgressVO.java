package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TaskProgressVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TaskProgressVO {

  @JsonProperty("status")
  private Integer status;

  @JsonProperty("limit")
  private Integer limit;

  @JsonProperty("process")
  private Integer process;

  public TaskProgressVO status(Integer status) {
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
  */
  
  @Schema(name = "status", required = false)
  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public TaskProgressVO limit(Integer limit) {
    this.limit = limit;
    return this;
  }

  /**
   * 暂时无效
   * @return limit
  */
  
  @Schema(name = "limit", description = "暂时无效", required = false)
  public Integer getLimit() {
    return limit;
  }

  public void setLimit(Integer limit) {
    this.limit = limit;
  }

  public TaskProgressVO process(Integer process) {
    this.process = process;
    return this;
  }

  /**
   * 暂时无效
   * @return process
  */
  
  @Schema(name = "process", description = "暂时无效", required = false)
  public Integer getProcess() {
    return process;
  }

  public void setProcess(Integer process) {
    this.process = process;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskProgressVO taskProgressVO = (TaskProgressVO) o;
    return Objects.equals(this.status, taskProgressVO.status) &&
        Objects.equals(this.limit, taskProgressVO.limit) &&
        Objects.equals(this.process, taskProgressVO.process);
  }

  @Override
  public int hashCode() {
    return Objects.hash(status, limit, process);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskProgressVO {\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    limit: ").append(toIndentedString(limit)).append("\n");
    sb.append("    process: ").append(toIndentedString(process)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

