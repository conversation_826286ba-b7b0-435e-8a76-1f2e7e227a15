package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * QuestionListResponse
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class QuestionListResponse {

  @JsonProperty("id")
  private String id;

  @JsonProperty("title")
  private String title;

  @JsonProperty("option1")
  private String option1;

  @JsonProperty("option2")
  private String option2;

  @JsonProperty("imgUrl")
  private String imgUrl;

  @JsonProperty("categories")
  @Valid
  private List<String> categories = null;

  @JsonProperty("userOption")
  private String userOption;

  @JsonProperty("correctOption")
  private String correctOption;

  @JsonProperty("correct")
  private Boolean correct;

  public QuestionListResponse id(String id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  
  @Schema(name = "id", required = false)
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public QuestionListResponse title(String title) {
    this.title = title;
    return this;
  }

  /**
   * Get title
   * @return title
  */
  
  @Schema(name = "title", required = false)
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public QuestionListResponse option1(String option1) {
    this.option1 = option1;
    return this;
  }

  /**
   * Get option1
   * @return option1
  */
  
  @Schema(name = "option1", required = false)
  public String getOption1() {
    return option1;
  }

  public void setOption1(String option1) {
    this.option1 = option1;
  }

  public QuestionListResponse option2(String option2) {
    this.option2 = option2;
    return this;
  }

  /**
   * Get option2
   * @return option2
  */
  
  @Schema(name = "option2", required = false)
  public String getOption2() {
    return option2;
  }

  public void setOption2(String option2) {
    this.option2 = option2;
  }

  public QuestionListResponse imgUrl(String imgUrl) {
    this.imgUrl = imgUrl;
    return this;
  }

  /**
   * Get imgUrl
   * @return imgUrl
  */
  
  @Schema(name = "imgUrl", required = false)
  public String getImgUrl() {
    return imgUrl;
  }

  public void setImgUrl(String imgUrl) {
    this.imgUrl = imgUrl;
  }

  public QuestionListResponse categories(List<String> categories) {
    this.categories = categories;
    return this;
  }

  public QuestionListResponse addCategoriesItem(String categoriesItem) {
    if (this.categories == null) {
      this.categories = new ArrayList<>();
    }
    this.categories.add(categoriesItem);
    return this;
  }

  /**
   * Get categories
   * @return categories
  */
  
  @Schema(name = "categories", required = false)
  public List<String> getCategories() {
    return categories;
  }

  public void setCategories(List<String> categories) {
    this.categories = categories;
  }

  public QuestionListResponse userOption(String userOption) {
    this.userOption = userOption;
    return this;
  }

  /**
   * Get userOption
   * @return userOption
  */
  
  @Schema(name = "userOption", required = false)
  public String getUserOption() {
    return userOption;
  }

  public void setUserOption(String userOption) {
    this.userOption = userOption;
  }

  public QuestionListResponse correctOption(String correctOption) {
    this.correctOption = correctOption;
    return this;
  }

  /**
   * Get correctOption
   * @return correctOption
  */
  
  @Schema(name = "correctOption", required = false)
  public String getCorrectOption() {
    return correctOption;
  }

  public void setCorrectOption(String correctOption) {
    this.correctOption = correctOption;
  }

  public QuestionListResponse correct(Boolean correct) {
    this.correct = correct;
    return this;
  }

  /**
   * Get correct
   * @return correct
  */
  
  @Schema(name = "correct", required = false)
  public Boolean getCorrect() {
    return correct;
  }

  public void setCorrect(Boolean correct) {
    this.correct = correct;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    QuestionListResponse questionListResponse = (QuestionListResponse) o;
    return Objects.equals(this.id, questionListResponse.id) &&
        Objects.equals(this.title, questionListResponse.title) &&
        Objects.equals(this.option1, questionListResponse.option1) &&
        Objects.equals(this.option2, questionListResponse.option2) &&
        Objects.equals(this.imgUrl, questionListResponse.imgUrl) &&
        Objects.equals(this.categories, questionListResponse.categories) &&
        Objects.equals(this.userOption, questionListResponse.userOption) &&
        Objects.equals(this.correctOption, questionListResponse.correctOption) &&
        Objects.equals(this.correct, questionListResponse.correct);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, title, option1, option2, imgUrl, categories, userOption, correctOption, correct);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QuestionListResponse {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    option1: ").append(toIndentedString(option1)).append("\n");
    sb.append("    option2: ").append(toIndentedString(option2)).append("\n");
    sb.append("    imgUrl: ").append(toIndentedString(imgUrl)).append("\n");
    sb.append("    categories: ").append(toIndentedString(categories)).append("\n");
    sb.append("    userOption: ").append(toIndentedString(userOption)).append("\n");
    sb.append("    correctOption: ").append(toIndentedString(correctOption)).append("\n");
    sb.append("    correct: ").append(toIndentedString(correct)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

