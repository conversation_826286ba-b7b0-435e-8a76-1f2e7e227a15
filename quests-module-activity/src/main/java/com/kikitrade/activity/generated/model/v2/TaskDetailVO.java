package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.activity.generated.model.v2.Award;
import com.kikitrade.activity.generated.model.v2.TaskSubVO;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TaskDetailVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TaskDetailVO {

  @JsonProperty("taskId")
  private String taskId;

  @JsonProperty("title")
  private String title;

  @JsonProperty("desc")
  private String desc;

  @JsonProperty("login_url")
  private String loginUrl;

  @JsonProperty("image")
  private Map image;

  @JsonProperty("platform")
  private String platform;

  @JsonProperty("rewardType")
  private String rewardType;

  @JsonProperty("rewardWay")
  private String rewardWay;

  @JsonProperty("subTasks")
  @Valid
  private List<TaskSubVO> subTasks = null;

  @JsonProperty("rewards")
  @Valid
  private List<Award> rewards = null;

  @JsonProperty("labelName")
  private String labelName;

  @JsonProperty("labelColor")
  private String labelColor;

  @JsonProperty("startTime")
  private Long startTime;

  @JsonProperty("endTime")
  private Long endTime;

  @JsonProperty("total")
  private Integer total;

  @JsonProperty("process")
  private Integer process;

  @JsonProperty("rewardStatus")
  private Integer rewardStatus;

  @JsonProperty("attr")
  private Map attr;

  @JsonProperty("status")
  private Integer status;

  public TaskDetailVO taskId(String taskId) {
    this.taskId = taskId;
    return this;
  }

  /**
   * Get taskId
   * @return taskId
  */
  
  @Schema(name = "taskId", required = false)
  public String getTaskId() {
    return taskId;
  }

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  public TaskDetailVO title(String title) {
    this.title = title;
    return this;
  }

  /**
   * Get title
   * @return title
  */
  
  @Schema(name = "title", required = false)
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public TaskDetailVO desc(String desc) {
    this.desc = desc;
    return this;
  }

  /**
   * Get desc
   * @return desc
  */
  
  @Schema(name = "desc", required = false)
  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public TaskDetailVO loginUrl(String loginUrl) {
    this.loginUrl = loginUrl;
    return this;
  }

  /**
   * Get loginUrl
   * @return loginUrl
  */
  
  @Schema(name = "login_url", required = false)
  public String getLoginUrl() {
    return loginUrl;
  }

  public void setLoginUrl(String loginUrl) {
    this.loginUrl = loginUrl;
  }

  public TaskDetailVO image(Map image) {
    this.image = image;
    return this;
  }

  /**
   * Get image
   * @return image
  */
  @Valid 
  @Schema(name = "image", required = false)
  public Map getImage() {
    return image;
  }

  public void setImage(Map image) {
    this.image = image;
  }

  public TaskDetailVO platform(String platform) {
    this.platform = platform;
    return this;
  }

  /**
   * Get platform
   * @return platform
  */
  
  @Schema(name = "platform", required = false)
  public String getPlatform() {
    return platform;
  }

  public void setPlatform(String platform) {
    this.platform = platform;
  }

  public TaskDetailVO rewardType(String rewardType) {
    this.rewardType = rewardType;
    return this;
  }

  /**
   * Get rewardType
   * @return rewardType
  */
  
  @Schema(name = "rewardType", required = false)
  public String getRewardType() {
    return rewardType;
  }

  public void setRewardType(String rewardType) {
    this.rewardType = rewardType;
  }

  public TaskDetailVO rewardWay(String rewardWay) {
    this.rewardWay = rewardWay;
    return this;
  }

  /**
   * Get rewardWay
   * @return rewardWay
  */
  
  @Schema(name = "rewardWay", required = false)
  public String getRewardWay() {
    return rewardWay;
  }

  public void setRewardWay(String rewardWay) {
    this.rewardWay = rewardWay;
  }

  public TaskDetailVO subTasks(List<TaskSubVO> subTasks) {
    this.subTasks = subTasks;
    return this;
  }

  public TaskDetailVO addSubTasksItem(TaskSubVO subTasksItem) {
    if (this.subTasks == null) {
      this.subTasks = new ArrayList<>();
    }
    this.subTasks.add(subTasksItem);
    return this;
  }

  /**
   * Get subTasks
   * @return subTasks
  */
  @Valid 
  @Schema(name = "subTasks", required = false)
  public List<TaskSubVO> getSubTasks() {
    return subTasks;
  }

  public void setSubTasks(List<TaskSubVO> subTasks) {
    this.subTasks = subTasks;
  }

  public TaskDetailVO rewards(List<Award> rewards) {
    this.rewards = rewards;
    return this;
  }

  public TaskDetailVO addRewardsItem(Award rewardsItem) {
    if (this.rewards == null) {
      this.rewards = new ArrayList<>();
    }
    this.rewards.add(rewardsItem);
    return this;
  }

  /**
   * Get rewards
   * @return rewards
  */
  @Valid 
  @Schema(name = "rewards", required = false)
  public List<Award> getRewards() {
    return rewards;
  }

  public void setRewards(List<Award> rewards) {
    this.rewards = rewards;
  }

  public TaskDetailVO labelName(String labelName) {
    this.labelName = labelName;
    return this;
  }

  /**
   * Get labelName
   * @return labelName
  */
  
  @Schema(name = "labelName", required = false)
  public String getLabelName() {
    return labelName;
  }

  public void setLabelName(String labelName) {
    this.labelName = labelName;
  }

  public TaskDetailVO labelColor(String labelColor) {
    this.labelColor = labelColor;
    return this;
  }

  /**
   * Get labelColor
   * @return labelColor
  */
  
  @Schema(name = "labelColor", required = false)
  public String getLabelColor() {
    return labelColor;
  }

  public void setLabelColor(String labelColor) {
    this.labelColor = labelColor;
  }

  public TaskDetailVO startTime(Long startTime) {
    this.startTime = startTime;
    return this;
  }

  /**
   * Get startTime
   * @return startTime
  */
  
  @Schema(name = "startTime", required = false)
  public Long getStartTime() {
    return startTime;
  }

  public void setStartTime(Long startTime) {
    this.startTime = startTime;
  }

  public TaskDetailVO endTime(Long endTime) {
    this.endTime = endTime;
    return this;
  }

  /**
   * Get endTime
   * @return endTime
  */
  
  @Schema(name = "endTime", required = false)
  public Long getEndTime() {
    return endTime;
  }

  public void setEndTime(Long endTime) {
    this.endTime = endTime;
  }

  public TaskDetailVO total(Integer total) {
    this.total = total;
    return this;
  }

  /**
   * Get total
   * @return total
  */
  
  @Schema(name = "total", required = false)
  public Integer getTotal() {
    return total;
  }

  public void setTotal(Integer total) {
    this.total = total;
  }

  public TaskDetailVO process(Integer process) {
    this.process = process;
    return this;
  }

  /**
   * Get process
   * @return process
  */
  
  @Schema(name = "process", required = false)
  public Integer getProcess() {
    return process;
  }

  public void setProcess(Integer process) {
    this.process = process;
  }

  public TaskDetailVO rewardStatus(Integer rewardStatus) {
    this.rewardStatus = rewardStatus;
    return this;
  }

  /**
   * Get rewardStatus
   * @return rewardStatus
  */
  
  @Schema(name = "rewardStatus", required = false)
  public Integer getRewardStatus() {
    return rewardStatus;
  }

  public void setRewardStatus(Integer rewardStatus) {
    this.rewardStatus = rewardStatus;
  }

  public TaskDetailVO attr(Map attr) {
    this.attr = attr;
    return this;
  }

  /**
   * Get attr
   * @return attr
  */
  @Valid 
  @Schema(name = "attr", required = false)
  public Map getAttr() {
    return attr;
  }

  public void setAttr(Map attr) {
    this.attr = attr;
  }

  public TaskDetailVO status(Integer status) {
    this.status = status;
    return this;
  }

  /**
   * 任务完成状态 1-已完成 0-未完成
   * @return status
  */
  
  @Schema(name = "status", description = "任务完成状态 1-已完成 0-未完成", required = false)
  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskDetailVO taskDetailVO = (TaskDetailVO) o;
    return Objects.equals(this.taskId, taskDetailVO.taskId) &&
        Objects.equals(this.title, taskDetailVO.title) &&
        Objects.equals(this.desc, taskDetailVO.desc) &&
        Objects.equals(this.loginUrl, taskDetailVO.loginUrl) &&
        Objects.equals(this.image, taskDetailVO.image) &&
        Objects.equals(this.platform, taskDetailVO.platform) &&
        Objects.equals(this.rewardType, taskDetailVO.rewardType) &&
        Objects.equals(this.rewardWay, taskDetailVO.rewardWay) &&
        Objects.equals(this.subTasks, taskDetailVO.subTasks) &&
        Objects.equals(this.rewards, taskDetailVO.rewards) &&
        Objects.equals(this.labelName, taskDetailVO.labelName) &&
        Objects.equals(this.labelColor, taskDetailVO.labelColor) &&
        Objects.equals(this.startTime, taskDetailVO.startTime) &&
        Objects.equals(this.endTime, taskDetailVO.endTime) &&
        Objects.equals(this.total, taskDetailVO.total) &&
        Objects.equals(this.process, taskDetailVO.process) &&
        Objects.equals(this.rewardStatus, taskDetailVO.rewardStatus) &&
        Objects.equals(this.attr, taskDetailVO.attr) &&
        Objects.equals(this.status, taskDetailVO.status);
  }

  @Override
  public int hashCode() {
    return Objects.hash(taskId, title, desc, loginUrl, image, platform, rewardType, rewardWay, subTasks, rewards, labelName, labelColor, startTime, endTime, total, process, rewardStatus, attr, status);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskDetailVO {\n");
    sb.append("    taskId: ").append(toIndentedString(taskId)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    desc: ").append(toIndentedString(desc)).append("\n");
    sb.append("    loginUrl: ").append(toIndentedString(loginUrl)).append("\n");
    sb.append("    image: ").append(toIndentedString(image)).append("\n");
    sb.append("    platform: ").append(toIndentedString(platform)).append("\n");
    sb.append("    rewardType: ").append(toIndentedString(rewardType)).append("\n");
    sb.append("    rewardWay: ").append(toIndentedString(rewardWay)).append("\n");
    sb.append("    subTasks: ").append(toIndentedString(subTasks)).append("\n");
    sb.append("    rewards: ").append(toIndentedString(rewards)).append("\n");
    sb.append("    labelName: ").append(toIndentedString(labelName)).append("\n");
    sb.append("    labelColor: ").append(toIndentedString(labelColor)).append("\n");
    sb.append("    startTime: ").append(toIndentedString(startTime)).append("\n");
    sb.append("    endTime: ").append(toIndentedString(endTime)).append("\n");
    sb.append("    total: ").append(toIndentedString(total)).append("\n");
    sb.append("    process: ").append(toIndentedString(process)).append("\n");
    sb.append("    rewardStatus: ").append(toIndentedString(rewardStatus)).append("\n");
    sb.append("    attr: ").append(toIndentedString(attr)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

