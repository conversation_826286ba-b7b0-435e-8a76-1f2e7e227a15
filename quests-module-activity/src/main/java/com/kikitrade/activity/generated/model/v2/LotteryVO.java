package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * LotteryVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class LotteryVO {

  @JsonProperty("reward")
  private BigDecimal reward;

  @JsonProperty("currency")
  private String currency;

  @JsonProperty("orderId")
  private String orderId;

  public LotteryVO reward(BigDecimal reward) {
    this.reward = reward;
    return this;
  }

  /**
   * 奖励金额
   * @return reward
  */
  @Valid 
  @Schema(name = "reward", description = "奖励金额", required = false)
  public BigDecimal getReward() {
    return reward;
  }

  public void setReward(BigDecimal reward) {
    this.reward = reward;
  }

  public LotteryVO currency(String currency) {
    this.currency = currency;
    return this;
  }

  /**
   * 奖励币种
   * @return currency
  */
  
  @Schema(name = "currency", description = "奖励币种", required = false)
  public String getCurrency() {
    return currency;
  }

  public void setCurrency(String currency) {
    this.currency = currency;
  }

  public LotteryVO orderId(String orderId) {
    this.orderId = orderId;
    return this;
  }

  /**
   * 订单id，如果需要支付时产生订单id
   * @return orderId
  */
  
  @Schema(name = "orderId", description = "订单id，如果需要支付时产生订单id", required = false)
  public String getOrderId() {
    return orderId;
  }

  public void setOrderId(String orderId) {
    this.orderId = orderId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LotteryVO lotteryVO = (LotteryVO) o;
    return Objects.equals(this.reward, lotteryVO.reward) &&
        Objects.equals(this.currency, lotteryVO.currency) &&
        Objects.equals(this.orderId, lotteryVO.orderId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(reward, currency, orderId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LotteryVO {\n");
    sb.append("    reward: ").append(toIndentedString(reward)).append("\n");
    sb.append("    currency: ").append(toIndentedString(currency)).append("\n");
    sb.append("    orderId: ").append(toIndentedString(orderId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

