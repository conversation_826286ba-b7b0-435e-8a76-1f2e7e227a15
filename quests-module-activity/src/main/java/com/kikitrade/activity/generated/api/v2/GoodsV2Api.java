/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.0.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.kikitrade.activity.generated.api.v2;

import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.activity.generated.model.v2.WebResultGoodsListVO;
import com.kikitrade.activity.generated.model.v2.WebResultGoodsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "GoodsV2", description = "the GoodsV2 API")
public interface GoodsV2Api {

    default GoodsV2ApiDelegate getDelegate() {
        return new GoodsV2ApiDelegate() {};
    }

    /**
     * GET /v2/goods/detail : 商品详情
     * 
     *
     * @param goodsId 商品id (optional)
     * @param saasId saas_id (optional)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2GoodsDetailGet",
        summary = "商品详情",
        tags = { "GoodsV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultGoodsVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/goods/detail",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultGoodsVO> v2GoodsDetailGet(
        @Parameter(name = "goodsId", description = "商品id") @Valid @RequestParam(value = "goodsId", required = false) String goodsId,
        @Parameter(name = "saas_id", description = "saas_id") @RequestHeader(value = "saas_id", required = false) String saasId
    ) {
        return getDelegate().v2GoodsDetailGet(goodsId, saasId);
    }


    /**
     * GET /v2/goods/list : 商品列表
     * 
     *
     * @param offset  (optional)
     * @param limit  (optional, default to 8)
     * @param saasId saas_id (optional)
     * @param exclude exclude (optional)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2GoodsListGet",
        summary = "商品列表",
        tags = { "GoodsV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultGoodsListVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/goods/list",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultGoodsListVO> v2GoodsListGet(
        @Parameter(name = "offset", description = "") @Valid @RequestParam(value = "offset", required = false) Integer offset,
        @Parameter(name = "limit", description = "") @Valid @RequestParam(value = "limit", required = false, defaultValue = "8") Integer limit,
        @Parameter(name = "saas_id", description = "saas_id") @RequestHeader(value = "saas_id", required = false) String saasId,
        @Parameter(name = "exclude", description = "exclude") @Valid @RequestParam(value = "exclude", required = false) String exclude
    ) {
        return getDelegate().v2GoodsListGet(offset, limit, saasId, exclude);
    }


    /**
     * POST /v2/goods/redeem : 兑换商品
     * 
     *
     * @param goodsId  (required)
     * @param totalPrice  (required)
     * @param quality  (required)
     * @param timestamp  (required)
     * @param sign  (required)
     * @param saasId saas_id (optional)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2GoodsRedeemPost",
        summary = "兑换商品",
        tags = { "GoodsV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResult.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/goods/redeem",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResult> v2GoodsRedeemPost(
        @NotNull @Parameter(name = "goodsId", description = "", required = true) @Valid @RequestParam(value = "goodsId", required = true) String goodsId,
        @NotNull @Parameter(name = "totalPrice", description = "", required = true) @Valid @RequestParam(value = "totalPrice", required = true) Integer totalPrice,
        @NotNull @Parameter(name = "quality", description = "", required = true) @Valid @RequestParam(value = "quality", required = true) Integer quality,
        @NotNull @Parameter(name = "timestamp", description = "", required = true) @Valid @RequestParam(value = "timestamp", required = true) Long timestamp,
        @NotNull @Parameter(name = "sign", description = "", required = true) @Valid @RequestParam(value = "sign", required = true) String sign,
        @Parameter(name = "saas_id", description = "saas_id") @RequestHeader(value = "saas_id", required = false) String saasId
    ) {
        return getDelegate().v2GoodsRedeemPost(goodsId, totalPrice, quality, timestamp, sign, saasId);
    }

}
