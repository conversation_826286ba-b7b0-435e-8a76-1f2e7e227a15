package com.kikitrade.activity.generated.api.v2;

import com.kikitrade.activity.generated.model.v2.WebResultActiveTaskVO;
import com.kikitrade.activity.generated.model.v2.WebResultActiveTaskVOS;
import com.kikitrade.activity.generated.model.v2.WebResultCheckInStatusVO;
import com.kikitrade.activity.generated.model.v2.WebResultDoTaskVO;
import com.kikitrade.activity.generated.model.v2.WebResultLadderRewardVO;
import com.kikitrade.activity.generated.model.v2.WebResultSeriesTaskVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskDetailVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskPopVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskProgressVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskRewardStatVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskRewardVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskTweetVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskVO;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link TaskV2ApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface TaskV2ApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * POST /v2/tasks/checkin : check in
     * check in
     *
     * @param saasId saasId (required)
     * @param idToken idToken (optional)
     * @param jwtToken jwt_token (optional)
     * @return success (status code 200)
     * @see TaskV2Api#checkIn
     */
    default ResponseEntity<WebResultActiveTaskVO> checkIn(String saasId,
        String idToken,
        String jwtToken) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/tasks/checkin/status : check in
     * check in
     *
     * @param saasId saasId (required)
     * @param idToken idToken (optional)
     * @param jwtToken jwt_token (optional)
     * @return success (status code 200)
     * @see TaskV2Api#checkInStatus
     */
    default ResponseEntity<WebResultCheckInStatusVO> checkInStatus(String saasId,
        String idToken,
        String jwtToken) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/tasks/code/{code} : 根据code查询任务详情
     * 
     *
     * @param code  (required)
     * @return 成功 (status code 200)
     * @see TaskV2Api#v2TasksCodeCodeGet
     */
    default ResponseEntity<WebResultSeriesTaskVO> v2TasksCodeCodeGet(String code) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/tasks/detail : 任务详情
     * 
     *
     * @param taskId 任务id (optional)
     * @return 成功 (status code 200)
     * @see TaskV2Api#v2TasksDetailGet
     */
    default ResponseEntity<WebResultTaskDetailVO> v2TasksDetailGet(String taskId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /v2/tasks/doquests : 做任务
     * 
     *
     * @param userId 用户id、address (required)
     * @param name 事件名称 (required)
     * @param contentId 当前事件对应的资源id (required)
     * @param eventTime 当前事件对应的资源价钱 (required)
     * @param sign app授权码 (required)
     * @param saasId saasId (required)
     * @param targetUserId 当前事件对应的资源的原资源用户id (optional)
     * @param targetContentId 当前事件对应的资源的原资源id (optional)
     * @param extendAttr 当前事件附加属性 (optional)
     * @return 成功 (status code 200)
     * @see TaskV2Api#v2TasksDoquestsPost
     */
    default ResponseEntity<WebResultTaskRewardVO> v2TasksDoquestsPost(String userId,
        String name,
        String contentId,
        Long eventTime,
        String sign,
        String saasId,
        String targetUserId,
        String targetContentId,
        String extendAttr) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /v2/tasks/dotask : 做任务
     * 
     *
     * @param taskId 任务id (required)
     * @return 成功 (status code 200)
     * @see TaskV2Api#v2TasksDotaskPost
     */
    default ResponseEntity<WebResultActiveTaskVO> v2TasksDotaskPost(String taskId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/tasks/early/pop
     * 
     *
     * @param saasId saasId (required)
     * @return 成功 (status code 200)
     * @see TaskV2Api#v2TasksEarlyPopGet
     */
    default ResponseEntity<WebResultTaskPopVO> v2TasksEarlyPopGet(String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/tasks/ladder/reward
     * 
     *
     * @param saasId saasId (required)
     * @return 成功 (status code 200)
     * @see TaskV2Api#v2TasksLadderRewardGet
     */
    default ResponseEntity<WebResultLadderRewardVO> v2TasksLadderRewardGet(String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/tasks/list : 获取任务列表
     * 
     *
     * @param saasId saas_id (optional)
     * @param channel channel (optional, default to app)
     * @param position home (optional)
     * @param clientType ios/android (optional)
     * @param appVersion app version (optional)
     * @return Successful operation (status code 200)
     * @see TaskV2Api#v2TasksListGet
     */
    default ResponseEntity<WebResultTaskVO> v2TasksListGet(String saasId,
        String channel,
        String position,
        String clientType,
        String appVersion) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/tasks/progress
     * 
     *
     * @param saasId saasId (required)
     * @param taskId 任务id (required)
     * @param type task or reward, 暂时只支持reward (required)
     * @return 成功 (status code 200)
     * @see TaskV2Api#v2TasksProgressGet
     */
    default ResponseEntity<WebResultTaskProgressVO> v2TasksProgressGet(String saasId,
        String taskId,
        String type) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/tasks/reward/by/code
     * 
     *
     * @param saasId saasId (required)
     * @param code code (required)
     * @return 成功 (status code 200)
     * @see TaskV2Api#v2TasksRewardByCodeGet
     */
    default ResponseEntity<WebResultTaskRewardStatVO> v2TasksRewardByCodeGet(String saasId,
        List<String> code) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /v2/tasks/sync/doquests : 同步做任务
     * 
     *
     * @param taskId 任务id (required)
     * @return 成功 (status code 200)
     * @see TaskV2Api#v2TasksSyncDoquestsPost
     */
    default ResponseEntity<WebResultActiveTaskVOS> v2TasksSyncDoquestsPost(String taskId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/tasks/twitter/tweets/{taskId} : 查询twitter最近的帖子
     * 
     *
     * @param saasId saasId (required)
     * @param taskId taskId (required)
     * @return 成功 (status code 200)
     * @see TaskV2Api#v2TasksTwitterTweetsTaskIdGet
     */
    default ResponseEntity<WebResultTaskTweetVO> v2TasksTwitterTweetsTaskIdGet(String saasId,
        String taskId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /v2/tasks/verify : 做任务
     * 
     *
     * @param taskId 任务id (required)
     * @param saasId saasId (required)
     * @param ext 其他参数，如邀请链接 (optional)
     * @return 成功 (status code 200)
     * @see TaskV2Api#v2TasksVerifyPost
     */
    default ResponseEntity<WebResultDoTaskVO> v2TasksVerifyPost(String taskId,
        String saasId,
        String ext) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
