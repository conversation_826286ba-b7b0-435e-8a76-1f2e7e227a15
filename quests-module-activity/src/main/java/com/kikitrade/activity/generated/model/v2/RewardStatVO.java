package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * RewardStatVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class RewardStatVO {

  @JsonProperty("expectReward")
  private BigDecimal expectReward;

  public RewardStatVO expectReward(BigDecimal expectReward) {
    this.expectReward = expectReward;
    return this;
  }

  /**
   * Get expectReward
   * @return expectReward
  */
  @Valid 
  @Schema(name = "expectReward", required = false)
  public BigDecimal getExpectReward() {
    return expectReward;
  }

  public void setExpectReward(BigDecimal expectReward) {
    this.expectReward = expectReward;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RewardStatVO rewardStatVO = (RewardStatVO) o;
    return Objects.equals(this.expectReward, rewardStatVO.expectReward);
  }

  @Override
  public int hashCode() {
    return Objects.hash(expectReward);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RewardStatVO {\n");
    sb.append("    expectReward: ").append(toIndentedString(expectReward)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

