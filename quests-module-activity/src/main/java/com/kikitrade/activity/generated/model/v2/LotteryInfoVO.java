package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * LotteryInfoVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class LotteryInfoVO {

  @JsonProperty("code")
  private String code;

  @JsonProperty("type")
  private String type;

  @JsonProperty("total")
  private Integer total;

  @JsonProperty("opened")
  private Integer opened;

  @JsonProperty("nextOpenTime")
  private Long nextOpenTime;

  @JsonProperty("cumulateTotal")
  private Integer cumulateTotal;

  @JsonProperty("nextCumulateCnt")
  private Integer nextCumulateCnt;

  @JsonProperty("nextCumulateAward")
  private BigDecimal nextCumulateAward;

  public LotteryInfoVO code(String code) {
    this.code = code;
    return this;
  }

  /**
   * 宝箱的业务标识
   * @return code
  */
  
  @Schema(name = "code", description = "宝箱的业务标识", required = false)
  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public LotteryInfoVO type(String type) {
    this.type = type;
    return this;
  }

  /**
   * 宝箱类型，gold-box(黄金宝箱)/goldest-box(紫金宝箱)
   * @return type
  */
  
  @Schema(name = "type", description = "宝箱类型，gold-box(黄金宝箱)/goldest-box(紫金宝箱)", required = false)
  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public LotteryInfoVO total(Integer total) {
    this.total = total;
    return this;
  }

  /**
   * 宝箱总数
   * @return total
  */
  
  @Schema(name = "total", description = "宝箱总数", required = false)
  public Integer getTotal() {
    return total;
  }

  public void setTotal(Integer total) {
    this.total = total;
  }

  public LotteryInfoVO opened(Integer opened) {
    this.opened = opened;
    return this;
  }

  /**
   * 已开启宝箱数量
   * @return opened
  */
  
  @Schema(name = "opened", description = "已开启宝箱数量", required = false)
  public Integer getOpened() {
    return opened;
  }

  public void setOpened(Integer opened) {
    this.opened = opened;
  }

  public LotteryInfoVO nextOpenTime(Long nextOpenTime) {
    this.nextOpenTime = nextOpenTime;
    return this;
  }

  /**
   * 下次开启宝箱时间，utc时间
   * @return nextOpenTime
  */
  
  @Schema(name = "nextOpenTime", description = "下次开启宝箱时间，utc时间", required = false)
  public Long getNextOpenTime() {
    return nextOpenTime;
  }

  public void setNextOpenTime(Long nextOpenTime) {
    this.nextOpenTime = nextOpenTime;
  }

  public LotteryInfoVO cumulateTotal(Integer cumulateTotal) {
    this.cumulateTotal = cumulateTotal;
    return this;
  }

  /**
   * 累积开启的宝箱总数
   * @return cumulateTotal
  */
  
  @Schema(name = "cumulateTotal", description = "累积开启的宝箱总数", required = false)
  public Integer getCumulateTotal() {
    return cumulateTotal;
  }

  public void setCumulateTotal(Integer cumulateTotal) {
    this.cumulateTotal = cumulateTotal;
  }

  public LotteryInfoVO nextCumulateCnt(Integer nextCumulateCnt) {
    this.nextCumulateCnt = nextCumulateCnt;
    return this;
  }

  /**
   * 累积开启的宝箱总数
   * @return nextCumulateCnt
  */
  
  @Schema(name = "nextCumulateCnt", description = "累积开启的宝箱总数", required = false)
  public Integer getNextCumulateCnt() {
    return nextCumulateCnt;
  }

  public void setNextCumulateCnt(Integer nextCumulateCnt) {
    this.nextCumulateCnt = nextCumulateCnt;
  }

  public LotteryInfoVO nextCumulateAward(BigDecimal nextCumulateAward) {
    this.nextCumulateAward = nextCumulateAward;
    return this;
  }

  /**
   * 扩展字段
   * @return nextCumulateAward
  */
  @Valid 
  @Schema(name = "nextCumulateAward", description = "扩展字段", required = false)
  public BigDecimal getNextCumulateAward() {
    return nextCumulateAward;
  }

  public void setNextCumulateAward(BigDecimal nextCumulateAward) {
    this.nextCumulateAward = nextCumulateAward;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LotteryInfoVO lotteryInfoVO = (LotteryInfoVO) o;
    return Objects.equals(this.code, lotteryInfoVO.code) &&
        Objects.equals(this.type, lotteryInfoVO.type) &&
        Objects.equals(this.total, lotteryInfoVO.total) &&
        Objects.equals(this.opened, lotteryInfoVO.opened) &&
        Objects.equals(this.nextOpenTime, lotteryInfoVO.nextOpenTime) &&
        Objects.equals(this.cumulateTotal, lotteryInfoVO.cumulateTotal) &&
        Objects.equals(this.nextCumulateCnt, lotteryInfoVO.nextCumulateCnt) &&
        Objects.equals(this.nextCumulateAward, lotteryInfoVO.nextCumulateAward);
  }

  @Override
  public int hashCode() {
    return Objects.hash(code, type, total, opened, nextOpenTime, cumulateTotal, nextCumulateCnt, nextCumulateAward);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LotteryInfoVO {\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    total: ").append(toIndentedString(total)).append("\n");
    sb.append("    opened: ").append(toIndentedString(opened)).append("\n");
    sb.append("    nextOpenTime: ").append(toIndentedString(nextOpenTime)).append("\n");
    sb.append("    cumulateTotal: ").append(toIndentedString(cumulateTotal)).append("\n");
    sb.append("    nextCumulateCnt: ").append(toIndentedString(nextCumulateCnt)).append("\n");
    sb.append("    nextCumulateAward: ").append(toIndentedString(nextCumulateAward)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

