/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.0.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.kikitrade.activity.generated.api.s2;

import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.activity.generated.model.s2.WebResultRewardResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "RewardS2", description = "the RewardS2 API")
public interface RewardS2Api {

    default RewardS2ApiDelegate getDelegate() {
        return new RewardS2ApiDelegate() {};
    }

    /**
     * POST /s2/reward/grant : 发放积分
     * 
     *
     * @param customerId  (required)
     * @param amount  (required)
     * @param desc  (required)
     * @param saasId  (required)
     * @param authorization  (required)
     * @param businessId  (optional)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "s2RewardGrantPost",
        summary = "发放积分",
        tags = { "RewardS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultRewardResultVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/s2/reward/grant",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultRewardResultVO> s2RewardGrantPost(
        @NotNull @Parameter(name = "customerId", description = "", required = true) @Valid @RequestParam(value = "customerId", required = true) String customerId,
        @NotNull @Parameter(name = "amount", description = "", required = true) @Valid @RequestParam(value = "amount", required = true) String amount,
        @NotNull @Parameter(name = "desc", description = "", required = true) @Valid @RequestParam(value = "desc", required = true) String desc,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @Parameter(name = "businessId", description = "") @Valid @RequestParam(value = "businessId", required = false) String businessId
    ) {
        return getDelegate().s2RewardGrantPost(customerId, amount, desc, saasId, authorization, businessId);
    }


    /**
     * POST /s2/reward/receive : 领取奖励
     * 
     *
     * @param saasId  (required)
     * @param authorization  (required)
     * @param customerId  (required)
     * @param taskId  (required)
     * @param extendAttr 事件附加属性 (optional)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "s2RewardReceivePost",
        summary = "领取奖励",
        tags = { "RewardS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResult.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/s2/reward/receive",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResult> s2RewardReceivePost(
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @NotNull @Parameter(name = "customerId", description = "", required = true) @Valid @RequestParam(value = "customerId", required = true) String customerId,
        @NotNull @Parameter(name = "taskId", description = "", required = true) @Valid @RequestParam(value = "taskId", required = true) String taskId,
        @Parameter(name = "extendAttr", description = "事件附加属性") @Valid @RequestParam(value = "extendAttr", required = false) String extendAttr
    ) {
        return getDelegate().s2RewardReceivePost(saasId, authorization, customerId, taskId, extendAttr);
    }

}
