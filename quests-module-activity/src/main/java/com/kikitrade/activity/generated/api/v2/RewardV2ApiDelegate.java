package com.kikitrade.activity.generated.api.v2;

import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.activity.generated.model.v2.WebResultClaimVO;
import com.kikitrade.activity.generated.model.v2.WebResultRewardResultVO;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link RewardV2ApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface RewardV2ApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * GET /v2/reward/allow/claim/{type} : 判断NFT是否claim过
     * 
     *
     * @param type 支持的值 nft (required)
     * @param saasId  (required)
     * @return 成功 (status code 200)
     * @see RewardV2Api#allowClaim
     */
    default ResponseEntity<WebResult> allowClaim(String type,
        String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /v2/reward/claim/{type} : 使用nft/兑换码兑换积分
     * 
     *
     * @param type 支持的值 nft/redeem (required)
     * @param saasId  (required)
     * @param code 兑换码 (optional)
     * @return 成功 (status code 200)
     * @see RewardV2Api#claim
     */
    default ResponseEntity<WebResultClaimVO> claim(String type,
        String saasId,
        String code) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /v2/reward/grant : 发放积分
     * 
     *
     * @param address  (required)
     * @param price  (required)
     * @param desc  (required)
     * @param saasId  (required)
     * @param sign app授权码 (required)
     * @param businessId  (optional)
     * @return 成功 (status code 200)
     * @see RewardV2Api#v2RewardGrantPost
     */
    default ResponseEntity<WebResultRewardResultVO> v2RewardGrantPost(List<String> address,
        String price,
        String desc,
        String saasId,
        String sign,
        String businessId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /v2/reward/receive : 领取奖励
     * 
     *
     * @param taskId  (required)
     * @param saasId  (required)
     * @return 成功 (status code 200)
     * @see RewardV2Api#v2RewardReceivePost
     */
    default ResponseEntity<WebResult> v2RewardReceivePost(String taskId,
        String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
