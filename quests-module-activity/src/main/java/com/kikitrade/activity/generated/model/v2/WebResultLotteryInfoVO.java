package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.kikitrade.activity.generated.model.v2.LotteryInfoVO;
import com.kikitrade.activity.generated.model.v2.WebResultLotteryInfoVOAllOf;
import com.kikitrade.kweb.model.common.WebResult;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * WebResultLotteryInfoVO
 */


@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class WebResultLotteryInfoVO extends WebResult {

  @JsonProperty("obj")
  @Valid
  private List<LotteryInfoVO> obj = null;

  public WebResultLotteryInfoVO obj(List<LotteryInfoVO> obj) {
    this.obj = obj;
    return this;
  }

  public WebResultLotteryInfoVO addObjItem(LotteryInfoVO objItem) {
    if (this.obj == null) {
      this.obj = new ArrayList<>();
    }
    this.obj.add(objItem);
    return this;
  }

  /**
   * Get obj
   * @return obj
  */
  @Valid 
  @Schema(name = "obj", required = false)
  public List<LotteryInfoVO> getObj() {
    return obj;
  }

  public void setObj(List<LotteryInfoVO> obj) {
    this.obj = obj;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WebResultLotteryInfoVO webResultLotteryInfoVO = (WebResultLotteryInfoVO) o;
    return Objects.equals(this.obj, webResultLotteryInfoVO.obj) &&
        super.equals(o);
  }

  @Override
  public int hashCode() {
    return Objects.hash(obj, super.hashCode());
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WebResultLotteryInfoVO {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    obj: ").append(toIndentedString(obj)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

