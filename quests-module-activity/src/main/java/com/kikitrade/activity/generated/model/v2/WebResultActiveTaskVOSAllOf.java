package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.kikitrade.activity.generated.model.v2.Award;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * WebResultActiveTaskVOSAllOf
 */

@JsonTypeName("WebResultActiveTaskVOS_allOf")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class WebResultActiveTaskVOSAllOf {

  @JsonProperty("obj")
  @Valid
  private List<Award> obj = null;

  public WebResultActiveTaskVOSAllOf obj(List<Award> obj) {
    this.obj = obj;
    return this;
  }

  public WebResultActiveTaskVOSAllOf addObjItem(Award objItem) {
    if (this.obj == null) {
      this.obj = new ArrayList<>();
    }
    this.obj.add(objItem);
    return this;
  }

  /**
   * Get obj
   * @return obj
  */
  @Valid 
  @Schema(name = "obj", required = false)
  public List<Award> getObj() {
    return obj;
  }

  public void setObj(List<Award> obj) {
    this.obj = obj;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WebResultActiveTaskVOSAllOf webResultActiveTaskVOSAllOf = (WebResultActiveTaskVOSAllOf) o;
    return Objects.equals(this.obj, webResultActiveTaskVOSAllOf.obj);
  }

  @Override
  public int hashCode() {
    return Objects.hash(obj);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WebResultActiveTaskVOSAllOf {\n");
    sb.append("    obj: ").append(toIndentedString(obj)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

