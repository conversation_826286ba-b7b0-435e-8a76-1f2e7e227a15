package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * NodeVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class NodeVO {

  @JsonProperty("index")
  private Integer index;

  @JsonProperty("nodeLogo")
  private Integer nodeLogo;

  public NodeVO index(Integer index) {
    this.index = index;
    return this;
  }

  /**
   * Get index
   * @return index
  */
  
  @Schema(name = "index", required = false)
  public Integer getIndex() {
    return index;
  }

  public void setIndex(Integer index) {
    this.index = index;
  }

  public NodeVO nodeLogo(Integer nodeLogo) {
    this.nodeLogo = nodeLogo;
    return this;
  }

  /**
   * Get nodeLogo
   * @return nodeLogo
  */
  
  @Schema(name = "nodeLogo", required = false)
  public Integer getNodeLogo() {
    return nodeLogo;
  }

  public void setNodeLogo(Integer nodeLogo) {
    this.nodeLogo = nodeLogo;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    NodeVO nodeVO = (NodeVO) o;
    return Objects.equals(this.index, nodeVO.index) &&
        Objects.equals(this.nodeLogo, nodeVO.nodeLogo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(index, nodeLogo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class NodeVO {\n");
    sb.append("    index: ").append(toIndentedString(index)).append("\n");
    sb.append("    nodeLogo: ").append(toIndentedString(nodeLogo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

