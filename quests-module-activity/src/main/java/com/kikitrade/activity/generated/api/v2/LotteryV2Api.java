/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.0.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.kikitrade.activity.generated.api.v2;

import com.kikitrade.activity.generated.model.v2.WebResultLotteryInfoVO;
import com.kikitrade.activity.generated.model.v2.WebResultLotteryVO;
import com.kikitrade.activity.generated.model.v2.WebResultLotteryWinnersVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "LotteryV2", description = "抽奖相关接口")
public interface LotteryV2Api {

    default LotteryV2ApiDelegate getDelegate() {
        return new LotteryV2ApiDelegate() {};
    }

    /**
     * POST /v2/lottery/{code}/open : 抽奖
     * 抽奖
     *
     * @param code  (required)
     * @param saasId  (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "drew",
        summary = "抽奖",
        tags = { "LotteryV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultLotteryVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/lottery/{code}/open",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultLotteryVO> drew(
        @Parameter(name = "code", description = "", required = true) @PathVariable("code") String code,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId
    ) {
        return getDelegate().drew(code, saasId);
    }


    /**
     * GET /v2/lottery/info : 奖池信息
     * 奖池信息
     *
     * @param saasId  (required)
     * @param code 多个code，以逗号分隔 (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "lotteryInfo",
        summary = "奖池信息",
        tags = { "LotteryV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultLotteryInfoVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/lottery/info",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultLotteryInfoVO> lotteryInfo(
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "code", description = "多个code，以逗号分隔", required = true) @Valid @RequestParam(value = "code", required = true) String code
    ) {
        return getDelegate().lotteryInfo(saasId, code);
    }


    /**
     * GET /v2/lottery/winners : 中奖名单查询
     *
     * @param saasId  (required)
     * @param lotteryCode 抽奖活动code (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "lotteryWinners",
        summary = "中奖名单查询",
        tags = { "LotteryV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultLotteryWinnersVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/lottery/winners",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultLotteryWinnersVO> lotteryWinners(
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "lotteryCode", description = "抽奖活动code", required = true) @Valid @RequestParam(value = "lotteryCode", required = true) String lotteryCode
    ) {
        return getDelegate().lotteryWinners(saasId, lotteryCode);
    }

}
