package com.kikitrade.activity.generated.api.v2;

import com.kikitrade.activity.generated.model.v2.UserAnswerVO;
import com.kikitrade.activity.generated.model.v2.WebResultQuestionVO;
import com.kikitrade.activity.generated.model.v2.WebResultSettleVO;
import com.kikitrade.activity.generated.model.v2.WebResultUserVO;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link QuestionV2ApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface QuestionV2ApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * GET /v2/question/acquire : Acquire a group of questions
     * Acquire a group of questions
     *
     * @param JWT_TOKEN JWT_TOKEN (required)
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     * @see QuestionV2Api#v2QuestionAcquireGet
     */
    default ResponseEntity<WebResultQuestionVO> v2QuestionAcquireGet(String JWT_TOKEN,
        String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/question/me : User sets details
     * User details
     *
     * @param JWT_TOKEN JWT_TOKEN (required)
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     * @see QuestionV2Api#v2QuestionMeGet
     */
    default ResponseEntity<WebResultUserVO> v2QuestionMeGet(String JWT_TOKEN,
        String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /v2/question/submit : Submit a group of questions
     * Submit a group of questions
     *
     * @param JWT_TOKEN JWT_TOKEN (required)
     * @param saasId saasId (required)
     * @param userAnswerVO  (required)
     * @return Successful operation (status code 200)
     * @see QuestionV2Api#v2QuestionSubmitPost
     */
    default ResponseEntity<WebResultSettleVO> v2QuestionSubmitPost(String JWT_TOKEN,
        String saasId,
        UserAnswerVO userAnswerVO) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
