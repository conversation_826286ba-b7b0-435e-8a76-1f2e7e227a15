package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.activity.generated.model.v2.NodeVO;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TaskCodeVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TaskCodeVO {

  @JsonProperty("checkIn")
  private Boolean checkIn;

  @JsonProperty("taskId")
  private String taskId;

  @JsonProperty("title")
  private String title;

  @JsonProperty("desc")
  private String desc;

  @JsonProperty("nodes")
  @Valid
  private List<NodeVO> nodes = null;

  @JsonProperty("process")
  private Integer process;

  @JsonProperty("image")
  @Valid
  private List<String> image = null;

  public TaskCodeVO checkIn(Boolean checkIn) {
    this.checkIn = checkIn;
    return this;
  }

  /**
   * Get checkIn
   * @return checkIn
  */
  
  @Schema(name = "checkIn", required = false)
  public Boolean getCheckIn() {
    return checkIn;
  }

  public void setCheckIn(Boolean checkIn) {
    this.checkIn = checkIn;
  }

  public TaskCodeVO taskId(String taskId) {
    this.taskId = taskId;
    return this;
  }

  /**
   * Get taskId
   * @return taskId
  */
  
  @Schema(name = "taskId", required = false)
  public String getTaskId() {
    return taskId;
  }

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  public TaskCodeVO title(String title) {
    this.title = title;
    return this;
  }

  /**
   * Get title
   * @return title
  */
  
  @Schema(name = "title", required = false)
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public TaskCodeVO desc(String desc) {
    this.desc = desc;
    return this;
  }

  /**
   * Get desc
   * @return desc
  */
  
  @Schema(name = "desc", required = false)
  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public TaskCodeVO nodes(List<NodeVO> nodes) {
    this.nodes = nodes;
    return this;
  }

  public TaskCodeVO addNodesItem(NodeVO nodesItem) {
    if (this.nodes == null) {
      this.nodes = new ArrayList<>();
    }
    this.nodes.add(nodesItem);
    return this;
  }

  /**
   * Get nodes
   * @return nodes
  */
  @Valid 
  @Schema(name = "nodes", required = false)
  public List<NodeVO> getNodes() {
    return nodes;
  }

  public void setNodes(List<NodeVO> nodes) {
    this.nodes = nodes;
  }

  public TaskCodeVO process(Integer process) {
    this.process = process;
    return this;
  }

  /**
   * Get process
   * @return process
  */
  
  @Schema(name = "process", required = false)
  public Integer getProcess() {
    return process;
  }

  public void setProcess(Integer process) {
    this.process = process;
  }

  public TaskCodeVO image(List<String> image) {
    this.image = image;
    return this;
  }

  public TaskCodeVO addImageItem(String imageItem) {
    if (this.image == null) {
      this.image = new ArrayList<>();
    }
    this.image.add(imageItem);
    return this;
  }

  /**
   * Get image
   * @return image
  */
  
  @Schema(name = "image", required = false)
  public List<String> getImage() {
    return image;
  }

  public void setImage(List<String> image) {
    this.image = image;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskCodeVO taskCodeVO = (TaskCodeVO) o;
    return Objects.equals(this.checkIn, taskCodeVO.checkIn) &&
        Objects.equals(this.taskId, taskCodeVO.taskId) &&
        Objects.equals(this.title, taskCodeVO.title) &&
        Objects.equals(this.desc, taskCodeVO.desc) &&
        Objects.equals(this.nodes, taskCodeVO.nodes) &&
        Objects.equals(this.process, taskCodeVO.process) &&
        Objects.equals(this.image, taskCodeVO.image);
  }

  @Override
  public int hashCode() {
    return Objects.hash(checkIn, taskId, title, desc, nodes, process, image);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskCodeVO {\n");
    sb.append("    checkIn: ").append(toIndentedString(checkIn)).append("\n");
    sb.append("    taskId: ").append(toIndentedString(taskId)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    desc: ").append(toIndentedString(desc)).append("\n");
    sb.append("    nodes: ").append(toIndentedString(nodes)).append("\n");
    sb.append("    process: ").append(toIndentedString(process)).append("\n");
    sb.append("    image: ").append(toIndentedString(image)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

