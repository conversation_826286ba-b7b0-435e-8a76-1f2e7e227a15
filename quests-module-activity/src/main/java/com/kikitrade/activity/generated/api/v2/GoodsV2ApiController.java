package com.kikitrade.activity.generated.api.v2;

import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.activity.generated.model.v2.WebResultGoodsListVO;
import com.kikitrade.activity.generated.model.v2.WebResultGoodsVO;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.*;
import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Controller
@RequestMapping("${openapi.kweb.base-path:}")
public class GoodsV2ApiController implements GoodsV2Api {

    private final GoodsV2ApiDelegate delegate;

    public GoodsV2ApiController(@Autowired(required = false) GoodsV2ApiDelegate delegate) {
        this.delegate = Optional.ofNullable(delegate).orElse(new GoodsV2ApiDelegate() {});
    }

    @Override
    public GoodsV2ApiDelegate getDelegate() {
        return delegate;
    }

}
