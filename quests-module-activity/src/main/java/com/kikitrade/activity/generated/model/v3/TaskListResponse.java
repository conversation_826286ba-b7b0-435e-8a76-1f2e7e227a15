package com.kikitrade.activity.generated.model.v3;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.activity.generated.model.v3.TaskSubVO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TaskListResponse
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TaskListResponse {

  @JsonProperty("taskId")
  private String taskId;

  @JsonProperty("groupId")
  private String groupId;

  @JsonProperty("title")
  private String title;

  @JsonProperty("desc")
  private String desc;

  @JsonProperty("image")
  private Map image;

  @JsonProperty("labelName")
  private String labelName;

  @JsonProperty("labelColor")
  private String labelColor;

  @JsonProperty("url")
  private String url;

  @JsonProperty("connectUrl")
  private String connectUrl;

  @JsonProperty("dataUrl")
  private String dataUrl;

  @JsonProperty("limit")
  private Integer limit;

  @JsonProperty("progress")
  private Integer progress;

  @JsonProperty("status")
  private Integer status;

  @JsonProperty("domain")
  private String domain;

  @JsonProperty("btn")
  private Integer btn;

  @JsonProperty("showProgress")
  private Boolean showProgress;

  @JsonProperty("link")
  private Map link;

  @JsonProperty("code")
  private String code;

  @JsonProperty("reward")
  private BigDecimal reward;

  @JsonProperty("position")
  private String position;

  @JsonProperty("subTask")
  @Valid
  private List<TaskSubVO> subTask = null;

  public TaskListResponse taskId(String taskId) {
    this.taskId = taskId;
    return this;
  }

  /**
   * Get taskId
   * @return taskId
  */
  
  @Schema(name = "taskId", required = false)
  public String getTaskId() {
    return taskId;
  }

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  public TaskListResponse groupId(String groupId) {
    this.groupId = groupId;
    return this;
  }

  /**
   * Get groupId
   * @return groupId
  */
  
  @Schema(name = "groupId", required = false)
  public String getGroupId() {
    return groupId;
  }

  public void setGroupId(String groupId) {
    this.groupId = groupId;
  }

  public TaskListResponse title(String title) {
    this.title = title;
    return this;
  }

  /**
   * Get title
   * @return title
  */
  
  @Schema(name = "title", required = false)
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public TaskListResponse desc(String desc) {
    this.desc = desc;
    return this;
  }

  /**
   * Get desc
   * @return desc
  */
  
  @Schema(name = "desc", required = false)
  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public TaskListResponse image(Map image) {
    this.image = image;
    return this;
  }

  /**
   * Get image
   * @return image
  */
  @Valid 
  @Schema(name = "image", required = false)
  public Map getImage() {
    return image;
  }

  public void setImage(Map image) {
    this.image = image;
  }

  public TaskListResponse labelName(String labelName) {
    this.labelName = labelName;
    return this;
  }

  /**
   * Get labelName
   * @return labelName
  */
  
  @Schema(name = "labelName", required = false)
  public String getLabelName() {
    return labelName;
  }

  public void setLabelName(String labelName) {
    this.labelName = labelName;
  }

  public TaskListResponse labelColor(String labelColor) {
    this.labelColor = labelColor;
    return this;
  }

  /**
   * Get labelColor
   * @return labelColor
  */
  
  @Schema(name = "labelColor", required = false)
  public String getLabelColor() {
    return labelColor;
  }

  public void setLabelColor(String labelColor) {
    this.labelColor = labelColor;
  }

  public TaskListResponse url(String url) {
    this.url = url;
    return this;
  }

  /**
   * Get url
   * @return url
  */
  
  @Schema(name = "url", required = false)
  public String getUrl() {
    return url;
  }

  public void setUrl(String url) {
    this.url = url;
  }

  public TaskListResponse connectUrl(String connectUrl) {
    this.connectUrl = connectUrl;
    return this;
  }

  /**
   * Get connectUrl
   * @return connectUrl
  */
  
  @Schema(name = "connectUrl", required = false)
  public String getConnectUrl() {
    return connectUrl;
  }

  public void setConnectUrl(String connectUrl) {
    this.connectUrl = connectUrl;
  }

  public TaskListResponse dataUrl(String dataUrl) {
    this.dataUrl = dataUrl;
    return this;
  }

  /**
   * Get dataUrl
   * @return dataUrl
  */
  
  @Schema(name = "dataUrl", required = false)
  public String getDataUrl() {
    return dataUrl;
  }

  public void setDataUrl(String dataUrl) {
    this.dataUrl = dataUrl;
  }

  public TaskListResponse limit(Integer limit) {
    this.limit = limit;
    return this;
  }

  /**
   * Get limit
   * @return limit
  */
  
  @Schema(name = "limit", required = false)
  public Integer getLimit() {
    return limit;
  }

  public void setLimit(Integer limit) {
    this.limit = limit;
  }

  public TaskListResponse progress(Integer progress) {
    this.progress = progress;
    return this;
  }

  /**
   * Get progress
   * @return progress
  */
  
  @Schema(name = "progress", required = false)
  public Integer getProgress() {
    return progress;
  }

  public void setProgress(Integer progress) {
    this.progress = progress;
  }

  public TaskListResponse status(Integer status) {
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
  */
  
  @Schema(name = "status", required = false)
  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public TaskListResponse domain(String domain) {
    this.domain = domain;
    return this;
  }

  /**
   * Get domain
   * @return domain
  */
  
  @Schema(name = "domain", required = false)
  public String getDomain() {
    return domain;
  }

  public void setDomain(String domain) {
    this.domain = domain;
  }

  public TaskListResponse btn(Integer btn) {
    this.btn = btn;
    return this;
  }

  /**
   * 0:不显示 1:go 2:verify
   * @return btn
  */
  
  @Schema(name = "btn", description = "0:不显示 1:go 2:verify", required = false)
  public Integer getBtn() {
    return btn;
  }

  public void setBtn(Integer btn) {
    this.btn = btn;
  }

  public TaskListResponse showProgress(Boolean showProgress) {
    this.showProgress = showProgress;
    return this;
  }

  /**
   * 是否显示进度条
   * @return showProgress
  */
  
  @Schema(name = "showProgress", description = "是否显示进度条", required = false)
  public Boolean getShowProgress() {
    return showProgress;
  }

  public void setShowProgress(Boolean showProgress) {
    this.showProgress = showProgress;
  }

  public TaskListResponse link(Map link) {
    this.link = link;
    return this;
  }

  /**
   * Get link
   * @return link
  */
  @Valid 
  @Schema(name = "link", required = false)
  public Map getLink() {
    return link;
  }

  public void setLink(Map link) {
    this.link = link;
  }

  public TaskListResponse code(String code) {
    this.code = code;
    return this;
  }

  /**
   * Get code
   * @return code
  */
  
  @Schema(name = "code", required = false)
  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public TaskListResponse reward(BigDecimal reward) {
    this.reward = reward;
    return this;
  }

  /**
   * Get reward
   * @return reward
  */
  @Valid 
  @Schema(name = "reward", required = false)
  public BigDecimal getReward() {
    return reward;
  }

  public void setReward(BigDecimal reward) {
    this.reward = reward;
  }

  public TaskListResponse position(String position) {
    this.position = position;
    return this;
  }

  /**
   * Get position
   * @return position
  */
  
  @Schema(name = "position", required = false)
  public String getPosition() {
    return position;
  }

  public void setPosition(String position) {
    this.position = position;
  }

  public TaskListResponse subTask(List<TaskSubVO> subTask) {
    this.subTask = subTask;
    return this;
  }

  public TaskListResponse addSubTaskItem(TaskSubVO subTaskItem) {
    if (this.subTask == null) {
      this.subTask = new ArrayList<>();
    }
    this.subTask.add(subTaskItem);
    return this;
  }

  /**
   * Get subTask
   * @return subTask
  */
  @Valid 
  @Schema(name = "subTask", required = false)
  public List<TaskSubVO> getSubTask() {
    return subTask;
  }

  public void setSubTask(List<TaskSubVO> subTask) {
    this.subTask = subTask;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskListResponse taskListResponse = (TaskListResponse) o;
    return Objects.equals(this.taskId, taskListResponse.taskId) &&
        Objects.equals(this.groupId, taskListResponse.groupId) &&
        Objects.equals(this.title, taskListResponse.title) &&
        Objects.equals(this.desc, taskListResponse.desc) &&
        Objects.equals(this.image, taskListResponse.image) &&
        Objects.equals(this.labelName, taskListResponse.labelName) &&
        Objects.equals(this.labelColor, taskListResponse.labelColor) &&
        Objects.equals(this.url, taskListResponse.url) &&
        Objects.equals(this.connectUrl, taskListResponse.connectUrl) &&
        Objects.equals(this.dataUrl, taskListResponse.dataUrl) &&
        Objects.equals(this.limit, taskListResponse.limit) &&
        Objects.equals(this.progress, taskListResponse.progress) &&
        Objects.equals(this.status, taskListResponse.status) &&
        Objects.equals(this.domain, taskListResponse.domain) &&
        Objects.equals(this.btn, taskListResponse.btn) &&
        Objects.equals(this.showProgress, taskListResponse.showProgress) &&
        Objects.equals(this.link, taskListResponse.link) &&
        Objects.equals(this.code, taskListResponse.code) &&
        Objects.equals(this.reward, taskListResponse.reward) &&
        Objects.equals(this.position, taskListResponse.position) &&
        Objects.equals(this.subTask, taskListResponse.subTask);
  }

  @Override
  public int hashCode() {
    return Objects.hash(taskId, groupId, title, desc, image, labelName, labelColor, url, connectUrl, dataUrl, limit, progress, status, domain, btn, showProgress, link, code, reward, position, subTask);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskListResponse {\n");
    sb.append("    taskId: ").append(toIndentedString(taskId)).append("\n");
    sb.append("    groupId: ").append(toIndentedString(groupId)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    desc: ").append(toIndentedString(desc)).append("\n");
    sb.append("    image: ").append(toIndentedString(image)).append("\n");
    sb.append("    labelName: ").append(toIndentedString(labelName)).append("\n");
    sb.append("    labelColor: ").append(toIndentedString(labelColor)).append("\n");
    sb.append("    url: ").append(toIndentedString(url)).append("\n");
    sb.append("    connectUrl: ").append(toIndentedString(connectUrl)).append("\n");
    sb.append("    dataUrl: ").append(toIndentedString(dataUrl)).append("\n");
    sb.append("    limit: ").append(toIndentedString(limit)).append("\n");
    sb.append("    progress: ").append(toIndentedString(progress)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    domain: ").append(toIndentedString(domain)).append("\n");
    sb.append("    btn: ").append(toIndentedString(btn)).append("\n");
    sb.append("    showProgress: ").append(toIndentedString(showProgress)).append("\n");
    sb.append("    link: ").append(toIndentedString(link)).append("\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    reward: ").append(toIndentedString(reward)).append("\n");
    sb.append("    position: ").append(toIndentedString(position)).append("\n");
    sb.append("    subTask: ").append(toIndentedString(subTask)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

