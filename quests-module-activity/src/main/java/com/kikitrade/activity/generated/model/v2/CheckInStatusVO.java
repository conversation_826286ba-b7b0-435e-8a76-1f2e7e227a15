package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.activity.generated.model.v2.Award;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * CheckInStatusVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class CheckInStatusVO {

  @JsonProperty("todayReward")
  private String todayReward;

  @JsonProperty("todayRewardType")
  private String todayRewardType;

  @JsonProperty("title")
  private String title;

  @JsonProperty("desc")
  private String desc;

  @JsonProperty("icon")
  private String icon;

  @JsonProperty("checkIn")
  private Boolean checkIn = false;

  @JsonProperty("checkInTime")
  private Long checkInTime;

  @JsonProperty("checkInDays")
  private Integer checkInDays = 0;

  @JsonProperty("checkInReward")
  @Valid
  private List<Award> checkInReward = null;

  public CheckInStatusVO todayReward(String todayReward) {
    this.todayReward = todayReward;
    return this;
  }

  /**
   * 今日签到奖励
   * @return todayReward
  */
  
  @Schema(name = "todayReward", description = "今日签到奖励", required = false)
  public String getTodayReward() {
    return todayReward;
  }

  public void setTodayReward(String todayReward) {
    this.todayReward = todayReward;
  }

  public CheckInStatusVO todayRewardType(String todayRewardType) {
    this.todayRewardType = todayRewardType;
    return this;
  }

  /**
   * 今日签到奖励类型
   * @return todayRewardType
  */
  
  @Schema(name = "todayRewardType", description = "今日签到奖励类型", required = false)
  public String getTodayRewardType() {
    return todayRewardType;
  }

  public void setTodayRewardType(String todayRewardType) {
    this.todayRewardType = todayRewardType;
  }

  public CheckInStatusVO title(String title) {
    this.title = title;
    return this;
  }

  /**
   * 任务标题
   * @return title
  */
  
  @Schema(name = "title", description = "任务标题", required = false)
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public CheckInStatusVO desc(String desc) {
    this.desc = desc;
    return this;
  }

  /**
   * Get desc
   * @return desc
  */
  
  @Schema(name = "desc", required = false)
  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public CheckInStatusVO icon(String icon) {
    this.icon = icon;
    return this;
  }

  /**
   * Get icon
   * @return icon
  */
  
  @Schema(name = "icon", required = false)
  public String getIcon() {
    return icon;
  }

  public void setIcon(String icon) {
    this.icon = icon;
  }

  public CheckInStatusVO checkIn(Boolean checkIn) {
    this.checkIn = checkIn;
    return this;
  }

  /**
   * true 已签到，false 未签到
   * @return checkIn
  */
  
  @Schema(name = "checkIn", example = "true", description = "true 已签到，false 未签到", required = false)
  public Boolean getCheckIn() {
    return checkIn;
  }

  public void setCheckIn(Boolean checkIn) {
    this.checkIn = checkIn;
  }

  public CheckInStatusVO checkInTime(Long checkInTime) {
    this.checkInTime = checkInTime;
    return this;
  }

  /**
   * Get checkInTime
   * @return checkInTime
  */
  
  @Schema(name = "checkInTime", example = "1640995200000", required = false)
  public Long getCheckInTime() {
    return checkInTime;
  }

  public void setCheckInTime(Long checkInTime) {
    this.checkInTime = checkInTime;
  }

  public CheckInStatusVO checkInDays(Integer checkInDays) {
    this.checkInDays = checkInDays;
    return this;
  }

  /**
   * Get checkInDays
   * @return checkInDays
  */
  
  @Schema(name = "checkInDays", example = "7", required = false)
  public Integer getCheckInDays() {
    return checkInDays;
  }

  public void setCheckInDays(Integer checkInDays) {
    this.checkInDays = checkInDays;
  }

  public CheckInStatusVO checkInReward(List<Award> checkInReward) {
    this.checkInReward = checkInReward;
    return this;
  }

  public CheckInStatusVO addCheckInRewardItem(Award checkInRewardItem) {
    if (this.checkInReward == null) {
      this.checkInReward = new ArrayList<>();
    }
    this.checkInReward.add(checkInRewardItem);
    return this;
  }

  /**
   * Get checkInReward
   * @return checkInReward
  */
  @Valid 
  @Schema(name = "checkInReward", required = false)
  public List<Award> getCheckInReward() {
    return checkInReward;
  }

  public void setCheckInReward(List<Award> checkInReward) {
    this.checkInReward = checkInReward;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CheckInStatusVO checkInStatusVO = (CheckInStatusVO) o;
    return Objects.equals(this.todayReward, checkInStatusVO.todayReward) &&
        Objects.equals(this.todayRewardType, checkInStatusVO.todayRewardType) &&
        Objects.equals(this.title, checkInStatusVO.title) &&
        Objects.equals(this.desc, checkInStatusVO.desc) &&
        Objects.equals(this.icon, checkInStatusVO.icon) &&
        Objects.equals(this.checkIn, checkInStatusVO.checkIn) &&
        Objects.equals(this.checkInTime, checkInStatusVO.checkInTime) &&
        Objects.equals(this.checkInDays, checkInStatusVO.checkInDays) &&
        Objects.equals(this.checkInReward, checkInStatusVO.checkInReward);
  }

  @Override
  public int hashCode() {
    return Objects.hash(todayReward, todayRewardType, title, desc, icon, checkIn, checkInTime, checkInDays, checkInReward);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CheckInStatusVO {\n");
    sb.append("    todayReward: ").append(toIndentedString(todayReward)).append("\n");
    sb.append("    todayRewardType: ").append(toIndentedString(todayRewardType)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    desc: ").append(toIndentedString(desc)).append("\n");
    sb.append("    icon: ").append(toIndentedString(icon)).append("\n");
    sb.append("    checkIn: ").append(toIndentedString(checkIn)).append("\n");
    sb.append("    checkInTime: ").append(toIndentedString(checkInTime)).append("\n");
    sb.append("    checkInDays: ").append(toIndentedString(checkInDays)).append("\n");
    sb.append("    checkInReward: ").append(toIndentedString(checkInReward)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

