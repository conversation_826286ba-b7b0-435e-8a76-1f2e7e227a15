package com.kikitrade.activity.generated.api.v2;

import com.kikitrade.activity.generated.model.v2.WebResultActiveTaskVO;
import com.kikitrade.activity.generated.model.v2.WebResultActiveTaskVOS;
import com.kikitrade.activity.generated.model.v2.WebResultCheckInStatusVO;
import com.kikitrade.activity.generated.model.v2.WebResultDoTaskVO;
import com.kikitrade.activity.generated.model.v2.WebResultLadderRewardVO;
import com.kikitrade.activity.generated.model.v2.WebResultSeriesTaskVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskDetailVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskPopVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskProgressVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskRewardStatVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskRewardVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskTweetVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskVO;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.*;
import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Controller
@RequestMapping("${openapi.kweb.base-path:}")
public class TaskV2ApiController implements TaskV2Api {

    private final TaskV2ApiDelegate delegate;

    public TaskV2ApiController(@Autowired(required = false) TaskV2ApiDelegate delegate) {
        this.delegate = Optional.ofNullable(delegate).orElse(new TaskV2ApiDelegate() {});
    }

    @Override
    public TaskV2ApiDelegate getDelegate() {
        return delegate;
    }

}
