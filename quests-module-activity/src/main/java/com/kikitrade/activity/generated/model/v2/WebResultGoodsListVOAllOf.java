package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.kikitrade.activity.generated.model.v2.GoodsVO;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * WebResultGoodsListVOAllOf
 */

@JsonTypeName("WebResultGoodsListVO_allOf")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class WebResultGoodsListVOAllOf {

  @JsonProperty("obj")
  @Valid
  private List<GoodsVO> obj = null;

  public WebResultGoodsListVOAllOf obj(List<GoodsVO> obj) {
    this.obj = obj;
    return this;
  }

  public WebResultGoodsListVOAllOf addObjItem(GoodsVO objItem) {
    if (this.obj == null) {
      this.obj = new ArrayList<>();
    }
    this.obj.add(objItem);
    return this;
  }

  /**
   * Get obj
   * @return obj
  */
  @Valid 
  @Schema(name = "obj", required = false)
  public List<GoodsVO> getObj() {
    return obj;
  }

  public void setObj(List<GoodsVO> obj) {
    this.obj = obj;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WebResultGoodsListVOAllOf webResultGoodsListVOAllOf = (WebResultGoodsListVOAllOf) o;
    return Objects.equals(this.obj, webResultGoodsListVOAllOf.obj);
  }

  @Override
  public int hashCode() {
    return Objects.hash(obj);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WebResultGoodsListVOAllOf {\n");
    sb.append("    obj: ").append(toIndentedString(obj)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

