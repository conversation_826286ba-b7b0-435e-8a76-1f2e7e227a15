package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.kikitrade.activity.generated.model.v2.RewardResultVO;
import com.kikitrade.activity.generated.model.v2.WebResultRewardResultVOAllOf;
import com.kikitrade.kweb.model.common.WebResult;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * WebResultRewardResultVO
 */


@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class WebResultRewardResultVO extends WebResult {

  @JsonProperty("obj")
  @Valid
  private List<RewardResultVO> obj = null;

  public WebResultRewardResultVO obj(List<RewardResultVO> obj) {
    this.obj = obj;
    return this;
  }

  public WebResultRewardResultVO addObjItem(RewardResultVO objItem) {
    if (this.obj == null) {
      this.obj = new ArrayList<>();
    }
    this.obj.add(objItem);
    return this;
  }

  /**
   * Get obj
   * @return obj
  */
  @Valid 
  @Schema(name = "obj", required = false)
  public List<RewardResultVO> getObj() {
    return obj;
  }

  public void setObj(List<RewardResultVO> obj) {
    this.obj = obj;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WebResultRewardResultVO webResultRewardResultVO = (WebResultRewardResultVO) o;
    return Objects.equals(this.obj, webResultRewardResultVO.obj) &&
        super.equals(o);
  }

  @Override
  public int hashCode() {
    return Objects.hash(obj, super.hashCode());
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WebResultRewardResultVO {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    obj: ").append(toIndentedString(obj)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

