package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * Winner
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class Winner {

  @JsonProperty("address")
  private String address;

  @JsonProperty("order")
  private Integer order;

  public Winner address(String address) {
    this.address = address;
    return this;
  }

  /**
   * 用户地址，用于标识中奖者
   * @return address
  */
  @NotNull 
  @Schema(name = "address", description = "用户地址，用于标识中奖者", required = true)
  public String getAddress() {
    return address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public Winner order(Integer order) {
    this.order = order;
    return this;
  }

  /**
   * 中奖排名
   * @return order
  */
  @NotNull 
  @Schema(name = "order", description = "中奖排名", required = true)
  public Integer getOrder() {
    return order;
  }

  public void setOrder(Integer order) {
    this.order = order;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Winner winner = (Winner) o;
    return Objects.equals(this.address, winner.address) &&
        Objects.equals(this.order, winner.order);
  }

  @Override
  public int hashCode() {
    return Objects.hash(address, order);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Winner {\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    order: ").append(toIndentedString(order)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

