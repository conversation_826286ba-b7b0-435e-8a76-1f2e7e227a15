package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * UserAnswerVOQuestionsInner
 */

@JsonTypeName("UserAnswerVO_questions_inner")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class UserAnswerVOQuestionsInner {

  @JsonProperty("id")
  private String id;

  @JsonProperty("title")
  private String title;

  @JsonProperty("option1")
  private String option1;

  @JsonProperty("option2")
  private String option2;

  @JsonProperty("userOption")
  private String userOption;

  public UserAnswerVOQuestionsInner id(String id) {
    this.id = id;
    return this;
  }

  /**
   * The ID of the question
   * @return id
  */
  
  @Schema(name = "id", description = "The ID of the question", required = false)
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public UserAnswerVOQuestionsInner title(String title) {
    this.title = title;
    return this;
  }

  /**
   * The question title
   * @return title
  */
  
  @Schema(name = "title", description = "The question title", required = false)
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public UserAnswerVOQuestionsInner option1(String option1) {
    this.option1 = option1;
    return this;
  }

  /**
   * The question option1
   * @return option1
  */
  
  @Schema(name = "option1", description = "The question option1", required = false)
  public String getOption1() {
    return option1;
  }

  public void setOption1(String option1) {
    this.option1 = option1;
  }

  public UserAnswerVOQuestionsInner option2(String option2) {
    this.option2 = option2;
    return this;
  }

  /**
   * The question option2
   * @return option2
  */
  
  @Schema(name = "option2", description = "The question option2", required = false)
  public String getOption2() {
    return option2;
  }

  public void setOption2(String option2) {
    this.option2 = option2;
  }

  public UserAnswerVOQuestionsInner userOption(String userOption) {
    this.userOption = userOption;
    return this;
  }

  /**
   * The userOption to the question
   * @return userOption
  */
  
  @Schema(name = "userOption", description = "The userOption to the question", required = false)
  public String getUserOption() {
    return userOption;
  }

  public void setUserOption(String userOption) {
    this.userOption = userOption;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UserAnswerVOQuestionsInner userAnswerVOQuestionsInner = (UserAnswerVOQuestionsInner) o;
    return Objects.equals(this.id, userAnswerVOQuestionsInner.id) &&
        Objects.equals(this.title, userAnswerVOQuestionsInner.title) &&
        Objects.equals(this.option1, userAnswerVOQuestionsInner.option1) &&
        Objects.equals(this.option2, userAnswerVOQuestionsInner.option2) &&
        Objects.equals(this.userOption, userAnswerVOQuestionsInner.userOption);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, title, option1, option2, userOption);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UserAnswerVOQuestionsInner {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    option1: ").append(toIndentedString(option1)).append("\n");
    sb.append("    option2: ").append(toIndentedString(option2)).append("\n");
    sb.append("    userOption: ").append(toIndentedString(userOption)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

