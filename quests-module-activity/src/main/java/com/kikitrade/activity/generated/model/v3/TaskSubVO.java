package com.kikitrade.activity.generated.model.v3;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TaskSubVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TaskSubVO {

  @JsonProperty("taskId")
  private String taskId;

  @JsonProperty("title")
  private String title;

  @JsonProperty("desc")
  private String desc;

  @JsonProperty("reward")
  private String reward;

  @JsonProperty("domain")
  private String domain;

  @JsonProperty("url")
  private String url;

  @JsonProperty("image")
  private String image;

  @JsonProperty("status")
  private Integer status;

  @JsonProperty("platform")
  private String platform;

  @JsonProperty("code")
  private String code;

  @JsonProperty("connectUrl")
  private String connectUrl;

  @JsonProperty("rewardStatus")
  private Integer rewardStatus;

  @JsonProperty("startTime")
  private Long startTime;

  @JsonProperty("endTime")
  private Long endTime;

  public TaskSubVO taskId(String taskId) {
    this.taskId = taskId;
    return this;
  }

  /**
   * Get taskId
   * @return taskId
  */
  
  @Schema(name = "taskId", required = false)
  public String getTaskId() {
    return taskId;
  }

  public void setTaskId(String taskId) {
    this.taskId = taskId;
  }

  public TaskSubVO title(String title) {
    this.title = title;
    return this;
  }

  /**
   * Get title
   * @return title
  */
  
  @Schema(name = "title", required = false)
  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public TaskSubVO desc(String desc) {
    this.desc = desc;
    return this;
  }

  /**
   * Get desc
   * @return desc
  */
  
  @Schema(name = "desc", required = false)
  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  public TaskSubVO reward(String reward) {
    this.reward = reward;
    return this;
  }

  /**
   * Get reward
   * @return reward
  */
  
  @Schema(name = "reward", required = false)
  public String getReward() {
    return reward;
  }

  public void setReward(String reward) {
    this.reward = reward;
  }

  public TaskSubVO domain(String domain) {
    this.domain = domain;
    return this;
  }

  /**
   * Get domain
   * @return domain
  */
  
  @Schema(name = "domain", required = false)
  public String getDomain() {
    return domain;
  }

  public void setDomain(String domain) {
    this.domain = domain;
  }

  public TaskSubVO url(String url) {
    this.url = url;
    return this;
  }

  /**
   * Get url
   * @return url
  */
  
  @Schema(name = "url", required = false)
  public String getUrl() {
    return url;
  }

  public void setUrl(String url) {
    this.url = url;
  }

  public TaskSubVO image(String image) {
    this.image = image;
    return this;
  }

  /**
   * Get image
   * @return image
  */
  
  @Schema(name = "image", required = false)
  public String getImage() {
    return image;
  }

  public void setImage(String image) {
    this.image = image;
  }

  public TaskSubVO status(Integer status) {
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
  */
  
  @Schema(name = "status", required = false)
  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public TaskSubVO platform(String platform) {
    this.platform = platform;
    return this;
  }

  /**
   * Get platform
   * @return platform
  */
  
  @Schema(name = "platform", required = false)
  public String getPlatform() {
    return platform;
  }

  public void setPlatform(String platform) {
    this.platform = platform;
  }

  public TaskSubVO code(String code) {
    this.code = code;
    return this;
  }

  /**
   * Get code
   * @return code
  */
  
  @Schema(name = "code", required = false)
  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public TaskSubVO connectUrl(String connectUrl) {
    this.connectUrl = connectUrl;
    return this;
  }

  /**
   * Get connectUrl
   * @return connectUrl
  */
  
  @Schema(name = "connectUrl", required = false)
  public String getConnectUrl() {
    return connectUrl;
  }

  public void setConnectUrl(String connectUrl) {
    this.connectUrl = connectUrl;
  }

  public TaskSubVO rewardStatus(Integer rewardStatus) {
    this.rewardStatus = rewardStatus;
    return this;
  }

  /**
   * Get rewardStatus
   * @return rewardStatus
  */
  
  @Schema(name = "rewardStatus", required = false)
  public Integer getRewardStatus() {
    return rewardStatus;
  }

  public void setRewardStatus(Integer rewardStatus) {
    this.rewardStatus = rewardStatus;
  }

  public TaskSubVO startTime(Long startTime) {
    this.startTime = startTime;
    return this;
  }

  /**
   * Get startTime
   * @return startTime
  */
  
  @Schema(name = "startTime", required = false)
  public Long getStartTime() {
    return startTime;
  }

  public void setStartTime(Long startTime) {
    this.startTime = startTime;
  }

  public TaskSubVO endTime(Long endTime) {
    this.endTime = endTime;
    return this;
  }

  /**
   * Get endTime
   * @return endTime
  */
  
  @Schema(name = "endTime", required = false)
  public Long getEndTime() {
    return endTime;
  }

  public void setEndTime(Long endTime) {
    this.endTime = endTime;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskSubVO taskSubVO = (TaskSubVO) o;
    return Objects.equals(this.taskId, taskSubVO.taskId) &&
        Objects.equals(this.title, taskSubVO.title) &&
        Objects.equals(this.desc, taskSubVO.desc) &&
        Objects.equals(this.reward, taskSubVO.reward) &&
        Objects.equals(this.domain, taskSubVO.domain) &&
        Objects.equals(this.url, taskSubVO.url) &&
        Objects.equals(this.image, taskSubVO.image) &&
        Objects.equals(this.status, taskSubVO.status) &&
        Objects.equals(this.platform, taskSubVO.platform) &&
        Objects.equals(this.code, taskSubVO.code) &&
        Objects.equals(this.connectUrl, taskSubVO.connectUrl) &&
        Objects.equals(this.rewardStatus, taskSubVO.rewardStatus) &&
        Objects.equals(this.startTime, taskSubVO.startTime) &&
        Objects.equals(this.endTime, taskSubVO.endTime);
  }

  @Override
  public int hashCode() {
    return Objects.hash(taskId, title, desc, reward, domain, url, image, status, platform, code, connectUrl, rewardStatus, startTime, endTime);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskSubVO {\n");
    sb.append("    taskId: ").append(toIndentedString(taskId)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    desc: ").append(toIndentedString(desc)).append("\n");
    sb.append("    reward: ").append(toIndentedString(reward)).append("\n");
    sb.append("    domain: ").append(toIndentedString(domain)).append("\n");
    sb.append("    url: ").append(toIndentedString(url)).append("\n");
    sb.append("    image: ").append(toIndentedString(image)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    platform: ").append(toIndentedString(platform)).append("\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    connectUrl: ").append(toIndentedString(connectUrl)).append("\n");
    sb.append("    rewardStatus: ").append(toIndentedString(rewardStatus)).append("\n");
    sb.append("    startTime: ").append(toIndentedString(startTime)).append("\n");
    sb.append("    endTime: ").append(toIndentedString(endTime)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

