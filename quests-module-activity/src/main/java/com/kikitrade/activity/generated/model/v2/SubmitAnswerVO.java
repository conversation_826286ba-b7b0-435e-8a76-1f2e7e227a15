package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.activity.generated.model.v2.SubmitAnswerVOQuestionsInner;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * SubmitAnswerVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class SubmitAnswerVO {

  @JsonProperty("spend_time")
  private BigDecimal spendTime;

  @JsonProperty("questions")
  @Valid
  private List<SubmitAnswerVOQuestionsInner> questions = new ArrayList<>();

  public SubmitAnswerVO spendTime(BigDecimal spendTime) {
    this.spendTime = spendTime;
    return this;
  }

  /**
   * Time spent answering the questions
   * @return spendTime
  */
  @NotNull @Valid 
  @Schema(name = "spend_time", description = "Time spent answering the questions", required = true)
  public BigDecimal getSpendTime() {
    return spendTime;
  }

  public void setSpendTime(BigDecimal spendTime) {
    this.spendTime = spendTime;
  }

  public SubmitAnswerVO questions(List<SubmitAnswerVOQuestionsInner> questions) {
    this.questions = questions;
    return this;
  }

  public SubmitAnswerVO addQuestionsItem(SubmitAnswerVOQuestionsInner questionsItem) {
    this.questions.add(questionsItem);
    return this;
  }

  /**
   * Get questions
   * @return questions
  */
  @NotNull @Valid 
  @Schema(name = "questions", required = true)
  public List<SubmitAnswerVOQuestionsInner> getQuestions() {
    return questions;
  }

  public void setQuestions(List<SubmitAnswerVOQuestionsInner> questions) {
    this.questions = questions;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SubmitAnswerVO submitAnswerVO = (SubmitAnswerVO) o;
    return Objects.equals(this.spendTime, submitAnswerVO.spendTime) &&
        Objects.equals(this.questions, submitAnswerVO.questions);
  }

  @Override
  public int hashCode() {
    return Objects.hash(spendTime, questions);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SubmitAnswerVO {\n");
    sb.append("    spendTime: ").append(toIndentedString(spendTime)).append("\n");
    sb.append("    questions: ").append(toIndentedString(questions)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

