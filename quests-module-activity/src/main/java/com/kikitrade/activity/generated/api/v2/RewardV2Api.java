/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.0.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.kikitrade.activity.generated.api.v2;

import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.activity.generated.model.v2.WebResultClaimVO;
import com.kikitrade.activity.generated.model.v2.WebResultRewardResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "RewardV2", description = "the RewardV2 API")
public interface RewardV2Api {

    default RewardV2ApiDelegate getDelegate() {
        return new RewardV2ApiDelegate() {};
    }

    /**
     * GET /v2/reward/allow/claim/{type} : 判断NFT是否claim过
     * 
     *
     * @param type 支持的值 nft (required)
     * @param saasId  (required)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "allowClaim",
        summary = "判断NFT是否claim过",
        tags = { "RewardV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResult.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/reward/allow/claim/{type}",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResult> allowClaim(
        @Parameter(name = "type", description = "支持的值 nft", required = true) @PathVariable("type") String type,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId
    ) {
        return getDelegate().allowClaim(type, saasId);
    }


    /**
     * POST /v2/reward/claim/{type} : 使用nft/兑换码兑换积分
     * 
     *
     * @param type 支持的值 nft/redeem (required)
     * @param saasId  (required)
     * @param code 兑换码 (optional)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "claim",
        summary = "使用nft/兑换码兑换积分",
        tags = { "RewardV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultClaimVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/reward/claim/{type}",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultClaimVO> claim(
        @Parameter(name = "type", description = "支持的值 nft/redeem", required = true) @PathVariable("type") String type,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "code", description = "兑换码") @Valid @RequestParam(value = "code", required = false) String code
    ) {
        return getDelegate().claim(type, saasId, code);
    }


    /**
     * POST /v2/reward/grant : 发放积分
     * 
     *
     * @param address  (required)
     * @param price  (required)
     * @param desc  (required)
     * @param saasId  (required)
     * @param sign app授权码 (required)
     * @param businessId  (optional)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2RewardGrantPost",
        summary = "发放积分",
        tags = { "RewardV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultRewardResultVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/reward/grant",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultRewardResultVO> v2RewardGrantPost(
        @NotNull @Parameter(name = "address", description = "", required = true) @Valid @RequestParam(value = "address", required = true) List<String> address,
        @NotNull @Parameter(name = "price", description = "", required = true) @Valid @RequestParam(value = "price", required = true) String price,
        @NotNull @Parameter(name = "desc", description = "", required = true) @Valid @RequestParam(value = "desc", required = true) String desc,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "sign", description = "app授权码", required = true) @RequestHeader(value = "sign", required = true) String sign,
        @Parameter(name = "businessId", description = "") @Valid @RequestParam(value = "businessId", required = false) String businessId
    ) {
        return getDelegate().v2RewardGrantPost(address, price, desc, saasId, sign, businessId);
    }


    /**
     * POST /v2/reward/receive : 领取奖励
     * 
     *
     * @param taskId  (required)
     * @param saasId  (required)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2RewardReceivePost",
        summary = "领取奖励",
        tags = { "RewardV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResult.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/reward/receive",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResult> v2RewardReceivePost(
        @NotNull @Parameter(name = "taskId", description = "", required = true) @Valid @RequestParam(value = "taskId", required = true) String taskId,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId
    ) {
        return getDelegate().v2RewardReceivePost(taskId, saasId);
    }

}
