/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.0.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.kikitrade.activity.generated.api.s2;

import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.activity.generated.model.s2.WebResultTaskFulfillVO;
import com.kikitrade.activity.generated.model.s2.WebResultVerifyVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "TaskS2", description = "the TaskS2 API")
public interface TaskS2Api {

    default TaskS2ApiDelegate getDelegate() {
        return new TaskS2ApiDelegate() {};
    }

    /**
     * POST /s2/tasks/fulfill : 做任务
     * 
     *
     * @param authorization  (required)
     * @param saasId saasId (required)
     * @param cid 用户id、address (required)
     * @param name 事件名称 (required)
     * @param contentId 当前事件对应的资源id (required)
     * @param eventTime 当前事件对应的时间戳 (required)
     * @param targetUserId 当前事件对应的资源的原资源用户id (optional)
     * @param targetContentId 当前事件对应的资源的原资源id (optional)
     * @param extendAttr 当前事件附加属性 (optional)
     * @return 成功 (status code 200)
     *         or 失败 (status code 400)
     */
    @Operation(
        operationId = "fulfillTask",
        summary = "做任务",
        tags = { "TaskS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResult.class))
            }),
            @ApiResponse(responseCode = "400", description = "失败", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResult.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/s2/tasks/fulfill",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResult> fulfillTask(
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "cid", description = "用户id、address", required = true) @Valid @RequestParam(value = "cid", required = true) String cid,
        @NotNull @Parameter(name = "name", description = "事件名称", required = true) @Valid @RequestParam(value = "name", required = true) String name,
        @NotNull @Parameter(name = "contentId", description = "当前事件对应的资源id", required = true) @Valid @RequestParam(value = "contentId", required = true) String contentId,
        @NotNull @Parameter(name = "eventTime", description = "当前事件对应的时间戳", required = true) @Valid @RequestParam(value = "eventTime", required = true) Long eventTime,
        @Parameter(name = "targetUserId", description = "当前事件对应的资源的原资源用户id") @Valid @RequestParam(value = "targetUserId", required = false) String targetUserId,
        @Parameter(name = "targetContentId", description = "当前事件对应的资源的原资源id") @Valid @RequestParam(value = "targetContentId", required = false) String targetContentId,
        @Parameter(name = "extendAttr", description = "当前事件附加属性") @Valid @RequestParam(value = "extendAttr", required = false) String extendAttr
    ) {
        return getDelegate().fulfillTask(authorization, saasId, cid, name, contentId, eventTime, targetUserId, targetContentId, extendAttr);
    }


    /**
     * GET /s2/tasks/fulfill/status : 任务状态查询
     * 
     *
     * @param authorization  (required)
     * @param saasId saasId (required)
     * @param cid 其他平台用户ID (required)
     * @param taskId 任务id (required)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "s2TasksFulfillStatusGet",
        summary = "任务状态查询",
        tags = { "TaskS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultTaskFulfillVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/s2/tasks/fulfill/status",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultTaskFulfillVO> s2TasksFulfillStatusGet(
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "cid", description = "其他平台用户ID", required = true) @Valid @RequestParam(value = "cid", required = true) String cid,
        @NotNull @Parameter(name = "taskId", description = "任务id", required = true) @Valid @RequestParam(value = "taskId", required = true) String taskId
    ) {
        return getDelegate().s2TasksFulfillStatusGet(authorization, saasId, cid, taskId);
    }


    /**
     * GET /s2/tasks/verify : 验证用户是否完成某个操作
     * 验证用户是否完成某个操作
     *
     * @param authorization  (required)
     * @param saasId saasId (required)
     * @param cid 其他平台用户ID (required)
     * @param scene 待验证的场景，目前只支持 follow_x (required)
     * @param ext 其他额外参数，json格式 (optional)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "s2TasksVerifyGet",
        summary = "验证用户是否完成某个操作",
        tags = { "TaskS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultVerifyVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/s2/tasks/verify",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultVerifyVO> s2TasksVerifyGet(
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "cid", description = "其他平台用户ID", required = true) @Valid @RequestParam(value = "cid", required = true) String cid,
        @NotNull @Parameter(name = "scene", description = "待验证的场景，目前只支持 follow_x", required = true) @Valid @RequestParam(value = "scene", required = true) String scene,
        @Parameter(name = "ext", description = "其他额外参数，json格式") @Valid @RequestParam(value = "ext", required = false) String ext
    ) {
        return getDelegate().s2TasksVerifyGet(authorization, saasId, cid, scene, ext);
    }

}
