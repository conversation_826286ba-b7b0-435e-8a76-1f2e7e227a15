/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.0.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.kikitrade.activity.generated.api.v2;

import com.kikitrade.activity.generated.model.v2.WebResultActiveTaskVO;
import com.kikitrade.activity.generated.model.v2.WebResultActiveTaskVOS;
import com.kikitrade.activity.generated.model.v2.WebResultCheckInStatusVO;
import com.kikitrade.activity.generated.model.v2.WebResultDoTaskVO;
import com.kikitrade.activity.generated.model.v2.WebResultLadderRewardVO;
import com.kikitrade.activity.generated.model.v2.WebResultSeriesTaskVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskDetailVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskPopVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskProgressVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskRewardStatVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskRewardVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskTweetVO;
import com.kikitrade.activity.generated.model.v2.WebResultTaskVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "TaskV2", description = "the TaskV2 API")
public interface TaskV2Api {

    default TaskV2ApiDelegate getDelegate() {
        return new TaskV2ApiDelegate() {};
    }

    /**
     * POST /v2/tasks/checkin : check in
     * check in
     *
     * @param saasId saasId (required)
     * @param idToken idToken (optional)
     * @param jwtToken jwt_token (optional)
     * @return success (status code 200)
     */
    @Operation(
        operationId = "checkIn",
        summary = "check in",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "success", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultActiveTaskVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/tasks/checkin",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultActiveTaskVO> checkIn(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "idToken", description = "idToken") @RequestHeader(value = "idToken", required = false) String idToken,
        @Parameter(name = "jwt_token", description = "jwt_token") @RequestHeader(value = "jwt_token", required = false) String jwtToken
    ) {
        return getDelegate().checkIn(saasId, idToken, jwtToken);
    }


    /**
     * GET /v2/tasks/checkin/status : check in
     * check in
     *
     * @param saasId saasId (required)
     * @param idToken idToken (optional)
     * @param jwtToken jwt_token (optional)
     * @return success (status code 200)
     */
    @Operation(
        operationId = "checkInStatus",
        summary = "check in",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "success", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultCheckInStatusVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/tasks/checkin/status",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultCheckInStatusVO> checkInStatus(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "idToken", description = "idToken") @RequestHeader(value = "idToken", required = false) String idToken,
        @Parameter(name = "jwt_token", description = "jwt_token") @RequestHeader(value = "jwt_token", required = false) String jwtToken
    ) {
        return getDelegate().checkInStatus(saasId, idToken, jwtToken);
    }


    /**
     * GET /v2/tasks/code/{code} : 根据code查询任务详情
     * 
     *
     * @param code  (required)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2TasksCodeCodeGet",
        summary = "根据code查询任务详情",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultSeriesTaskVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/tasks/code/{code}",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultSeriesTaskVO> v2TasksCodeCodeGet(
        @Parameter(name = "code", description = "", required = true) @PathVariable("code") String code
    ) {
        return getDelegate().v2TasksCodeCodeGet(code);
    }


    /**
     * GET /v2/tasks/detail : 任务详情
     * 
     *
     * @param taskId 任务id (optional)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2TasksDetailGet",
        summary = "任务详情",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultTaskDetailVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/tasks/detail",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultTaskDetailVO> v2TasksDetailGet(
        @Parameter(name = "taskId", description = "任务id") @Valid @RequestParam(value = "taskId", required = false) String taskId
    ) {
        return getDelegate().v2TasksDetailGet(taskId);
    }


    /**
     * POST /v2/tasks/doquests : 做任务
     * 
     *
     * @param userId 用户id、address (required)
     * @param name 事件名称 (required)
     * @param contentId 当前事件对应的资源id (required)
     * @param eventTime 当前事件对应的资源价钱 (required)
     * @param sign app授权码 (required)
     * @param saasId saasId (required)
     * @param targetUserId 当前事件对应的资源的原资源用户id (optional)
     * @param targetContentId 当前事件对应的资源的原资源id (optional)
     * @param extendAttr 当前事件附加属性 (optional)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2TasksDoquestsPost",
        summary = "做任务",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultTaskRewardVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/tasks/doquests",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultTaskRewardVO> v2TasksDoquestsPost(
        @NotNull @Parameter(name = "userId", description = "用户id、address", required = true) @Valid @RequestParam(value = "userId", required = true) String userId,
        @NotNull @Parameter(name = "name", description = "事件名称", required = true) @Valid @RequestParam(value = "name", required = true) String name,
        @NotNull @Parameter(name = "contentId", description = "当前事件对应的资源id", required = true) @Valid @RequestParam(value = "contentId", required = true) String contentId,
        @NotNull @Parameter(name = "eventTime", description = "当前事件对应的资源价钱", required = true) @Valid @RequestParam(value = "eventTime", required = true) Long eventTime,
        @Parameter(name = "sign", description = "app授权码", required = true) @RequestHeader(value = "sign", required = true) String sign,
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "targetUserId", description = "当前事件对应的资源的原资源用户id") @Valid @RequestParam(value = "targetUserId", required = false) String targetUserId,
        @Parameter(name = "targetContentId", description = "当前事件对应的资源的原资源id") @Valid @RequestParam(value = "targetContentId", required = false) String targetContentId,
        @Parameter(name = "extendAttr", description = "当前事件附加属性") @Valid @RequestParam(value = "extendAttr", required = false) String extendAttr
    ) {
        return getDelegate().v2TasksDoquestsPost(userId, name, contentId, eventTime, sign, saasId, targetUserId, targetContentId, extendAttr);
    }


    /**
     * POST /v2/tasks/dotask : 做任务
     * 
     *
     * @param taskId 任务id (required)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2TasksDotaskPost",
        summary = "做任务",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultActiveTaskVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/tasks/dotask",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultActiveTaskVO> v2TasksDotaskPost(
        @NotNull @Parameter(name = "taskId", description = "任务id", required = true) @Valid @RequestParam(value = "taskId", required = true) String taskId
    ) {
        return getDelegate().v2TasksDotaskPost(taskId);
    }


    /**
     * GET /v2/tasks/early/pop
     * 
     *
     * @param saasId saasId (required)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2TasksEarlyPopGet",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultTaskPopVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/tasks/early/pop",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultTaskPopVO> v2TasksEarlyPopGet(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId
    ) {
        return getDelegate().v2TasksEarlyPopGet(saasId);
    }


    /**
     * GET /v2/tasks/ladder/reward
     * 
     *
     * @param saasId saasId (required)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2TasksLadderRewardGet",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultLadderRewardVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/tasks/ladder/reward",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultLadderRewardVO> v2TasksLadderRewardGet(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId
    ) {
        return getDelegate().v2TasksLadderRewardGet(saasId);
    }


    /**
     * GET /v2/tasks/list : 获取任务列表
     * 
     *
     * @param saasId saas_id (optional)
     * @param channel channel (optional, default to app)
     * @param position home (optional)
     * @param clientType ios/android (optional)
     * @param appVersion app version (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "v2TasksListGet",
        summary = "获取任务列表",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultTaskVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/tasks/list",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultTaskVO> v2TasksListGet(
        @Parameter(name = "saas_id", description = "saas_id") @RequestHeader(value = "saas_id", required = false) String saasId,
        @Parameter(name = "channel", description = "channel") @Valid @RequestParam(value = "channel", required = false, defaultValue = "app") String channel,
        @Parameter(name = "position", description = "home") @Valid @RequestParam(value = "position", required = false) String position,
        @Parameter(name = "clientType", description = "ios/android") @Valid @RequestParam(value = "clientType", required = false) String clientType,
        @Parameter(name = "appVersion", description = "app version") @Valid @RequestParam(value = "appVersion", required = false) String appVersion
    ) {
        return getDelegate().v2TasksListGet(saasId, channel, position, clientType, appVersion);
    }


    /**
     * GET /v2/tasks/progress
     * 
     *
     * @param saasId saasId (required)
     * @param taskId 任务id (required)
     * @param type task or reward, 暂时只支持reward (required)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2TasksProgressGet",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultTaskProgressVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/tasks/progress",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultTaskProgressVO> v2TasksProgressGet(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "taskId", description = "任务id", required = true) @Valid @RequestParam(value = "taskId", required = true) String taskId,
        @NotNull @Parameter(name = "type", description = "task or reward, 暂时只支持reward", required = true) @Valid @RequestParam(value = "type", required = true) String type
    ) {
        return getDelegate().v2TasksProgressGet(saasId, taskId, type);
    }


    /**
     * GET /v2/tasks/reward/by/code
     * 
     *
     * @param saasId saasId (required)
     * @param code code (required)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2TasksRewardByCodeGet",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultTaskRewardStatVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/tasks/reward/by/code",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultTaskRewardStatVO> v2TasksRewardByCodeGet(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "code", description = "code", required = true) @Valid @RequestParam(value = "code", required = true) List<String> code
    ) {
        return getDelegate().v2TasksRewardByCodeGet(saasId, code);
    }


    /**
     * POST /v2/tasks/sync/doquests : 同步做任务
     * 
     *
     * @param taskId 任务id (required)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2TasksSyncDoquestsPost",
        summary = "同步做任务",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultActiveTaskVOS.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/tasks/sync/doquests",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultActiveTaskVOS> v2TasksSyncDoquestsPost(
        @NotNull @Parameter(name = "taskId", description = "任务id", required = true) @Valid @RequestParam(value = "taskId", required = true) String taskId
    ) {
        return getDelegate().v2TasksSyncDoquestsPost(taskId);
    }


    /**
     * GET /v2/tasks/twitter/tweets/{taskId} : 查询twitter最近的帖子
     * 
     *
     * @param saasId saasId (required)
     * @param taskId taskId (required)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2TasksTwitterTweetsTaskIdGet",
        summary = "查询twitter最近的帖子",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultTaskTweetVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v2/tasks/twitter/tweets/{taskId}",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultTaskTweetVO> v2TasksTwitterTweetsTaskIdGet(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "taskId", description = "taskId", required = true) @PathVariable("taskId") String taskId
    ) {
        return getDelegate().v2TasksTwitterTweetsTaskIdGet(saasId, taskId);
    }


    /**
     * POST /v2/tasks/verify : 做任务
     * 
     *
     * @param taskId 任务id (required)
     * @param saasId saasId (required)
     * @param ext 其他参数，如邀请链接 (optional)
     * @return 成功 (status code 200)
     */
    @Operation(
        operationId = "v2TasksVerifyPost",
        summary = "做任务",
        tags = { "TaskV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "成功", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultDoTaskVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/v2/tasks/verify",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultDoTaskVO> v2TasksVerifyPost(
        @NotNull @Parameter(name = "taskId", description = "任务id", required = true) @Valid @RequestParam(value = "taskId", required = true) String taskId,
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "ext", description = "其他参数，如邀请链接") @Valid @RequestParam(value = "ext", required = false) String ext
    ) {
        return getDelegate().v2TasksVerifyPost(taskId, saasId, ext);
    }

}
