package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * SubmitAnswerVOQuestionsInner
 */

@JsonTypeName("SubmitAnswerVO_questions_inner")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class SubmitAnswerVOQuestionsInner {

  @JsonProperty("id")
  private String id;

  @JsonProperty("user_option")
  private String userOption;

  public SubmitAnswerVOQuestionsInner id(String id) {
    this.id = id;
    return this;
  }

  /**
   * The ID of the question
   * @return id
  */
  
  @Schema(name = "id", description = "The ID of the question", required = false)
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public SubmitAnswerVOQuestionsInner userOption(String userOption) {
    this.userOption = userOption;
    return this;
  }

  /**
   * The answer to the question
   * @return userOption
  */
  
  @Schema(name = "user_option", description = "The answer to the question", required = false)
  public String getUserOption() {
    return userOption;
  }

  public void setUserOption(String userOption) {
    this.userOption = userOption;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SubmitAnswerVOQuestionsInner submitAnswerVOQuestionsInner = (SubmitAnswerVOQuestionsInner) o;
    return Objects.equals(this.id, submitAnswerVOQuestionsInner.id) &&
        Objects.equals(this.userOption, submitAnswerVOQuestionsInner.userOption);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, userOption);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SubmitAnswerVOQuestionsInner {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    userOption: ").append(toIndentedString(userOption)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

