package com.kikitrade.activity.generated.api.v2;

import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.activity.generated.model.v2.WebResultGoodsListVO;
import com.kikitrade.activity.generated.model.v2.WebResultGoodsVO;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link GoodsV2ApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface GoodsV2ApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * GET /v2/goods/detail : 商品详情
     * 
     *
     * @param goodsId 商品id (optional)
     * @param saasId saas_id (optional)
     * @return 成功 (status code 200)
     * @see GoodsV2Api#v2GoodsDetailGet
     */
    default ResponseEntity<WebResultGoodsVO> v2GoodsDetailGet(String goodsId,
        String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/goods/list : 商品列表
     * 
     *
     * @param offset  (optional)
     * @param limit  (optional, default to 8)
     * @param saasId saas_id (optional)
     * @param exclude exclude (optional)
     * @return 成功 (status code 200)
     * @see GoodsV2Api#v2GoodsListGet
     */
    default ResponseEntity<WebResultGoodsListVO> v2GoodsListGet(Integer offset,
        Integer limit,
        String saasId,
        String exclude) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /v2/goods/redeem : 兑换商品
     * 
     *
     * @param goodsId  (required)
     * @param totalPrice  (required)
     * @param quality  (required)
     * @param timestamp  (required)
     * @param sign  (required)
     * @param saasId saas_id (optional)
     * @return 成功 (status code 200)
     * @see GoodsV2Api#v2GoodsRedeemPost
     */
    default ResponseEntity<WebResult> v2GoodsRedeemPost(String goodsId,
        Integer totalPrice,
        Integer quality,
        Long timestamp,
        String sign,
        String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
