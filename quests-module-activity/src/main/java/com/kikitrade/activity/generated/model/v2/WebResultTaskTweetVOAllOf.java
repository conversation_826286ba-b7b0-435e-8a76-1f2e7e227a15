package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * WebResultTaskTweetVOAllOf
 */

@JsonTypeName("WebResultTaskTweetVO_allOf")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class WebResultTaskTweetVOAllOf {

  @JsonProperty("obj")
  @Valid
  private List<String> obj = null;

  public WebResultTaskTweetVOAllOf obj(List<String> obj) {
    this.obj = obj;
    return this;
  }

  public WebResultTaskTweetVOAllOf addObjItem(String objItem) {
    if (this.obj == null) {
      this.obj = new ArrayList<>();
    }
    this.obj.add(objItem);
    return this;
  }

  /**
   * Get obj
   * @return obj
  */
  
  @Schema(name = "obj", required = false)
  public List<String> getObj() {
    return obj;
  }

  public void setObj(List<String> obj) {
    this.obj = obj;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WebResultTaskTweetVOAllOf webResultTaskTweetVOAllOf = (WebResultTaskTweetVOAllOf) o;
    return Objects.equals(this.obj, webResultTaskTweetVOAllOf.obj);
  }

  @Override
  public int hashCode() {
    return Objects.hash(obj);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WebResultTaskTweetVOAllOf {\n");
    sb.append("    obj: ").append(toIndentedString(obj)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

