package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.activity.generated.model.v2.Winner;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * LotteryWinnersVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class LotteryWinnersVO {

  @JsonProperty("poolCode")
  private String poolCode;

  @JsonProperty("poolWinners")
  @Valid
  private List<Winner> poolWinners = null;

  public LotteryWinnersVO poolCode(String poolCode) {
    this.poolCode = poolCode;
    return this;
  }

  /**
   * 奖池标识
   * @return poolCode
  */
  
  @Schema(name = "poolCode", description = "奖池标识", required = false)
  public String getPoolCode() {
    return poolCode;
  }

  public void setPoolCode(String poolCode) {
    this.poolCode = poolCode;
  }

  public LotteryWinnersVO poolWinners(List<Winner> poolWinners) {
    this.poolWinners = poolWinners;
    return this;
  }

  public LotteryWinnersVO addPoolWinnersItem(Winner poolWinnersItem) {
    if (this.poolWinners == null) {
      this.poolWinners = new ArrayList<>();
    }
    this.poolWinners.add(poolWinnersItem);
    return this;
  }

  /**
   * 各奖池的中奖者列表，key为奖池标识(poolCode)，value为该奖池的中奖者列表
   * @return poolWinners
  */
  @Valid 
  @Schema(name = "poolWinners", description = "各奖池的中奖者列表，key为奖池标识(poolCode)，value为该奖池的中奖者列表", required = false)
  public List<Winner> getPoolWinners() {
    return poolWinners;
  }

  public void setPoolWinners(List<Winner> poolWinners) {
    this.poolWinners = poolWinners;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LotteryWinnersVO lotteryWinnersVO = (LotteryWinnersVO) o;
    return Objects.equals(this.poolCode, lotteryWinnersVO.poolCode) &&
        Objects.equals(this.poolWinners, lotteryWinnersVO.poolWinners);
  }

  @Override
  public int hashCode() {
    return Objects.hash(poolCode, poolWinners);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LotteryWinnersVO {\n");
    sb.append("    poolCode: ").append(toIndentedString(poolCode)).append("\n");
    sb.append("    poolWinners: ").append(toIndentedString(poolWinners)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

