package com.kikitrade.activity.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.kikitrade.activity.generated.model.v2.LotteryWinnersVO;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * WebResultLotteryWinnersVOAllOf
 */

@JsonTypeName("WebResultLotteryWinnersVO_allOf")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class WebResultLotteryWinnersVOAllOf {

  @JsonProperty("obj")
  @Valid
  private List<LotteryWinnersVO> obj = null;

  public WebResultLotteryWinnersVOAllOf obj(List<LotteryWinnersVO> obj) {
    this.obj = obj;
    return this;
  }

  public WebResultLotteryWinnersVOAllOf addObjItem(LotteryWinnersVO objItem) {
    if (this.obj == null) {
      this.obj = new ArrayList<>();
    }
    this.obj.add(objItem);
    return this;
  }

  /**
   * Get obj
   * @return obj
  */
  @Valid 
  @Schema(name = "obj", required = false)
  public List<LotteryWinnersVO> getObj() {
    return obj;
  }

  public void setObj(List<LotteryWinnersVO> obj) {
    this.obj = obj;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WebResultLotteryWinnersVOAllOf webResultLotteryWinnersVOAllOf = (WebResultLotteryWinnersVOAllOf) o;
    return Objects.equals(this.obj, webResultLotteryWinnersVOAllOf.obj);
  }

  @Override
  public int hashCode() {
    return Objects.hash(obj);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WebResultLotteryWinnersVOAllOf {\n");
    sb.append("    obj: ").append(toIndentedString(obj)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

