package com.kikitrade.activity.delegate;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.api.RemoteLotteryService;
import com.kikitrade.activity.api.exception.ActivityException;
import com.kikitrade.activity.api.model.LotteryConfigDTO;
import com.kikitrade.activity.api.model.LotteryResponse;
import com.kikitrade.activity.api.model.response.LotteryCountResponse;
import com.kikitrade.activity.api.model.response.LotteryWinnersResponse;
import com.kikitrade.activity.generated.api.v2.LotteryV2ApiDelegate;
import com.kikitrade.activity.generated.model.v2.*;
import com.kikitrade.activity.model.domain.ActivityCumulateConfig;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.asset.model.exception.AssetException;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.interceptor.CustomerHolder;
import com.kikitrade.kweb.utils.ResponseEntityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/1/8 15:45
 */
@Service
@Slf4j
public class LotteryV2ApiDelegateImpl implements LotteryV2ApiDelegate {

    @DubboReference
    private RemoteLotteryService remoteLotteryService;

    /**
     * POST /activities/lottery/{type}/open : 抽奖
     * 抽奖
     *
     * @param code   (required)
     * @param saasId (required)
     * @return Successful operation (status code 200)
     */
    @Override
    public ResponseEntity<WebResultLotteryVO> drew(String code, String saasId) {
        try{
            LotteryResponse lottery = remoteLotteryService.lottery(CustomerHolder.qcustomer().getUid(), code, saasId);
            WebResultLotteryVO lotteryVO = new WebResultLotteryVO();
            lotteryVO.setResponseEnum(WebResponseEnum.SUCCESS);
            lotteryVO.setObj(BeanUtil.copyProperties(lottery, LotteryVO::new));
            return ResponseEntityUtil.result(lotteryVO);
        }catch (ActivityException ex){
            log.error("ActivityV2ApiDelegate drew error", ex);
            return ResponseEntityUtil.result(WebResponseEnum.LOTTERY_PRODUCT_LIMIT);
        }catch (AssetException ex){
            log.error("ActivityV2ApiDelegate drew error", ex);
            return ResponseEntityUtil.result(WebResponseEnum.POINT_NOT_ENOUGH);
        }catch (Exception ex){
            log.error("ActivityV2ApiDelegate drew error", ex);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
    }

    @Override
    public ResponseEntity<WebResultLotteryInfoVO> lotteryInfo(String saasId, String code) {

        try{
            List<LotteryInfoVO> items = new ArrayList<>();
            for(String c : code.split(",")){
                LotteryConfigDTO lotteryInfo = remoteLotteryService.lotteryInfo(c, saasId);
                if(lotteryInfo == null){
                    continue;
                }
                LotteryInfoVO info = new LotteryInfoVO();
                info.setCode(lotteryInfo.getCode());
                info.setType(lotteryInfo.getType());
                info.setTotal(lotteryInfo.getLimitTimes());

                CustomerBindDTO qcustomer = CustomerHolder.qcustomer();
                Integer count = 0;
                if(qcustomer != null){
                    LotteryCountResponse lotteryCountDetail = remoteLotteryService.lotteryCountDetail(qcustomer.getUid(), c, saasId);
                    if(lotteryCountDetail != null){
                        count = Optional.ofNullable(lotteryCountDetail.getLatestCycleCount()).map(Long::intValue).orElse(0);
                        info.setOpened(count);

                        if(BooleanUtil.isTrue(lotteryInfo.getIsCumulate())){
                            Integer historyCount = Optional.ofNullable(lotteryCountDetail.getHistoryCount()).map(Long::intValue).orElse(0);
                            info.setCumulateTotal(historyCount);
                            ActivityCumulateConfig nextCumulateConfig = getNextCumulateConfig(lotteryInfo.getCumulateInfo(), historyCount);
                            if (nextCumulateConfig != null) {
                                info.setNextCumulateAward(new BigDecimal(nextCumulateConfig.getRewardAmount()));
                                info.setNextCumulateCnt(nextCumulateConfig.getCumulateTotal() - historyCount);
                            }
                        }
                    }
                }
                if(count.intValue() > 0){
                    if("weekly".equals(lotteryInfo.getLimitUnit())){
                        OffsetDateTime nextMonday0AM = OffsetDateTime.of(
                                LocalDate.now().with(TemporalAdjusters.next(DayOfWeek.MONDAY)),
                                LocalTime.MIDNIGHT,
                                ZoneOffset.of("+00:00")
                        );
                        info.setNextOpenTime(nextMonday0AM.toEpochSecond() * 1000);
                    }else{
                        info.setNextOpenTime(TimeUtil.addDay(TimeUtil.parse(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD_000000)), 1).getTime());
                    }
                }
                items.add(info);
            }

            WebResultLotteryInfoVO webResult = new WebResultLotteryInfoVO();
            webResult.setResponseEnum(WebResponseEnum.SUCCESS);
            webResult.setObj(items);
            return ResponseEntityUtil.result(webResult);
        }catch (Exception ex){
            log.error("LotteryV2ApiDelegateImpl lotteryInfo error", ex);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
    }

    @Override
    public ResponseEntity<WebResultLotteryWinnersVO> lotteryWinners(String saasId, String code) {
        try {
            LotteryWinnersResponse lotteryWinners = remoteLotteryService.listLotteryWinners(saasId, code);

            WebResultLotteryWinnersVO result = new WebResultLotteryWinnersVO();
            result.setResponseEnum(WebResponseEnum.SUCCESS);
            result.setObj(new ArrayList<>());

            if (lotteryWinners != null && CollectionUtils.isNotEmpty(lotteryWinners.getLotteryWinners())) {
                List<LotteryWinnersVO> winnersVOList = new ArrayList<>();

                lotteryWinners.getLotteryWinners().forEach(lotteryWinner -> {
                    LotteryWinnersVO winnersVO = new LotteryWinnersVO();
                    winnersVO.setPoolCode(lotteryWinner.getPoolCode());

                    List<Winner> winnerList = new ArrayList<>();
                    if (lotteryWinner.getPoolWinners() != null) {
                        lotteryWinner.getPoolWinners().forEach(winnerInfo -> {
                            Winner winner = new Winner();
                            winner.setAddress(winnerInfo.getAddress());
                            winner.setOrder(winnerInfo.getOrder());
                            winnerList.add(winner);
                        });
                    }

                    winnersVO.setPoolWinners(winnerList);
                    winnersVOList.add(winnersVO);
                });

                result.setObj(winnersVOList);
            }

            return ResponseEntityUtil.result(result);
        } catch (Exception ex) {
            log.error("LotteryV2ApiDelegateImpl lotteryWinners error", ex);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
    }

    private ActivityCumulateConfig getNextCumulateConfig(String info, Integer historyCount){
        List<ActivityCumulateConfig> activityCumulateConfigs = JSONObject.parseArray(info, ActivityCumulateConfig.class);
        if(CollectionUtils.isNotEmpty(activityCumulateConfigs)){
            ActivityCumulateConfig cumulateNext = null;
            for(ActivityCumulateConfig cumulateConfig : activityCumulateConfigs){
                if(cumulateConfig.getCumulateTotal() > historyCount){
                    cumulateNext = cumulateConfig;
                    break;
                }
            }
            return cumulateNext;
        }
        return null;
    }
}
