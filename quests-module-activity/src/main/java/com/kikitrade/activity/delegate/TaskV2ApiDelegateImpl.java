package com.kikitrade.activity.delegate;

import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.api.RemoteAuthService;
import com.kikitrade.activity.api.RemoteTaskService;
import com.kikitrade.activity.api.model.ActivityTaskDTO;
import com.kikitrade.activity.api.model.CheckInStatusDTO;
import com.kikitrade.activity.api.model.request.TaskListRequest;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.api.model.response.TaskListResponse;
import com.kikitrade.activity.api.model.response.*;
import com.kikitrade.activity.generated.api.v2.TaskV2ApiDelegate;
import com.kikitrade.activity.generated.model.v2.NodeVO;
import com.kikitrade.activity.generated.model.v2.*;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kevent.client.EventClient;
import com.kikitrade.kevent.common.model.EventDTO;
import com.kikitrade.kweb.config.QuestWebProperties;
import com.kikitrade.kweb.config.TaskCodeConfig;
import com.kikitrade.kweb.constants.WebConstants;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.interceptor.CustomerHolder;
import com.kikitrade.kweb.model.customer.QCustomer;
import com.kikitrade.kweb.service.KwebBaseService;
import com.kikitrade.kweb.utils.PemUtils;
import com.kikitrade.kweb.utils.ResponseEntityUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.MDC;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.*;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.time.DayOfWeek;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/27 16:30
 */
@Service
@Slf4j
public class TaskV2ApiDelegateImpl implements TaskV2ApiDelegate {

    @DubboReference
    private RemoteTaskService remoteTaskService;
    @Resource
    private EventClient eventClient;
    @Resource
    private QuestWebProperties questWebProperties;
    @Resource
    private KwebBaseService kwebBaseService;
    @DubboReference
    private RemoteAuthService remoteAuthService;
    @Resource
    private PemUtils pemUtils;

    private static final String REFRESH_TOKEN_TWITTER_URL = "https://api.twitter.com/2/oauth2/token";
    private static final String REFRESH_TOKEN_DISCORD_URL = "https://discord.com/api/v10/oauth2/token";
    private static final String ENCRYPT_MAX_POINT_MULTIPLIER = "encryptMaxPointMultiplier";
    private static final String POINT_MULTIPLIER = "pointMultiplier";
    private static final String LADDER_REWARD_CODE = "ladder_reward";

    @Override
    public ResponseEntity<WebResultTaskDetailVO> v2TasksDetailGet(String taskId) {

        try{
            QCustomer customer = CustomerHolder.qcustomer();
            TaskDetailResponse task = null;
            if(customer != null){
                task = remoteTaskService.getTask(taskId, customer.getUid());
            }else{
                task = remoteTaskService.getTask(taskId, null);
            }

            TaskDetailVO vo = BeanUtil.copyProperties(task, TaskDetailVO::new);
            vo.setRewards(BeanUtil.copyProperties(task.getRewards(), Award::new));
            List<TaskSubVO> subVOList = new ArrayList<>();
            for(TaskVO v : task.getSubTasks()){
                TaskSubVO t = BeanUtil.copyProperties(v, TaskSubVO::new);
                t.setRewards(BeanUtil.copyProperties(v.getRewards(), Award::new));
                t.setDomain(v.getDomain());
                t.setPlatform(v.getDomain());
                if(customer != null && v.getAttr().get("register-time") != null){
                    String rt = v.getAttr().get("register-time");
                    String cid = customer.getCid();
                    if(cid.substring(0, rt.length()).compareTo(rt) >= 0){
                        t.setStatus(-1);
                    }
                }
                subVOList.add(t);
            }
            vo.setSubTasks(subVOList);

            WebResultTaskDetailVO detailVO = new WebResultTaskDetailVO();
            detailVO.setSuccess(WebResponseEnum.SUCCESS.isSuccess());
            detailVO.setMsgKey(WebResponseEnum.SUCCESS.getKey());
            detailVO.setCode(WebResponseEnum.SUCCESS.getCode());
            detailVO.setObj(vo);

            return ResponseEntityUtil.result(detailVO);
        }catch (Exception ex){
            log.error("v2TasksDetailGet", ex);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
    }

    @Override
    public ResponseEntity<WebResultActiveTaskVO> v2TasksDotaskPost(String taskId) {
        try{
            CustomerBindDTO qcustomer = CustomerHolder.qcustomer();
            MDC.put("customerId", qcustomer == null ? "null" : qcustomer.getCid());
            log.info("user_track 【task】【check_in {}】",  taskId);
            WebResultActiveTaskVO taskVO = new WebResultActiveTaskVO();
            ActivityTaskDTO activityTaskDTO = new ActivityTaskDTO();
            activityTaskDTO.setCustomerId(qcustomer.getUid());
            activityTaskDTO.setEventTime(new Date().getTime());
            activityTaskDTO.setTaskId(taskId);
            ActivityResponse<List<com.kikitrade.activity.model.domain.Award>> task = remoteTaskService.task(activityTaskDTO);

            taskVO.setResponseEnum(WebResponseEnum.SUCCESS);
            if (Objects.nonNull(task) && Objects.nonNull(task.getObj()) && CollectionUtils.isNotEmpty(task.getObj())) {
                taskVO.setObj(BeanUtil.copyProperties(task.getObj().get(0), Award::new));
            }
            MDC.remove("customerId");
            return ResponseEntityUtil.result(taskVO);
       }catch (Exception ex){
            log.error("user_track【task】v2TasksDotaskPost error:{}", taskId, ex);
            MDC.remove("customerId");
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
       }
    }

    @Override
    public ResponseEntity<WebResultActiveTaskVOS> v2TasksSyncDoquestsPost(String taskId) {
        try{
            CustomerBindDTO qcustomer = CustomerHolder.qcustomer();
            MDC.put("customerId", qcustomer == null ? "null" : qcustomer.getCid());
            log.info("user_track 【syncQuests】【sync_do_quests {}】",  taskId);
            WebResultActiveTaskVOS taskVOS = new WebResultActiveTaskVOS();
            ActivityTaskDTO activityTaskDTO = new ActivityTaskDTO();
            activityTaskDTO.setCustomerId(qcustomer.getUid());
            activityTaskDTO.setEventTime(new Date().getTime());
            activityTaskDTO.setTaskId(taskId);
            ActivityResponse<List<com.kikitrade.activity.model.domain.Award>> task = remoteTaskService.task(activityTaskDTO);

            taskVOS.setResponseEnum(WebResponseEnum.SUCCESS);
            if (Objects.nonNull(task) && Objects.nonNull(task.getObj()) && CollectionUtils.isNotEmpty(task.getObj())) {
                taskVOS.setObj(BeanUtil.copyProperties(task.getObj(), Award::new));
            }
            MDC.remove("customerId");
            return ResponseEntityUtil.result(taskVOS);
        }catch (Exception ex){
            log.error("user_track【syncQuests】v2TasksSyncDoquestsPost error:{}", taskId, ex);
            MDC.remove("customerId");
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 第三方直接调用
     * @param userId 用户id、address (required)
     * @param name 事件名称 (required)
     * @param contentId 当前事件对应的资源id (required)
     * @param eventTime 当前事件对应的资源价钱 (required)
     * @param sign app授权码 (required)
     * @param saasId saasId (required)
     * @param targetUserId 当前事件对应的资源的原资源用户id (optional)
     * @param targetContentId 当前事件对应的资源的原资源id (optional)
     * @param attr 当前事件附加属性 (optional)
     * @return
     */
    @Override
    public ResponseEntity<WebResultTaskRewardVO> v2TasksDoquestsPost(String userId, String name, String contentId, Long eventTime, String sign, String saasId, String targetUserId, String targetContentId, String attr) {
        log.info("[do-task]  user_track【task】 Start doquests, userId: {}, name: {}, contentId: {}, eventTime: {}, sign: {}, saasId: {}, targetUserId: {}, targetContentId: {}, attr: {}",
                userId, name, contentId, eventTime, sign, saasId, targetUserId, targetContentId, attr);

        TaskCodeConfig taskCodeConfig = TaskCodeConfig.getValue(name);
        if (taskCodeConfig == null) {
            log.warn("[do-task] Unsupported event_code: {}", name);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
        log.info("[do-task] TaskCodeConfig for name: {}, config: {}", name, JSON.toJSONString(taskCodeConfig));

        try {
            String app = getAppFromSign(sign);
            if (questWebProperties.getTWebCustomerSign().get(app) == null) {
                log.info("[do-task] user_track【task】 No permission for sign: {}", sign);
                return ResponseEntityUtil.result(WebResponseEnum.APP_FORBIDDEN);
            }

            if ("registration".equals(name) && (StringUtils.isBlank(userId) || StringUtils.isBlank(targetUserId))) {
                log.info("[do-task] user_track【task】 Incomplete registration data, userId: {}, targetUserId: {}", userId, targetUserId);
                return ResponseEntityUtil.result(WebResponseEnum.SUCCESS);
            }

            JSONObject jsonObject = createJsonObject(attr, saasId);
            if ("registration".equals(name)) {
                pushEvent(jsonObject, saasId, name, userId, eventTime, contentId, targetUserId, targetContentId);
            } else {
                pushSplitEvent(jsonObject, saasId, name, userId, eventTime, contentId, taskCodeConfig, targetUserId, targetContentId);
            }

            log.info("[do-task] user_track【task】 Successfully processed doquests for userId: {}", userId);
            return ResponseEntityUtil.result(WebResponseEnum.SUCCESS);
        } catch (Exception ex) {
            log.error("[do-task] user_track【task】 Error processing doquests for userId: {}", userId, ex);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
    }

    private String getAppFromSign(String sign) throws UnsupportedEncodingException {
        String decodedSign = new String(Base64.getDecoder().decode(sign), "UTF-8");
        return decodedSign.split(":")[2];
    }

    private JSONObject createJsonObject(String attr, String saasId) {
        JSONObject jsonObject = new JSONObject();
        if (attr != null) {
            if (JSONUtil.isTypeJSON(attr)) {
                jsonObject.fluentPutAll(JSON.parseObject(attr));
                if (jsonObject.containsKey(ENCRYPT_MAX_POINT_MULTIPLIER)) {
                    RSA rsa = pemUtils.getRsa();
                    String multiplier = rsa.decryptStr(String.valueOf(jsonObject.get(ENCRYPT_MAX_POINT_MULTIPLIER)), KeyType.PrivateKey);
                    jsonObject.put(POINT_MULTIPLIER, multiplier);
                }
            } else {
                jsonObject.put("price", Double.valueOf(attr));
            }
        }
        jsonObject.put("saasId", saasId);
        return jsonObject;
    }

    public void pushEvent(JSONObject jsonObject, String saasId, String name, String userId, Long eventTime, String contentId, String targetUserId, String targetContentId) {
        EventDTO eventDTO = new EventDTO();
        jsonObject.put("targetId", targetContentId);
        if(StringUtils.isNotBlank(targetUserId)){
            QCustomer qCustomer = kwebBaseService.getByAddress(saasId, targetUserId);
            if(qCustomer != null){
                jsonObject.put("targetCustomerId", qCustomer.getUid() == null ? "-1" : qCustomer.getUid());
                jsonObject.put("targetVipLevel", qCustomer.getVipLevel());
            }
        }
        QCustomer customer = kwebBaseService.getByAddress(saasId, userId);
        if(customer != null){
            eventDTO.setCustomerId(customer.getUid());
            jsonObject.put("vipLevel", customer.getVipLevel());
        }else{
            eventDTO.setCustomerId("-1");
        }
        eventDTO.setTime(eventTime);
        eventDTO.setGlobalUid(contentId);
        eventDTO.setName(name);
        eventDTO.setBody(jsonObject);
        log.info("[do-task] user_track【task】 pushEvent:{}", eventDTO);
        MDC.put("customerId", eventDTO.getCustomerId());
        eventClient.push(eventDTO);
        MDC.remove("customerId");
    }

    public void pushSplitEvent(JSONObject jsonObject, String saasId, String name, String userId, Long eventTime, String contentId, TaskCodeConfig taskCodeConfig, String targetUserId, String targetContentId) {
        EventDTO eventDTO = new EventDTO();
        QCustomer customer = kwebBaseService.getByAddress(saasId, userId);
        if(customer != null){
            eventDTO.setCustomerId(customer.getUid());
            jsonObject.put("vipLevel", customer.getVipLevel());
        }else{
            eventDTO.setCustomerId("-1");
        }
        eventDTO.setTime(eventTime);
        eventDTO.setGlobalUid(contentId);
        eventDTO.setName(name);
        eventDTO.setBody(jsonObject);
        String splitCode = taskCodeConfig.getSplitCode();
        if (Objects.isNull(splitCode)) {
            //不设置拆分事件时，默认使用主事件
            splitCode = name;
        }
        for (String code: splitCode.split(WebConstants.COMMA)) {
            eventDTO.setName(code);
            if (code.endsWith(WebConstants.UNDERLINE)) {
                if (StringUtils.isNotBlank(targetUserId)) {
                    if (targetContentId == null) {
                        targetContentId = contentId;
                    }
                    eventDTO.setGlobalUid(targetContentId + userId);
                    jsonObject.put("sourceCustomerId", eventDTO.getCustomerId());
                    QCustomer qCustomer = kwebBaseService.getByAddress(saasId, targetUserId);
                    if (qCustomer != null) {
                        eventDTO.setCustomerId(qCustomer.getUid() == null ? "-1" : qCustomer.getUid());
                        jsonObject.put("vipLevel", qCustomer.getVipLevel());
                    } else {
                        eventDTO.setCustomerId("-1");
                    }
                } else {
                    log.debug("[do-task] Skip event code: {} due to empty targetUserId", code);
                    continue;
                }
                eventDTO.setName(code.substring(0, code.length() - 1));
            }
            log.info("[do-task] user_track【task】 pushSplitEvent:{}", JSON.toJSONString(eventDTO));
            MDC.put("customerId", eventDTO.getCustomerId());
            eventClient.push(eventDTO);
            MDC.remove("customerId");
        }
    }

    @Override
    public ResponseEntity<WebResultTaskTweetVO> v2TasksTwitterTweetsTaskIdGet(String saasId, String taskId) {
        List<String> twitterKols = remoteTaskService.getTwitterKols(taskId);
        WebResultTaskTweetVO tweetVO = new WebResultTaskTweetVO();
        tweetVO.setResponseEnum(WebResponseEnum.SUCCESS);
        tweetVO.setObj(twitterKols);
        return ResponseEntityUtil.result(tweetVO);
    }

    @Override
    public ResponseEntity<WebResultDoTaskVO> v2TasksVerifyPost(String taskId, String saasId, String ext) {
        log.info("user_track【task】Starting v2TasksVerifyPost with taskId: {}, saasId: {}, ext: {}", taskId, saasId, ext);
        WebResultDoTaskVO webResult = new WebResultDoTaskVO();
        QCustomer qcustomer = CustomerHolder.qcustomer();
        try {
            Token token = createToken(qcustomer);
            ext = decryptExt(ext, saasId);
            MDC.put("customerId", qcustomer == null ? "null" : qcustomer.getCid());
            VerifyResponse verifyResponse = remoteTaskService.verify(token, taskId, saasId, ext);
            MDC.put("customerId", qcustomer == null ? "null" : qcustomer.getCid());
            log.info("Verify response received: {}", verifyResponse);
            return buildResponse(verifyResponse, webResult);
        } catch (ActivityException e) {
            log.error("[task] verify error", e);
            return handleActivityException(e, webResult);
        } catch (Exception ex) {
            log.error("[task] verify error: {}, {}", taskId, qcustomer, ex);
            webResult.setResponseEnum(WebResponseEnum.TASK_VERIFY_NO_PASS);
            return ResponseEntityUtil.result(webResult);
        }
    }

    private Token createToken(QCustomer qcustomer) {
        log.debug("Creating token for customer: {}", qcustomer);
        Token token = new Token();
        token.setLoginCustomerId(qcustomer.getUid());
        token.setVipLevel(qcustomer.getVipLevel());
        token.setAddress(qcustomer.getAddressAll());
        return token;
    }

    private String decryptExt(String ext, String saasId) {
        log.debug("Decrypting ext: {}", ext);
        if (StringUtils.isNotBlank(ext)) {
            RSA rsa = pemUtils.getRsa();
            if (rsa != null) {
                Map<String, Object> map = JSON.parseObject(ext, Map.class);
                if (map.containsKey(ENCRYPT_MAX_POINT_MULTIPLIER)) {
                    String multiplier = rsa.decryptStr(String.valueOf(map.get(ENCRYPT_MAX_POINT_MULTIPLIER)), KeyType.PrivateKey);
                    map.put(ENCRYPT_MAX_POINT_MULTIPLIER, multiplier);
                    ext = JSON.toJSONString(map);
                }
            }
        }
        String jwtToken = CustomerHolder.customerContext().getJwtToken();
        //将jwtToken通过ext字段传到kactivity，用于profile验证
        if(questWebProperties.getTWebCustomerJwt().get(saasId) != null) {
            Map extMap = new HashMap();
            if (StringUtils.isNotBlank(ext)) {
                extMap = JSON.parseObject(ext, Map.class);
            }
            extMap.put(WebConstants.JWT_TOKEN, jwtToken);
            ext = JSON.toJSONString(extMap);
        }
        return ext;
    }

    private ResponseEntity<WebResultDoTaskVO> buildResponse(VerifyResponse verifyResponse, WebResultDoTaskVO webResult) {
        log.debug("Building response for verifyResponse: {}", verifyResponse);
        DoTaskVO doTaskVO = new DoTaskVO();
        doTaskVO.setAccessToken(verifyResponse.getAccessToken());
        doTaskVO.setRefreshToken(verifyResponse.getRefreshToken());
        doTaskVO.setPostUrl(verifyResponse.getPostUrl());
        if (verifyResponse.isSuccess()) {
            webResult.setResponseEnum(WebResponseEnum.SUCCESS);
            webResult.setObj(doTaskVO);
        }
        return ResponseEntityUtil.result(webResult);
    }

    private ResponseEntity<WebResultDoTaskVO> handleActivityException(ActivityException e, WebResultDoTaskVO webResult) {
        log.error("user_track【task】[task] verify error", e);
        switch (e.getCode()) {
            case SOCIAL_VERIFY_ALREADY:
                webResult.setResponseEnum(WebResponseEnum.TASK_VERIFY_ALREADY);
                break;
            case ACCESS_TOKEN_EXPIRE:
                webResult.setResponseEnum(WebResponseEnum.AUTH_CODE_INVALID);
                break;
            case TASK_PRE_CHECK_NOT_PASS:
                webResult.setResponseEnum(WebResponseEnum.TASK_PRE_CHECK_NOT_PASS);
                break;
            case ACTIVITY_HOT:
                webResult.setResponseEnum(WebResponseEnum.ACTIVITY_HOT);
                break;
            case SOCIAL_CALL_BACK_WAIT:
                webResult.setResponseEnum(WebResponseEnum.SOCIAL_CALL_BACK_WAIT);
                break;
            default:
                webResult.setResponseEnum(WebResponseEnum.TASK_VERIFY_NO_PASS);
                break;
        }
        return ResponseEntityUtil.result(webResult);
    }

    @Override
    public ResponseEntity<WebResultTaskVO> v2TasksListGet(String saasId, String channel, String position, String clientType, String appVersion) {

        QCustomer qcustomer = CustomerHolder.qcustomer();
        TaskListRequest request = new TaskListRequest();
        request.setSaasId(saasId);
        request.setChannel(channel);
        request.setPosition(position);
        request.setClientType(clientType);
        log.info("[task] list customer:{}", qcustomer == null ? "null" : "uid=" + qcustomer.getUid());
        if (qcustomer != null) {
            request.setCustomerId(qcustomer.getUid());
            request.setVipLevel(qcustomer.getVipLevel());
        }

        List<TaskListResponse> taskList = remoteTaskService.taskList(request);
        List<com.kikitrade.activity.generated.model.v2.TaskListResponse> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(taskList)) {
            list = taskList.stream()
                .map(this::convertToV2Response)
                .collect(Collectors.toList());
        }
        WebResultTaskVO taskVO = new WebResultTaskVO();
        taskVO.setSuccess(WebResponseEnum.SUCCESS.isSuccess());
        taskVO.setMsgKey(WebResponseEnum.SUCCESS.getKey());
        taskVO.setCode(WebResponseEnum.SUCCESS.getCode());
        taskVO.setObj(list);
        return ResponseEntityUtil.result(taskVO);
    }

    private com.kikitrade.activity.generated.model.v2.TaskListResponse convertToV2Response(TaskListResponse res) {
        com.kikitrade.activity.generated.model.v2.TaskListResponse r = new com.kikitrade.activity.generated.model.v2.TaskListResponse();
        r.setTaskId(res.getTaskId());
        r.setGroupId(res.getGroupId());
        r.setTitle(res.getTitle());
        r.setDesc(res.getDesc());
        r.setImage(res.getImage());
        r.setLabelName(res.getLabelName());
        r.setLabelColor(res.getLabelColor());
        r.setUrl(res.getUrl());
        r.setConnectUrl(res.getConnectUrl());
        r.setLimit(res.getLimit());
        r.setProgress(res.getProgress() == null ? 0 : res.getProgress());
        r.setStatus(res.getStatus());
        r.setCompleteTime(res.getCompleteTime());
        r.setEndTime(res.getEndTime());
        r.setDomain(res.getDomain());
        r.setBtn(res.getBtn());
        r.setCode(res.getCode());
        r.setShowProgress(res.getShowProgress());
        r.setLink(res.getLink());
        r.setReward(res.getShowReward());
        r.setPosition(res.getPosition());
        r.setCycle(res.getCycleEnum() == null ? "" : res.getCycleEnum().name());
        r.setIcon(res.getIcon());
        r.setPostTaskCode(res.getPostTaskCode());
        r.setPostTaskDesc(res.getPostTaskDesc());
        if(res.getPostTaskReward() != null){
            r.setPostTaskReward(new BigDecimal(res.getPostTaskReward()));
        }
        r.setPostTaskStatus(res.getPostTaskStatus());

        List<TaskVO> subTasks = res.getSubTasks();
        if (subTasks != null) {
            List<TaskSubVO> subVOList = subTasks.stream()
                .map(this::convertToSubVO)
                .collect(Collectors.toList());
            r.setSubTask(subVOList);
        }
        r.setRewardFrequency(res.getRewardFrequency());
        r.setTodayTaskStatus(res.getTodayTaskStatus());
        return r;
    }

    private TaskSubVO convertToSubVO(TaskVO taskVO) {
        TaskSubVO subVO = new TaskSubVO();
        subVO.setTitle(taskVO.getTitle());
        subVO.setDesc(taskVO.getDesc());
        subVO.setStatus(taskVO.getStatus());
        return subVO;
    }

    @Override
    public ResponseEntity<WebResultSeriesTaskVO> v2TasksCodeCodeGet(String code) {
        TaskCodeDetailResponse task = remoteTaskService.getTaskByCode(CustomerHolder.qcustomer().getSaasId(), code, CustomerHolder.qcustomer().getUid());
        log.info("v2TasksCodeCodeGet response:{}", task);
        TaskCodeVO taskCodeVO = new TaskCodeVO();
        BeanUtil.copyProperties(task, taskCodeVO);
        taskCodeVO.setNodes(BeanUtil.copyProperties(task.getNodes(), NodeVO::new));
        WebResultSeriesTaskVO taskVO = new WebResultSeriesTaskVO();
        taskVO.setResponseEnum(WebResponseEnum.SUCCESS);
        taskVO.setObj(taskCodeVO);
        return ResponseEntityUtil.result(taskVO);
    }

    @Override
    public ResponseEntity<WebResultTaskRewardStatVO> v2TasksRewardByCodeGet(String saasId, List<String> codes) {
        QCustomer qcustomer = CustomerHolder.qcustomer();
        BigDecimal total = new BigDecimal(0);
        for(String code : codes){
            List<com.kikitrade.activity.model.domain.Award> awards = remoteTaskService.getTaskRewardByCode(saasId, code, ActivityConstant.VipLevelEnum.valueOf(qcustomer.getVipLevel()));
            for(com.kikitrade.activity.model.domain.Award award : awards){
                if(NumberUtils.isNumber(award.getAmount())){
                    total = total.add(new BigDecimal(award.getAmount()));
                } else if (NumberUtils.isNumber(award.getShowAmount())) {
                    total = total.add(new BigDecimal(award.getShowAmount()));
                }
            }
        }

        RewardStatVO rewardStatVO = new RewardStatVO();
        rewardStatVO.setExpectReward(total);

        WebResultTaskRewardStatVO webResultTaskRewardStatVO = new WebResultTaskRewardStatVO();
        webResultTaskRewardStatVO.setResponseEnum(WebResponseEnum.SUCCESS);
        webResultTaskRewardStatVO.setObj(rewardStatVO);
        return ResponseEntityUtil.result(webResultTaskRewardStatVO);
    }

    @Override
    public ResponseEntity<WebResultLadderRewardVO> v2TasksLadderRewardGet(String saasId) {
        QCustomer qcustomer = CustomerHolder.qcustomer();
        List<com.kikitrade.activity.model.domain.Award> awards
                = remoteTaskService.listTaskRewards(saasId, LADDER_REWARD_CODE, qcustomer.getVipLevel() == null ? ActivityConstant.VipLevelEnum.NORMAL : ActivityConstant.VipLevelEnum.valueOf(qcustomer.getVipLevel()));
        if (CollectionUtils.isEmpty(awards)) {
            return ResponseEntityUtil.result(new WebResultLadderRewardVO());
        }

        WebResultLadderRewardVO webResultLadderRewardVO = new WebResultLadderRewardVO();
        webResultLadderRewardVO.setResponseEnum(WebResponseEnum.SUCCESS);

        List<LadderRewardVO> ladderRewardVOList = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            LadderRewardVO ladderRewardVO = new LadderRewardVO();
            ladderRewardVO.setLevel(Long.parseLong(String.valueOf(i)));

            Map<String, Object> param = new HashMap<>();
            param.put("_lastLadderLevel", i);
            param.put("_lastLadderSize", 30);
            param.put("_cyclePoints", 10000);

            param.put("_yesterdayRank", 1);
            Integer max = calculateExpression(param, awards.get(0).getAmount());
            param.put("_yesterdayRank", 30);
            Integer min = calculateExpression(param, awards.get(0).getAmount());
            ladderRewardVO.minScoreForRankReward(getMinScoreFor(awards.get(0).getAmount(), i));
            ladderRewardVO.setRankReward(max.toString() + "-" + min.toString());
            ladderRewardVO.setRankRewardCurrency(awards.get(0).getCurrency());

            for (DayOfWeek day : DayOfWeek.values()) {
                String dayName = day.name();
                String paramName = "_is" + dayName.charAt(0) + dayName.substring(1).toLowerCase();
                param.put(paramName, 1);
            }
            Integer weekLyReward = calculateExpression(param, awards.get(1).getAmount());
            ladderRewardVO.minScoreForWeeklyReward(getMinScoreFor(awards.get(1).getAmount(), i));
            ladderRewardVO.setWeeklyReward(String.valueOf(weekLyReward));
            ladderRewardVO.setWeeklyRewardCurrency(awards.get(1).getCurrency());

            ladderRewardVOList.add(ladderRewardVO);
            param.clear();
        }
        webResultLadderRewardVO.setObj(ladderRewardVOList);
        return ResponseEntityUtil.result(webResultLadderRewardVO);
    }

    private Integer calculateExpression(Map<String, Object> param, String expression) {
        EvaluationContext context = new StandardEvaluationContext();
        context.setVariable("map", param);
        return new SpelExpressionParser().parseExpression(expression).getValue(context, Integer.class);
    }

    public static Long getMinScoreFor(String expression, int targetLevel) {
        Pattern pattern = Pattern.compile("#map\\['_lastLadderLevel'\\] == " + targetLevel + " && #map\\['_cyclePoints'\\] >= (\\d+)");
        Matcher matcher = pattern.matcher(expression);
        if (matcher.find()) {
            return Long.parseLong(matcher.group(1));
        }
        return 0L;
    }

    @Override
    public ResponseEntity<WebResultTaskProgressVO> v2TasksProgressGet(String saasId, String taskId, String type) {
        QCustomer qcustomer = CustomerHolder.qcustomer();
        TaskProgressResponse taskProgress = remoteTaskService.getTaskProgress(saasId, taskId, qcustomer.getUid(), type);
        TaskProgressVO progressVO = new TaskProgressVO();
        progressVO.setStatus(taskProgress.getStatus());

        WebResultTaskProgressVO webResult = new WebResultTaskProgressVO();
        webResult.setResponseEnum(WebResponseEnum.SUCCESS);
        webResult.setObj(progressVO);
        return ResponseEntityUtil.result(webResult);
    }

    /**
     * GET /v2/tasks/early/pop
     *
     * @param saasId saasId (required)
     * @return 成功 (status code 200)
     */
    @Override
    public ResponseEntity<WebResultTaskPopVO> v2TasksEarlyPopGet(String saasId) {
        QCustomer qcustomer = CustomerHolder.qcustomer();
        if (qcustomer == null) {
            log.warn("Customer is null for saasId: {}", saasId);
            return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_NOT_FOUND);
        }

        try {
            TaskPopResponse taskProgress = remoteTaskService.getTaskEarlyBirdPop(saasId, qcustomer.getUid());
            TaskPopVO popVO = new TaskPopVO();
            popVO.setPop(taskProgress.getPop());
            popVO.setTaskId(taskProgress.getTaskId());

            WebResultTaskPopVO webResult = new WebResultTaskPopVO();
            webResult.setResponseEnum(WebResponseEnum.SUCCESS);
            webResult.setObj(popVO);
            return ResponseEntityUtil.result(webResult);
        } catch (Exception ex) {
            log.error("v2TasksEarlyPopGet error:{}", qcustomer.getUid(), ex);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
    }

    @Override
    public ResponseEntity<WebResultActiveTaskVO> checkIn(String saasId, String  idToken, String jwtToken) {
        QCustomer qcustomer = CustomerHolder.qcustomer();
        try{
            WebResultActiveTaskVO taskVO = new WebResultActiveTaskVO();
            if (Objects.isNull(qcustomer)) {
                taskVO.setResponseEnum(WebResponseEnum.AUTH_CODE_INVALID);
                return ResponseEntityUtil.result(taskVO);
            }

            ActivityTaskDTO activityTaskDTO = new ActivityTaskDTO();
            activityTaskDTO.setCustomerId(qcustomer.getUid());
            activityTaskDTO.setEventTime(new Date().getTime());
            activityTaskDTO.setEventCode("check_in");
            activityTaskDTO.setSaasId(saasId);
            ActivityResponse<List<com.kikitrade.activity.model.domain.Award>> task = remoteTaskService.task(activityTaskDTO);

            taskVO.setResponseEnum(WebResponseEnum.SUCCESS);
            if (Objects.nonNull(task) && Objects.nonNull(task.getObj()) && CollectionUtils.isNotEmpty(task.getObj())) {
                taskVO.setObj(BeanUtil.copyProperties(task.getObj().get(0), Award::new));
            }

            return ResponseEntityUtil.result(taskVO);
        }catch (Exception ex){
            log.error("checkIn error:{}", qcustomer.getUid(), ex);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
    }

    /**
     * GET /v2/tasks/checkin/status : check in
     * check in
     *
     * @param saasId   saasId (required)
     * @param idToken  idToken (optional)
     * @param jwtToken jwt_token (optional)
     * @return success (status code 200)
     */
    @Override
    public ResponseEntity<WebResultCheckInStatusVO> checkInStatus(String saasId, String idToken, String jwtToken) {
        QCustomer qcustomer = CustomerHolder.qcustomer();
        CheckInStatusDTO checkInStatusDTO = remoteTaskService.checkInStatus(saasId, qcustomer.getUid());

        CheckInStatusVO checkInStatusVO = new CheckInStatusVO();
        checkInStatusVO.setTodayReward(checkInStatusDTO.getTodayReward());
        checkInStatusVO.setTodayRewardType(checkInStatusDTO.getTodayRewardType());
        checkInStatusVO.setTitle(checkInStatusDTO.getTitle());
        checkInStatusVO.setDesc(checkInStatusDTO.getDesc());
        checkInStatusVO.setIcon(checkInStatusDTO.getIcon());
        checkInStatusVO.setCheckIn(checkInStatusDTO.getCheckIn());
        checkInStatusVO.setCheckInDays(checkInStatusDTO.getCheckInDays());
        checkInStatusVO.setCheckInTime(checkInStatusDTO.getCheckInTime());

        List<Award> rewardList = new ArrayList<>();
        checkInStatusDTO.getCheckInReward().forEach(
            item -> {
                Award award = new Award();
                award.setAmount(item.getAmount());
                award.setCurrency(item.getCurrency());
                award.setType(item.getType());
                award.setIndex(item.getIndex());
                rewardList.add(award);
            });
        checkInStatusVO.setCheckInReward(rewardList);

        WebResultCheckInStatusVO statusVO = new WebResultCheckInStatusVO();
        statusVO.setResponseEnum(WebResponseEnum.SUCCESS);
        statusVO.setObj(checkInStatusVO);
        return ResponseEntityUtil.result(statusVO);
    }
}
