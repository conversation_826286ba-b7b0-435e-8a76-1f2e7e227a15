package com.kikitrade.activity.delegate;

import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.RemoteGoodsService;
import com.kikitrade.activity.api.exception.ActivityException;
import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.api.model.request.GoodsOrderRequest;
import com.kikitrade.activity.api.model.response.GoodsDetailResponse;
import com.kikitrade.activity.api.model.response.GoodsResponse;
import com.kikitrade.activity.generated.api.v2.GoodsV2ApiDelegate;
import com.kikitrade.activity.generated.model.v2.GoodsDetailVO;
import com.kikitrade.activity.generated.model.v2.GoodsVO;
import com.kikitrade.activity.generated.model.v2.WebResultGoodsListVO;
import com.kikitrade.activity.generated.model.v2.WebResultGoodsVO;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.kweb.config.QuestWebProperties;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.interceptor.CustomerHolder;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.kweb.model.customer.QCustomer;
import com.kikitrade.kweb.utils.PemUtils;
import com.kikitrade.kweb.utils.ResponseEntityUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/28 10:38
 */
@Service
@Slf4j
public class GoodsV2ApiDelegateImpl implements GoodsV2ApiDelegate {

    @DubboReference
    private RemoteGoodsService remoteGoodsService;
    @Resource
    private PemUtils pemUtils;
    @Resource
    private QuestWebProperties questWebProperties;

    /**
     * GET /v2/goods/detail : 商品详情
     *
     * @param goodsId 商品id (optional)
     * @param saasId  saas_id (optional)
     * @return 成功 (status code 200)
     */
    @Override
    public ResponseEntity<WebResultGoodsVO> v2GoodsDetailGet(String goodsId, String saasId) {
        GoodsDetailResponse goods = remoteGoodsService.goods(goodsId);
        GoodsDetailVO goodsVO = new GoodsDetailVO();
        BeanUtil.copyProperties(goods, goodsVO);
        goodsVO.setImage(goods.getImageMap());
        WebResultGoodsVO result = new WebResultGoodsVO();
        result.setResponseEnum(WebResponseEnum.SUCCESS);
        result.setObj(goodsVO);
        return ResponseEntityUtil.result(result);
    }

    /**
     * GET /v2/goods/list : 商品列表
     *
     * @param offset (optional)
     * @param limit  (optional)
     * @param saasId saas_id (optional)
     * @return 成功 (status code 200)
     */
    @Override
    public ResponseEntity<WebResultGoodsListVO> v2GoodsListGet(Integer offset, Integer limit, String saasId, String exclude) {
        List<GoodsResponse> responses = remoteGoodsService.goodsList(offset, limit, saasId, exclude);
        log.info("v2GoodsListGet response:{}", responses);
        WebResultGoodsListVO webResult = new WebResultGoodsListVO();
        webResult.setResponseEnum(WebResponseEnum.SUCCESS);
        List<GoodsVO> goodses = new ArrayList<>();
        for(GoodsResponse goods : responses){
            GoodsVO goodsVO = new GoodsVO();
            BeanUtil.copyProperties(goods, goodsVO);
            goodses.add(goodsVO);
        }
        webResult.setObj(goodses);
        return ResponseEntityUtil.result(webResult);
    }

    /**
     * POST /v2/goods/redeem : 兑换商品
     *
     * @param goodsId         (required)
     * @param totalPrice      (required)
     * @param quality         (required)
     * @param timestamp       (required)
     * @param sign            (required)
     * @param saasId          saas_id (optional)
     * @return 成功 (status code 200)
     */
    @Override
    public ResponseEntity<WebResult> v2GoodsRedeemPost(String goodsId, Integer totalPrice, Integer quality, Long timestamp, String sign, String saasId) {
        try {
            QCustomer customer = CustomerHolder.qcustomer();
            if(!checkSign(goodsId, totalPrice, quality, sign)){
                return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_SECURITY_CHECK_FAIL);
            }
            Map<String, String> mugenAddress = customer.getAddress();
            if(mugenAddress == null){
                return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_LOGIN_REQUIRED);
            }
            GoodsOrderRequest orderRequest = buildOrderRequest(goodsId, totalPrice, quality, mugenAddress, timestamp, saasId);
            remoteGoodsService.redeem(orderRequest);
            return ResponseEntityUtil.result(WebResponseEnum.SUCCESS);
        } catch (ActivityException ex){
            log.error("ActivityV2ApiDelegate drew error", ex);
            if(ex.getCode().equals(ActivityExceptionType.STOCK_NOT_ENOUGH.getCode())){
                return ResponseEntityUtil.result(WebResponseEnum.STOCK_NOT_ENOUGH);
            }else if(ex.getCode().equals(ActivityExceptionType.POINT_NOT_ENOUGH.getCode())){
                return ResponseEntityUtil.result(WebResponseEnum.POINT_NOT_ENOUGH);
            }
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        } catch (Exception e) {
            log.error("v2GoodsRedeemPost:{}, {}, {}, {}, {}", goodsId, totalPrice, quality, timestamp, sign, e);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_SECURITY_CHECK_FAIL);
        }
    }

    private Boolean checkSign(String goodsId, Integer totalPrice, Integer quality, String sign){
        try{
            log.info("checkSign request,{},{},{}", goodsId, totalPrice, quality);

            RSA rsa = pemUtils.getRsa();
            if(rsa == null){
                return true;
            }
            byte[] decrypt = rsa.decrypt(sign, KeyType.PrivateKey);

            Map object = JSON.parseObject(new String(decrypt), Map.class);
            log.info("checkSign decrypt:{}", object);
            Long time = Long.parseLong(String.valueOf(object.getOrDefault("timestamp", "1704338811580")));
            long l = new Date().getTime() - time;
            //只接收60s内的请求
            if(l/1000 > 600){
                log.info("checkSign time not match,{},{}", l, time);
                return false;
            }
            if(!goodsId.equals(object.get("goodsId")) || !totalPrice.equals(object.get("totalPrice"))
                    || !quality.equals(object.get("quality"))){
                log.error("checkSign param not match,{}", object);
                return false;
            }
            return true;
        }catch (Exception ex){
            log.error("checkSign", ex);
            return false;
        }
    }

    private GoodsOrderRequest buildOrderRequest(String goodsId, Integer totalPrice, Integer quality, Map<String, String> address, Long timestamp, String saasId){
        GoodsOrderRequest orderRequest = new GoodsOrderRequest();
        orderRequest.setGoodsId(goodsId);
        orderRequest.setCustomerId(CustomerHolder.qcustomer().getUid());
        orderRequest.setQuality(quality);
        orderRequest.setTotalPrice(new BigDecimal(totalPrice));
        orderRequest.setTimestamp(timestamp);
        orderRequest.setSaasId(saasId);
        orderRequest.setAddress(address);
        return orderRequest;
    }
}
