package com.kikitrade.activity.delegate;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.RemoteQuestionService;
import com.kikitrade.activity.api.model.QuestionAnswerVO;
import com.kikitrade.activity.api.model.RespondentIdentity;
import com.kikitrade.activity.api.model.request.UserAnswerRequest;
import com.kikitrade.activity.api.model.response.QuestionListResponse;
import com.kikitrade.activity.api.model.response.QuestionSetsResponse;
import com.kikitrade.activity.api.model.response.QuestionSettleResponse;
import com.kikitrade.activity.generated.api.v2.QuestionV2ApiDelegate;
import com.kikitrade.activity.generated.model.v2.UserAnswerVO;
import com.kikitrade.activity.generated.model.v2.UserSetsVO;
import com.kikitrade.activity.generated.model.v2.WebResultQuestionVO;
import com.kikitrade.activity.generated.model.v2.WebResultSettleVO;
import com.kikitrade.activity.generated.model.v2.WebResultUserVO;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.asset.api.RemoteAssetService;
import com.kikitrade.asset.model.AssetDTO;
import com.kikitrade.asset.model.constant.AssetCategory;
import com.kikitrade.asset.model.constant.AssetType;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.interceptor.CustomerHolder;
import com.kikitrade.kweb.model.customer.QCustomer;
import com.kikitrade.kweb.utils.ResponseEntityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/28 11:49
 */
@Service
@Slf4j
public class QuestionV2ApiDelegateImpl implements QuestionV2ApiDelegate {

    @DubboReference
    private RemoteQuestionService remoteQuestionService;

    @DubboReference
    private RemoteAssetService remoteAssetService;

    @Override
    public ResponseEntity<WebResultUserVO> v2QuestionMeGet(String authorization, String saasId) {
        QCustomer customer = CustomerHolder.qcustomer();
        RespondentIdentity identity = new RespondentIdentity();
        identity.setSaasId(saasId);
        identity.setUid(customer.getUid());

        QuestionSetsResponse questionSets = remoteQuestionService.getQuestionSets(identity);
        log.info("getCustomerQuestionSets questionSets = {}", questionSets);

        WebResultUserVO detailVO = new WebResultUserVO();
        UserSetsVO userSetsVO = BeanUtil.copyProperties(
                questionSets, UserSetsVO::new);
        userSetsVO.setUserId(questionSets.getCustomerId());
        userSetsVO.setRewardRemainTime(questionSets.getRewardRemainTime());
        detailVO.setSuccess(WebResponseEnum.SUCCESS.isSuccess());
        detailVO.setMsgKey(WebResponseEnum.SUCCESS.getKey());
        detailVO.setCode(WebResponseEnum.SUCCESS.getCode());
        detailVO.setObj(userSetsVO);
        return ResponseEntityUtil.result(detailVO);
    }

    @Override
    public ResponseEntity<WebResultQuestionVO> v2QuestionAcquireGet(String authorization, String saasId) {
        log.info("[v2QuestionAcquireGet] authorization = {}, saasId = {}", authorization, saasId);
        try{
            QCustomer customer = CustomerHolder.qcustomer();
            RespondentIdentity identity = new RespondentIdentity();
            identity.setSaasId(saasId);
            identity.setAuthorization(authorization);
            identity.setCid(customer.getCid());
            identity.setUid(customer.getUid());
            identity.setVipLevel(customer.getVipLevel());

            QuestionListResponse questionListResponse = remoteQuestionService.acquireQuestions(identity);
            log.info("acquireQuestions questionListResponse = {}", questionListResponse);

            WebResultQuestionVO detailVO = new WebResultQuestionVO();
            detailVO.setSuccess(WebResponseEnum.SUCCESS.isSuccess());
            detailVO.setMsgKey(WebResponseEnum.SUCCESS.getKey());
            detailVO.setCode(WebResponseEnum.SUCCESS.getCode());
            detailVO.setObj(BeanUtil.copyProperties(questionListResponse.getQuestions(),
                    com.kikitrade.activity.generated.model.v2.QuestionListResponse::new));
            return ResponseEntityUtil.result(detailVO);
        }catch (ActivityException ex){
            log.error("v2QuestionAcquireGet", ex);
            return ResponseEntityUtil.result(WebResponseEnum.QUESTS_QUESTION_ACQUIRE_FAIL);
        }
    }

    @Override
    public ResponseEntity<WebResultSettleVO> v2QuestionSubmitPost(String authorization, String saasId, UserAnswerVO userAnswerVO) {
        WebResultSettleVO detailVO = new WebResultSettleVO();
        log.info("[v2QuestionSubmitPost] authorization = {}, saasId = {}", authorization, saasId);
        try{
            QCustomer customer = CustomerHolder.qcustomer();
            RespondentIdentity identity = new RespondentIdentity();
            identity.setSaasId(saasId);
            identity.setAuthorization(authorization);
            identity.setCid(customer.getCid());
            identity.setUid(customer.getUid());
            identity.setVipLevel(customer.getVipLevel());

            AssetDTO assetPoint = remoteAssetService.asset(identity.getUid(), AssetType.POINT, AssetCategory.NORMAL);
            AssetDTO assetExperience = remoteAssetService.asset(identity.getUid(), AssetType.EXPERIENCE, AssetCategory.NORMAL);

            UserAnswerRequest userAnswerRequest = new UserAnswerRequest();
            userAnswerRequest.setSaas_id(saasId);
            userAnswerRequest.setSpendTimeMs(userAnswerVO.getSpendTime().longValue());
            userAnswerRequest.setQuestions(BeanUtil.copyProperties(userAnswerVO.getQuestions(), QuestionAnswerVO::new));

            QuestionSettleResponse settleResponse = remoteQuestionService.submitQuestions(identity, userAnswerRequest);
            log.info("submitQuestions settleResponse = {}", settleResponse);
            if (Objects.isNull(settleResponse) || Objects.isNull(settleResponse.getQuestionSettleVO())) {
                return ResponseEntityUtil.result(detailVO);
            }

            com.kikitrade.activity.generated.model.v2.SettleResponse response = BeanUtil.copyProperties(settleResponse.getQuestionSettleVO(),
                    com.kikitrade.activity.generated.model.v2.SettleResponse::new);

            if (Objects.nonNull(settleResponse.getQuestionSettleVO()) && Objects.nonNull(settleResponse.getQuestionSettleVO().getQuestions())) {
                String questionJsonString = JSON.toJSONString(settleResponse.getQuestionSettleVO().getQuestions());
                List<com.kikitrade.activity.generated.model.v2.QuestionListResponse> questionList =
                        JSON.parseArray(questionJsonString, com.kikitrade.activity.generated.model.v2.QuestionListResponse.class);
                response.setQuestions(questionList);
            }

            response.setBeforeScore(assetPoint.getAvailable());
            response.setTotalScore(assetPoint.getAvailable().add(response.getScore()));

            response.setBeforeExp(assetExperience.getAvailable().subtract(response.getRewardExp()));
            response.setTotalExp(assetExperience.getAvailable());

            response.setSpendTime(settleResponse.getQuestionSettleVO().getSpendTime());
            detailVO.setSuccess(WebResponseEnum.SUCCESS.isSuccess());
            detailVO.setMsgKey(WebResponseEnum.SUCCESS.getKey());
            detailVO.setCode(WebResponseEnum.SUCCESS.getCode());
            detailVO.setObj(response);
            return ResponseEntityUtil.result(detailVO);
        }catch (ActivityException ex){
            log.error("v2QuestionSubmitPost", ex);
            return ResponseEntityUtil.result(ex.getCode().getCode(), ex.getCode().getKey());
        }
    }

}
