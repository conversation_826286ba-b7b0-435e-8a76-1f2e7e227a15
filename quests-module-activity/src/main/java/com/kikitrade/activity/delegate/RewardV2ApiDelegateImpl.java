package com.kikitrade.activity.delegate;

import com.kikitrade.activity.api.RemoteClaimService;
import com.kikitrade.activity.api.RemoteRewardService;
import com.kikitrade.activity.api.exception.ActivityException;
import com.kikitrade.activity.api.model.response.ClaimResponse;
import com.kikitrade.activity.generated.api.v2.RewardV2Api;
import com.kikitrade.activity.generated.api.v2.RewardV2ApiDelegate;
import com.kikitrade.activity.generated.model.v2.ClaimVO;
import com.kikitrade.activity.generated.model.v2.RewardResultVO;
import com.kikitrade.activity.generated.model.v2.WebResultClaimVO;
import com.kikitrade.activity.generated.model.v2.WebResultRewardResultVO;
import com.kikitrade.activity.model.Result;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.kweb.config.QuestWebProperties;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.interceptor.CustomerHolder;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.kweb.model.customer.CustomerLoginContext;
import com.kikitrade.kweb.model.customer.QCustomer;
import com.kikitrade.kweb.service.KwebBaseService;
import com.kikitrade.kweb.utils.ResponseEntityUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.kikitrade.kweb.constants.WebResponseEnum.CLAIM_ITEM_ALREADY;
import static com.kikitrade.kweb.constants.WebResponseEnum.SYSTEM_ERROR;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/27 17:36
 */
@Service
@Slf4j
public class RewardV2ApiDelegateImpl implements RewardV2ApiDelegate {

    @Resource
    private QuestWebProperties questWebProperties;
    @DubboReference
    private RemoteRewardService remoteRewardService;
    @Resource
    private KwebBaseService kwebBaseService;
    @DubboReference
    private RemoteClaimService remoteClaimService;

    @Override
    public ResponseEntity<WebResult> v2RewardReceivePost(String taskId, String saasId) {
        try{
            QCustomer customer = CustomerHolder.qcustomer();
            if(customer.getAddress() == null){
                CustomerLoginContext customerLoginContext = CustomerHolder.customerContext();
                QCustomer qCustomer = kwebBaseService.getByIdToken(saasId, customerLoginContext.getIdToken(), customerLoginContext.getPublicKey());
                customer.setAddress(qCustomer.getAddress());
            }
            String address = String.valueOf(customer.getAddress().get(questWebProperties.getWeb3DefaultAddressType()));
            String customerId = customer.getUid();

            Boolean success = remoteRewardService.receiveReward(customerId, address, taskId);
            if(success){
                WebResult result = new WebResult();
                result.setResponseEnum(WebResponseEnum.SUCCESS);
                return ResponseEntityUtil.result(result);
            }
            return ResponseEntityUtil.result(WebResponseEnum.TASK_VERIFY_NO_PASS);
        }catch (Exception ex){
            log.error("v2RewardReceiveGet error:{}", taskId, ex);
            return ResponseEntityUtil.result(WebResponseEnum.CLAIM_REWARD_FAIL);
        }
    }

    @Override
    public ResponseEntity<WebResultRewardResultVO> v2RewardGrantPost(List<String> address, String price, String businessId, String desc, String saasId, String sign) {
        List<RewardResultVO> list = new ArrayList<>();
        for(String add : address){
            QCustomer customer = kwebBaseService.getByAddress(saasId, add);
            if(customer == null){
                RewardResultVO vo = new RewardResultVO();
                vo.setAddress(add);
                vo.setAddress("address not found");
                list.add(vo);
                continue;
            }
            try{
                remoteRewardService.rewardPoint(customer.getUid(), price, saasId, desc, businessId);
            }catch (Exception ex){
                log.error("RewardGrantPost, error:{}", address, ex);
                RewardResultVO vo = new RewardResultVO();
                vo.setAddress(add);
                vo.setAddress(ex.getMessage());
                list.add(vo);
            }
        }
        if(list.size() == 0){
            return ResponseEntityUtil.result(WebResponseEnum.SUCCESS);
        }else{
            WebResultRewardResultVO resultVO = new WebResultRewardResultVO();
            resultVO.setResponseEnum(WebResponseEnum.CLAIM_REWARD_FAIL);
            resultVO.setObj(list);
            return ResponseEntityUtil.result(resultVO);
        }
    }

    /**
     * POST /v2/reward/allow/claim/{type} : 判断NFT是否claim过
     *
     * @param type   支持的值 nft (required)
     * @param saasId (required)
     * @return 成功 (status code 200)
     */
    @Override
    public ResponseEntity<WebResult> allowClaim(String type, String saasId) {
        QCustomer qcustomer = CustomerHolder.qcustomer();

        WebResult webResult = new WebResult();
        try{
            Boolean allowClaim = remoteClaimService.allowClaim(type, qcustomer.getUid(), qcustomer.getPublicKey(), qcustomer.getAddressObjects());
            webResult.setResponseEnum(allowClaim ? WebResponseEnum.SUCCESS : WebResponseEnum.NOT_ALLOW_CLAIM_NFT);
        }catch (ActivityException ex){
            log.error("RewardV2ApiDelegateImpl allowClaim, customer:{}, ActivityException:{}", qcustomer.getUid(), ex);
            webResult.setResponseEnum(CLAIM_ITEM_ALREADY);
        }catch (Exception ex){
            webResult.setResponseEnum(SYSTEM_ERROR);
            log.error("RewardV2ApiDelegateImpl allowClaim, customer:{}, error:{}", qcustomer.getUid(), ex);
        }
        return ResponseEntityUtil.result(webResult);
    }

    /**
     * POST /v2/reward/claim/{type} :使用nft/兑换码兑换积分
     *
     *
     * @param type 支持的值 nft/redeem (required)
     * @param saasId  (required)
     * @param code 兑换码 (optional)
     * @return 成功 (status code 200)
     * @see RewardV2Api#claim
     */
    @Override
    public ResponseEntity<WebResultClaimVO> claim(String type, String saasId, String code) {
        QCustomer qcustomer = CustomerHolder.qcustomer();
        WebResultClaimVO result = new WebResultClaimVO();
        result.setResponseEnum(WebResponseEnum.SUCCESS);

        try{
            Result<ClaimResponse> response = remoteClaimService.claimItem(type, qcustomer.getUid(), qcustomer.getPublicKey(), code, saasId, qcustomer.getAddressObjects());
            if(response == null){
                result.setResponseEnum(WebResponseEnum.CLAIM_REWARD_FAIL);
                return ResponseEntityUtil.result(result);
            }
            if(response.isSuccess()){
                if( response.getData() != null ){
                    ClaimVO claimVO = new ClaimVO();
                    claimVO.setOrderId(response.getData().getOrderId());
                    claimVO.setCurrency(response.getData().getCurrency());
                    claimVO.setReward(response.getData().getReward());
                    result.setObj(claimVO);
                }
                return ResponseEntityUtil.result(result);
            }
            if(ActivityResponseCode.CLAIM_CODE_INVALID.getCode().equals(String.valueOf(response.getCode()))){
                result.setResponseEnum(WebResponseEnum.LUCK_FORTUNE_INVALID);
                return ResponseEntityUtil.result(result);
            }
            if(ActivityResponseCode.CLAIM_REPEAT.getCode().equals(String.valueOf(response.getCode()))){
                result.setResponseEnum(WebResponseEnum.LUCK_FORTUNE_RECEIVE_REPEAT_ERROR);
                return ResponseEntityUtil.result(result);
            }
            result.setResponseEnum(WebResponseEnum.CLAIM_REWARD_FAIL);
            return ResponseEntityUtil.result(result);

        }catch (Exception ex){
            log.error("claim error:{}", qcustomer.getAddressAll() ,ex);
            result.setResponseEnum(WebResponseEnum.CLAIM_REWARD_FAIL);
            return ResponseEntityUtil.result(result);
        }
    }
}
