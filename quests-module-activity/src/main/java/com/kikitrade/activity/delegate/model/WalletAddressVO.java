package com.kikitrade.activity.delegate.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.annotation.Generated;
import javax.validation.Valid;
import java.util.Map;
import java.util.Objects;

/**
 * WalletAddressVO
 */

@Data
public class WalletAddressVO {

  @JsonProperty("web3auth")
  private Map web3auth;

  public WalletAddressVO web3auth(Map web3auth) {
    this.web3auth = web3auth;
    return this;
  }

  /**
   * Get web3auth
   * @return web3auth
  */
  @Valid 
  @Schema(name = "web3auth", required = false)
  public Map getWeb3auth() {
    return web3auth;
  }

  public void setWeb3auth(Map web3auth) {
    this.web3auth = web3auth;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WalletAddressVO walletAddressVO = (WalletAddressVO) o;
    return Objects.equals(this.web3auth, walletAddressVO.web3auth);
  }

  @Override
  public int hashCode() {
    return Objects.hash(web3auth);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WalletAddressVO {\n");
    sb.append("    web3auth: ").append(toIndentedString(web3auth)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

