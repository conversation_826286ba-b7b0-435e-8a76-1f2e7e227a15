package com.kikitrade.activity.delegate;

import com.kikitrade.activity.api.RemoteRewardService;
import com.kikitrade.activity.generated.api.s2.RewardS2ApiDelegate;
import com.kikitrade.activity.generated.model.s2.RewardResultVO;
import com.kikitrade.activity.generated.model.s2.WebResultRewardResultVO;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.interceptor.CustomerHolder;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.kweb.model.customer.CustomerLoginContext;
import com.kikitrade.kweb.model.customer.QCustomer;
import com.kikitrade.kweb.utils.ResponseEntityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/4 16:49
 */
@Service
@Slf4j
public class RewardS2ApiDelegateImpl implements RewardS2ApiDelegate {

    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;
    @DubboReference
    private RemoteRewardService remoteRewardService;

    @Override
    public ResponseEntity<WebResultRewardResultVO> s2RewardGrantPost(String customerId, String amount, String desc, String saasId, String authorization, String businessId) {

        WebResultRewardResultVO webResult = new WebResultRewardResultVO();
        CustomerBindDTO bindDTO = remoteCustomerBindService.findById(saasId, customerId);
        String uid;
        if(bindDTO == null){
            CustomerDTO customerDTO = new CustomerDTO();
            customerDTO.setSaasId(saasId);
            customerDTO.setId(customerId);
            CustomerBindDTO customerBindDTO = remoteCustomerBindService.register(customerDTO);
            uid = customerBindDTO.getUid();
        }else{
            uid = bindDTO.getUid();
        }

        try{
            remoteRewardService.rewardPoint(uid, amount, saasId, desc, businessId);
        }catch (Exception ex){
            log.error("RewardGrantPost, error:{}", customerId, ex);
            RewardResultVO vo = new RewardResultVO();
            vo.setCustomerId(customerId);
            vo.setReason(ex.getMessage());
            WebResultRewardResultVO resultVO = new WebResultRewardResultVO();
            resultVO.setResponseEnum(WebResponseEnum.CLAIM_REWARD_FAIL);
            resultVO.setObj(vo);
            return ResponseEntityUtil.result(resultVO);
        }
        webResult.setResponseEnum(WebResponseEnum.SUCCESS);
        return ResponseEntityUtil.result(webResult);
    }

    /**
     * POST /s2/reward/receive : 领取奖励
     *
     * @param customerId    (required)
     * @param taskId        (required)
     * @param saasId        (required)
     * @param authorization (required)
     * @param extendAttr    (required)
     * @return 成功 (status code 200)
     */
    @Override
    public ResponseEntity<WebResult> s2RewardReceivePost(String saasId, String authorization, String customerId, String taskId, String extendAttr) {
        try{
            log.info("s2RewardReceivePost params customerId = {}, taskId = {}, saasId = {}, authorization = {}, extendAttr = {}",
                    customerId, taskId, saasId, authorization, extendAttr);
            CustomerBindDTO bindDTO = remoteCustomerBindService.findById(saasId, customerId);
            String uid;
            if(bindDTO == null){
                CustomerDTO customerDTO = new CustomerDTO();
                customerDTO.setSaasId(saasId);
                customerDTO.setId(customerId);
                CustomerBindDTO customerBindDTO = remoteCustomerBindService.register(customerDTO);
                uid = customerBindDTO.getUid();
            }else{
                uid = bindDTO.getUid();
            }

            Boolean success = remoteRewardService.receiveReward(uid, null, taskId, extendAttr);
            if(success){
                WebResult result = new WebResult();
                result.setResponseEnum(WebResponseEnum.SUCCESS);
                return ResponseEntityUtil.result(result);
            }
            return ResponseEntityUtil.result(WebResponseEnum.CLAIM_REWARD_FAIL);
        }catch (Exception ex){
            log.error("v2RewardReceiveGet error:{}", taskId, ex);
            return ResponseEntityUtil.result(WebResponseEnum.CLAIM_REWARD_FAIL);
        }
    }
}
