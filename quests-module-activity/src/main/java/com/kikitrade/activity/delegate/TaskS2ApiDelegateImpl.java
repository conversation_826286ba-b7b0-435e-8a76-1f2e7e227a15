package com.kikitrade.activity.delegate;

import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.activity.api.RemoteTaskService;
import com.kikitrade.activity.api.model.response.TaskCompletedResult;
import com.kikitrade.activity.api.model.response.VerifyResponse;
import com.kikitrade.activity.generated.api.s2.TaskS2ApiDelegate;
import com.kikitrade.activity.generated.model.s2.TaskFulfillVO;
import com.kikitrade.activity.generated.model.s2.WebResultTaskFulfillVO;
import com.kikitrade.activity.generated.model.s2.WebResultVerifyVO;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.kevent.client.EventClient;
import com.kikitrade.kevent.common.constant.EventConstants;
import com.kikitrade.kevent.common.model.EventDTO;
import com.kikitrade.kweb.config.TaskCodeConfig;
import com.kikitrade.kweb.constants.WebConstants;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.kweb.model.customer.QCustomer;
import com.kikitrade.kweb.service.KwebBaseService;
import com.kikitrade.kweb.utils.PemUtils;
import com.kikitrade.kweb.utils.ResponseEntityUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/6/14 15:49
 */
@Service
@Slf4j
public class TaskS2ApiDelegateImpl implements TaskS2ApiDelegate {

    @Resource
    private RemoteTaskService remoteTaskService;
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;
    @Resource
    private KwebBaseService kwebBaseService;
    @Resource
    private PemUtils pemUtils;
    @Resource
    private EventClient eventClient;

    private static final String ENCRYPT_MAX_POINT_MULTIPLIER = "encryptMaxPointMultiplier";
    private static final String POINT_MULTIPLIER = "pointMultiplier";

    @Override
    public ResponseEntity<WebResultTaskFulfillVO> s2TasksFulfillStatusGet(String authorization, String saasId, String cid, String taskId) {
        WebResultTaskFulfillVO webResult = new WebResultTaskFulfillVO();
        webResult.setResponseEnum(WebResponseEnum.SUCCESS);
        TaskFulfillVO fullfillVO = new TaskFulfillVO();
        fullfillVO.setStatus(ActivityConstant.TaskStatusEnum.NOT_STARTED.name());

        String[] strings = taskId.split(",");
        for(String id : strings){
            TaskCompletedResult taskStatus = remoteTaskService.getTaskStatus(saasId, id, cid);
            log.info("s2TasksFulfillStatusGet, status:{}", JSON.toJSONString(taskStatus));
            fullfillVO.setCompleteDays(taskStatus.getCompleteDays());
            fullfillVO.setConsecutiveDays(taskStatus.getConsecutiveDays());
            if(taskStatus.isDone()){
                fullfillVO.setCompleteTime(TimeUtil.parse(taskStatus.getCompleteTime()).getTime());
                fullfillVO.setStatus(ActivityConstant.TaskStatusEnum.DONE.name());
                webResult.setObj(fullfillVO);
                return ResponseEntityUtil.result(webResult);
            }
        }
        webResult.setObj(fullfillVO);
        return ResponseEntityUtil.result(webResult);
    }

    /**
     * POST /s2/tasks/fulfill : 做任务
     *
     * @param authorization   (required)
     * @param saasId          saasId (required)
     * @param cid             用户id、address (required)
     * @param name            事件名称 (required)
     * @param contentId       当前事件对应的资源id (required)
     * @param eventTime       当前事件对应的时间戳 (required)
     * @param targetUserId    当前事件对应的资源的原资源用户id (optional)
     * @param targetContentId 当前事件对应的资源的原资源id (optional)
     * @param extendAttr      当前事件附加属性 (optional)
     * @return 成功 (status code 200)
     * or 失败 (status code 400)
     */
    @Override
    public ResponseEntity<WebResult> fulfillTask(String authorization, String saasId, String cid, String name, String contentId, Long eventTime, String targetUserId, String targetContentId, String extendAttr) {
        log.info("user_track【task】[do-task] fulfillTask, userId:{}, name:{}, contentId:{}, eventTime:{}, sign:{}, saasId:{}, targetUserId:{}, targetContentId:{}, price:{}",
            cid, name, contentId, eventTime, authorization, saasId, targetUserId, targetContentId, extendAttr);
        TaskCodeConfig taskCodeConfig = TaskCodeConfig.getValue(name);
        if (taskCodeConfig == null) {
            log.warn("[do-task] Unsupported event_code: {}", name);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
        log.info("[do-task] TaskCodeConfig for name: {}, config: {}", name, JSON.toJSONString(taskCodeConfig));

        try {
            if ("registration".equals(name) && (StringUtils.isBlank(cid) || StringUtils.isBlank(targetUserId))) {
                log.info("[do-task] user_track【task】 Incomplete registration data, userId: {}, targetUserId: {}", cid, targetUserId);
                return ResponseEntityUtil.result(WebResponseEnum.SUCCESS);
            }

            JSONObject jsonObject = createJsonObject(extendAttr, saasId);
            if ("registration".equals(name)) {
                pushEvent(jsonObject, saasId, name, cid, eventTime, contentId, targetUserId, targetContentId);
            } else {
                pushSplitEvent(jsonObject, saasId, name, cid, eventTime, contentId, taskCodeConfig, targetUserId, targetContentId);
            }

            log.info("[do-task] user_track【task】 Successfully processed doquests for userId: {}", cid);
            return ResponseEntityUtil.result(WebResponseEnum.SUCCESS);
        } catch (Exception ex) {
            log.error("[do-task] user_track【task】 Error processing doquests for userId: {}", cid, ex);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
    }

    private JSONObject createJsonObject(String attr, String saasId) {
        JSONObject jsonObject = new JSONObject();
        if (attr != null) {
            if (JSONUtil.isTypeJSON(attr)) {
                jsonObject.fluentPutAll(JSON.parseObject(attr));
                if (jsonObject.containsKey(ENCRYPT_MAX_POINT_MULTIPLIER)) {
                    RSA rsa = pemUtils.getRsa();
                    String multiplier = rsa.decryptStr(String.valueOf(jsonObject.get(ENCRYPT_MAX_POINT_MULTIPLIER)), KeyType.PrivateKey);
                    jsonObject.put(POINT_MULTIPLIER, multiplier);
                }
            } else {
                jsonObject.put("price", Double.valueOf(attr));
            }
        }
        jsonObject.put("saasId", saasId);
        return jsonObject;
    }

    @Override
    public ResponseEntity<WebResultVerifyVO> s2TasksVerifyGet(String authorization, String saasId, String cid, String scene, String ext) {
        log.info("user_track【task】s2TasksVerify start, saasId:{}, cid:{}, scene:{}, ext:{}", saasId, cid, scene, ext);

        if (StringUtils.isBlank(cid)) {
            log.error("s2TasksVerify fail, cid not exist");
            return ResponseEntityUtil.result(WebResponseEnum.ACTIVITY_INVALID_PARAMETER);
        }

        CustomerBindDTO bindDTO = remoteCustomerBindService.findById(saasId, cid);
        if (bindDTO == null) {
            log.error("s2TasksVerify fail, CustomerBindDTO not exist, cid={}", cid);
            return ResponseEntityUtil.result(WebResponseEnum.AUTH_CODE_INVALID);
        }

        try {
            VerifyResponse response = remoteTaskService.serverVerify(saasId, bindDTO.getUid(), scene, ext);
            log.info("user_track【task】s2TasksVerify end, saasId:{}, cid:{}, scene:{}, ext:{}, response:{}", saasId, cid, scene, ext, response);

            WebResponseEnum responseCode = switch (response.getCode()) {
                case NOT_SUPPORT_SCENE -> WebResponseEnum.NOT_SUPPORT_SCENE;
                case INVALID_PARAMETER -> WebResponseEnum.ACTIVITY_INVALID_PARAMETER;
                case AUTH_CODE_INVALID -> WebResponseEnum.AUTH_CODE_INVALID;
                case TASK_VERIFY_NO_PASS -> WebResponseEnum.TASK_VERIFY_NO_PASS;
                case SUCCESS -> WebResponseEnum.SUCCESS;
                default -> WebResponseEnum.SYSTEM_ERROR;
            };
            log.info("s2TasksVerify responseCode: {}", responseCode);
            return ResponseEntityUtil.result(responseCode);
        } catch (Exception e) {
            log.error("user_track【task】s2TasksVerify exception, saasId:{}, cid:{}, scene:{}, ext:{}", saasId, cid, scene, ext, e);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
    }

    public void pushEvent(JSONObject jsonObject, String saasId, String name, String userId, Long eventTime, String contentId, String targetUserId, String targetContentId) {
        EventDTO eventDTO = new EventDTO();
        jsonObject.put("targetId", targetContentId);
        if(StringUtils.isNotBlank(targetUserId)){
            QCustomer qCustomer = kwebBaseService.getByAddress(saasId, targetUserId);
            if(qCustomer != null){
                jsonObject.put("targetCustomerId", qCustomer.getUid() == null ? "-1" : qCustomer.getUid());
                jsonObject.put("targetVipLevel", qCustomer.getVipLevel());
            }
        }
        QCustomer customer = kwebBaseService.getByAddress(saasId, userId);
        if(customer != null){
            eventDTO.setCustomerId(customer.getUid());
            jsonObject.put("vipLevel", customer.getVipLevel());
        }else{
            eventDTO.setCustomerId("-1");
        }
        eventDTO.setTime(eventTime);
        eventDTO.setGlobalUid(contentId);
        eventDTO.setName(name);
        eventDTO.setBody(jsonObject);
        log.info("[do-task] user_track【task】 pushEvent:{}", eventDTO);
        MDC.put("customerId", eventDTO.getCustomerId());
        eventClient.push(eventDTO);
        MDC.remove("customerId");
    }

    public void pushSplitEvent(JSONObject jsonObject, String saasId, String name, String userId, Long eventTime, String contentId, TaskCodeConfig taskCodeConfig, String targetUserId, String targetContentId) {
        EventDTO eventDTO = new EventDTO();
        QCustomer customer = kwebBaseService.getByAddress(saasId, userId);
        if(customer != null){
            eventDTO.setCustomerId(customer.getUid());
            jsonObject.put("vipLevel", customer.getVipLevel());
        }else{
            eventDTO.setCustomerId("-1");
        }
        eventDTO.setTime(eventTime);
        eventDTO.setGlobalUid(contentId);
        eventDTO.setName(name);
        eventDTO.setBody(jsonObject);
        String splitCode = taskCodeConfig.getSplitCode();
        if (Objects.isNull(splitCode)) {
            //不设置拆分事件时，默认使用主事件
            splitCode = name;
        }
        for (String code: splitCode.split(WebConstants.COMMA)) {
            eventDTO.setName(code);
            if (code.endsWith(WebConstants.UNDERLINE)) {
                if (StringUtils.isNotBlank(targetUserId)) {
                    if (targetContentId == null) {
                        targetContentId = contentId;
                    }
                    eventDTO.setGlobalUid(targetContentId + userId);
                    jsonObject.put("sourceCustomerId", eventDTO.getCustomerId());
                    QCustomer qCustomer = kwebBaseService.getByAddress(saasId, targetUserId);
                    if (qCustomer != null) {
                        eventDTO.setCustomerId(qCustomer.getUid() == null ? "-1" : qCustomer.getUid());
                        jsonObject.put("vipLevel", qCustomer.getVipLevel());
                    } else {
                        eventDTO.setCustomerId("-1");
                    }
                }
                eventDTO.setName(code.substring(0, code.length() - 1));
            }
            log.info("[do-task] user_track【task】 pushSplitEvent:{}", JSON.toJSONString(eventDTO));
            MDC.put("customerId", eventDTO.getCustomerId());
            eventClient.push(eventDTO);
            MDC.remove("customerId");
        }
    }
}
