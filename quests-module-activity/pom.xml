<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>quests-web</artifactId>
        <groupId>com.kikitrade</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>quests-module-activity</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.property.path>..</project.property.path>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>quests-module-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kactivity-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.dubbo</groupId>
                    <artifactId>dubbo</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kactivity-model</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>quests-module-customer</artifactId>
        </dependency>
    </dependencies>
    <profiles>
        <profile>
            <id>local</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.openapitools</groupId>
                        <artifactId>openapi-generator-maven-plugin</artifactId>
                        <version>6.0.0</version>
                        <executions>
                            <execution>
                                <id>task-v2</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.basedir}/src/main/resources/api/task-v2.yaml</inputSpec>
                                    <generatorName>spring</generatorName>
                                    <apiPackage>com.kikitrade.activity.generated.api.v2</apiPackage>
                                    <modelPackage>com.kikitrade.activity.generated.model.v2</modelPackage>
                                    <generateApiTests>false</generateApiTests>
                                    <generateModelTests>false</generateModelTests>
                                    <addTestCompileSourceRoot>false</addTestCompileSourceRoot>
                                    <output>.</output>
                                    <importMappings>
                                        <importMapping>WebResult=com.kikitrade.kweb.model.common.WebResult</importMapping>
                                        <importMapping>Date=java.util.Date</importMapping>
                                        <importMapping>Map=java.util.Map</importMapping>
                                    </importMappings>
                                    <configOptions>
                                        <delegatePattern>true</delegatePattern>
                                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                        <useTags>true</useTags>
                                    </configOptions>
                                </configuration>
                            </execution>
                            <execution>
                                <id>reward-v2</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.basedir}/src/main/resources/api/reward-v2.yaml</inputSpec>
                                    <generatorName>spring</generatorName>
                                    <apiPackage>com.kikitrade.activity.generated.api.v2</apiPackage>
                                    <modelPackage>com.kikitrade.activity.generated.model.v2</modelPackage>
                                    <generateApiTests>false</generateApiTests>
                                    <generateModelTests>false</generateModelTests>
                                    <addTestCompileSourceRoot>false</addTestCompileSourceRoot>
                                    <output>.</output>
                                    <importMappings>
                                        <importMapping>WebResult=com.kikitrade.kweb.model.common.WebResult</importMapping>
                                        <importMapping>Date=java.util.Date</importMapping>
                                    </importMappings>
                                    <configOptions>
                                        <delegatePattern>true</delegatePattern>
                                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                        <useTags>true</useTags>
                                    </configOptions>
                                </configuration>
                            </execution>
                            <execution>
                                <id>reward-s2</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.basedir}/src/main/resources/api/reward-s2.yaml</inputSpec>
                                    <generatorName>spring</generatorName>
                                    <apiPackage>com.kikitrade.activity.generated.api.s2</apiPackage>
                                    <modelPackage>com.kikitrade.activity.generated.model.s2</modelPackage>
                                    <generateApiTests>false</generateApiTests>
                                    <generateModelTests>false</generateModelTests>
                                    <addTestCompileSourceRoot>false</addTestCompileSourceRoot>
                                    <output>.</output>
                                    <importMappings>
                                        <importMapping>WebResult=com.kikitrade.kweb.model.common.WebResult</importMapping>
                                        <importMapping>Date=java.util.Date</importMapping>
                                    </importMappings>
                                    <configOptions>
                                        <delegatePattern>true</delegatePattern>
                                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                        <useTags>true</useTags>
                                    </configOptions>
                                </configuration>
                            </execution>
                            <execution>
                                <id>goods-v2</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.basedir}/src/main/resources/api/goods-v2.yaml</inputSpec>
                                    <generatorName>spring</generatorName>
                                    <apiPackage>com.kikitrade.activity.generated.api.v2</apiPackage>
                                    <modelPackage>com.kikitrade.activity.generated.model.v2</modelPackage>
                                    <generateApiTests>false</generateApiTests>
                                    <generateModelTests>false</generateModelTests>
                                    <addTestCompileSourceRoot>false</addTestCompileSourceRoot>
                                    <output>.</output>
                                    <importMappings>
                                        <importMapping>WebResult=com.kikitrade.kweb.model.common.WebResult</importMapping>
                                        <importMapping>Date=java.util.Date</importMapping>
                                    </importMappings>
                                    <configOptions>
                                        <delegatePattern>true</delegatePattern>
                                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                        <useTags>true</useTags>
                                    </configOptions>
                                </configuration>
                            </execution>
                            <execution>
                                <id>lottery-v2</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.basedir}/src/main/resources/api/lottery-v2.yaml</inputSpec>
                                    <generatorName>spring</generatorName>
                                    <apiPackage>com.kikitrade.activity.generated.api.v2</apiPackage>
                                    <modelPackage>com.kikitrade.activity.generated.model.v2</modelPackage>
                                    <generateApiTests>false</generateApiTests>
                                    <generateModelTests>false</generateModelTests>
                                    <addTestCompileSourceRoot>false</addTestCompileSourceRoot>
                                    <output>.</output>
                                    <importMappings>
                                        <importMapping>WebResult=com.kikitrade.kweb.model.common.WebResult</importMapping>
                                        <importMapping>Date=java.util.Date</importMapping>
                                    </importMappings>
                                    <configOptions>
                                        <delegatePattern>true</delegatePattern>
                                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                        <useTags>true</useTags>
                                    </configOptions>
                                </configuration>
                            </execution>
                            <execution>
                                <id>task-s2</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.basedir}/src/main/resources/api/task-s2.yaml</inputSpec>
                                    <generatorName>spring</generatorName>
                                    <apiPackage>com.kikitrade.activity.generated.api.s2</apiPackage>
                                    <modelPackage>com.kikitrade.activity.generated.model.s2</modelPackage>
                                    <generateApiTests>false</generateApiTests>
                                    <generateModelTests>false</generateModelTests>
                                    <addTestCompileSourceRoot>false</addTestCompileSourceRoot>
                                    <output>.</output>
                                    <importMappings>
                                        <importMapping>WebResult=com.kikitrade.kweb.model.common.WebResult</importMapping>
                                        <importMapping>Date=java.util.Date</importMapping>
                                    </importMappings>
                                    <configOptions>
                                        <delegatePattern>true</delegatePattern>
                                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                        <useTags>true</useTags>
                                    </configOptions>
                                </configuration>
                            </execution>
                            <execution>
                                <id>question-v2</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.basedir}/src/main/resources/api/question-v2.yaml</inputSpec>
                                    <generatorName>spring</generatorName>
                                    <apiPackage>com.kikitrade.activity.generated.api.v2</apiPackage>
                                    <modelPackage>com.kikitrade.activity.generated.model.v2</modelPackage>
                                    <generateApiTests>false</generateApiTests>
                                    <generateModelTests>false</generateModelTests>
                                    <addTestCompileSourceRoot>false</addTestCompileSourceRoot>
                                    <output>.</output>
                                    <importMappings>
                                        <importMapping>WebResult=com.kikitrade.kweb.model.common.WebResult</importMapping>
                                        <importMapping>Date=java.util.Date</importMapping>
                                    </importMappings>
                                    <configOptions>
                                        <delegatePattern>true</delegatePattern>
                                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                        <useTags>true</useTags>
                                    </configOptions>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
