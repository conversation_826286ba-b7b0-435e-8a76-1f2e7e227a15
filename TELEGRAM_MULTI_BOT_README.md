# Telegram 多机器人系统使用指南

## 概述

本系统支持为不同项目配置独立的Telegram机器人，每个机器人有独立的webhook路径，可以根据不同的路径识别是哪个项目的消息。

## 系统架构

### 核心组件

1. **TelegramBotConfig** - 多机器人配置类
2. **TelegramBotManager** - 机器人管理器，负责创建和管理多个机器人实例
3. **TelegramBotFactory** - 机器人工厂，使用Spring Bean方式创建机器人实例
4. **MultiBotTelegramWebhook** - 多机器人webhook控制器，处理不同项目的webhook请求
5. **TrexTelegramBot** - Trex项目专用的机器人实现
6. **TrexTelegramMessageService** - Trex项目专用的消息处理服务（Spring Bean，支持Dubbo注入）

### 工作流程

1. 系统启动时，Spring容器创建`TrexTelegramMessageService`等Bean，并注入Dubbo服务
2. `TelegramBotManager`通过`TelegramBotFactory`根据配置创建各个项目的机器人实例
3. 工厂使用Spring管理的消息处理服务Bean，确保依赖注入正常工作
4. 每个机器人有独立的webhook路径，如 `/telegram/webhook/trex/callback`
5. 当Telegram发送webhook请求时，`MultiBotTelegramWebhook` 根据路径识别项目
6. 请求被路由到对应项目的机器人实例进行处理，消息服务中的Dubbo服务可正常调用

## 配置说明

### 配置文件格式

```properties
# 全局调试模式
telegram.debug-mode=true

# Trex项目机器人配置
telegram.bots.trex.bot-username=TrexBot
telegram.bots.trex.bot-token=YOUR_BOT_TOKEN
telegram.bots.trex.webhook-path=/telegram/webhook/trex
telegram.bots.trex.description=Trex项目专用机器人
telegram.bots.trex.enabled=true

# 其他项目配置...
telegram.bots.vibra.bot-username=VibraBot
telegram.bots.vibra.bot-token=YOUR_VIBRA_BOT_TOKEN
telegram.bots.vibra.webhook-path=/telegram/webhook/vibra
telegram.bots.vibra.description=Vibra项目专用机器人
telegram.bots.vibra.enabled=false
```

### 配置参数说明

- `telegram.debug-mode`: 全局调试模式，影响所有机器人
- `telegram.bots.{project}.bot-username`: 机器人用户名
- `telegram.bots.{project}.bot-token`: 机器人Token
- `telegram.bots.{project}.webhook-path`: Webhook路径，必须唯一
- `telegram.bots.{project}.description`: 机器人描述
- `telegram.bots.{project}.enabled`: 是否启用该机器人

## API端点

### 多机器人端点

- `POST /telegram/webhook/{project}/callback` - 接收指定项目的webhook回调
- `GET /telegram/webhook/{project}/info` - 获取指定项目的机器人信息
- `GET /telegram/webhook/status` - 获取所有机器人的状态信息
- `GET /telegram/webhook/health` - 全局健康检查

### 示例

```bash
# 查看所有机器人状态
curl http://localhost:8080/telegram/webhook/status

# 查看Trex项目机器人信息
curl http://localhost:8080/telegram/webhook/trex/info

# Telegram webhook回调 (由Telegram服务器调用)
curl -X POST http://localhost:8080/telegram/webhook/trex/callback \
  -H "Content-Type: application/json" \
  -d '{"update_id": 123, "message": {...}}'
```

## 添加新项目机器人

### 1. 配置文件添加

在配置文件中添加新项目的机器人配置：

```properties
telegram.bots.newproject.bot-username=NewProjectBot
telegram.bots.newproject.bot-token=YOUR_NEW_BOT_TOKEN
telegram.bots.newproject.webhook-path=/telegram/webhook/newproject
telegram.bots.newproject.description=新项目专用机器人
telegram.bots.newproject.enabled=true
```

### 2. 创建专用机器人类 (可选)

如果需要特殊的消息处理逻辑，可以创建专用的机器人类：

```java
public class NewProjectTelegramBot extends TelegramWebhookBot {
    // 实现项目特定的逻辑
}
```

### 3. 修改TelegramBotManager

在 `TelegramBotManager.createBotInstance()` 方法中添加新项目的case：

```java
case "newproject":
    return new NewProjectTelegramBot(projectName, botInfo, telegramBotConfig.isDebugMode());
```

## 消息处理

### 当前实现

目前只有Trex项目有完整的消息处理实现，其他项目会使用默认的TrexTelegramBot实现。

### Bean注入支持

**重要**：`TrexTelegramMessageService`现在是Spring Bean，支持Dubbo服务注入：

```java
@Service
@Slf4j
public class TrexTelegramMessageService {

    @DubboReference
    private RemoteAuthService remoteAuthService; // 可以正常注入和使用

    private BotApiMethod<?> handleStartCommand(Message message, String args) {
        // 可以正常调用Dubbo服务
        remoteAuthService.auth(authRequest);
        return null;
    }
}
```

这解决了之前使用`new`创建对象导致的依赖注入失败问题。

### 消息处理流程

1. 接收到webhook请求
2. 根据路径识别项目
3. 路由到对应的机器人实例
4. 机器人调用对应的消息处理服务
5. 处理消息并返回响应

### 日志格式

所有日志都会包含项目标识，便于区分不同项目的消息：

```
[trex] 收到 Telegram 更新，ID: 123456
[vibra] 收到用户消息 - 用户ID: 789, 用户名: testuser
```

## 向后兼容性

- 原有的 `TelegramWebhook` 类被标记为 `@Deprecated`，但仍然保留
- 旧的端点会返回HTTP 410 Gone状态，提示迁移到新端点
- 配置结构向后兼容，Trex项目的默认配置会自动创建

## 故障排查

### 常见问题

1. **机器人未初始化**
   - 检查配置文件中 `enabled=true`
   - 检查bot-token是否正确
   - 查看启动日志中的初始化信息

2. **Webhook回调失败**
   - 确认webhook路径配置正确
   - 检查Telegram Bot设置的webhook URL
   - 查看应用日志中的错误信息

3. **消息处理异常**
   - 检查对应项目的消息处理服务
   - 查看具体的异常堆栈信息
   - 确认调试模式是否开启

### 调试建议

1. 开启调试模式：`telegram.debug-mode=true`
2. 查看启动日志确认机器人初始化状态
3. 使用 `/telegram/webhook/status` 端点检查系统状态
4. 检查具体项目的信息端点：`/telegram/webhook/{project}/info`

## 安全注意事项

1. **Token安全**：不要在代码中硬编码bot token，使用配置文件或环境变量
2. **Webhook验证**：建议添加Telegram webhook签名验证
3. **访问控制**：考虑为管理端点添加访问控制
4. **日志脱敏**：生产环境建议关闭调试模式，避免敏感信息泄露
