package com.kikitrade.kweb.controller.telegram;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kikitrade.kweb.controller.telegram.config.TelegramBotConfig;
import com.kikitrade.kweb.controller.telegram.manager.TelegramBotManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.telegram.telegrambots.bots.TelegramWebhookBot;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Telegram 多机器人系统测试
 * 
 * <AUTHOR>
 * @date 2025/7/23
 */
@ExtendWith(MockitoExtension.class)
public class TelegramMultiBotTest {

    @Mock
    private TelegramBotManager botManager;

    @Mock
    private TelegramBotConfig botConfig;

    @Mock
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;
    private MultiBotTelegramWebhook controller;

    @BeforeEach
    void setUp() {
//        controller = new MultiBotTelegramWebhook(botManager, botConfig, objectMapper);
//        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
    }

    @Test
    void testGlobalHealthCheck() throws Exception {
        // 模拟返回2个活跃的机器人
        Map<String, TelegramWebhookBot> mockBots = new HashMap<>();
        mockBots.put("trex", null);
        mockBots.put("vibra", null);
        
        when(botManager.getAllBots()).thenReturn(mockBots);

        mockMvc.perform(get("/telegram/webhook/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("Multi-Bot Telegram Webhook is running. Active bots: 2"));
    }

    @Test
    void testGetAllBotsStatus() throws Exception {
        // 模拟配置
        Map<String, TelegramBotConfig.BotInfo> mockBotsConfig = new HashMap<>();
        
        TelegramBotConfig.BotInfo trexBot = new TelegramBotConfig.BotInfo();
        trexBot.setBotUsername("TrexBot");
        trexBot.setWebhookPath("/telegram/webhook/trex");
        trexBot.setDescription("Trex项目机器人");
        trexBot.setEnabled(true);
        mockBotsConfig.put("trex", trexBot);

        TelegramBotConfig.BotInfo vibraBot = new TelegramBotConfig.BotInfo();
        vibraBot.setBotUsername("VibraBot");
        vibraBot.setWebhookPath("/telegram/webhook/vibra");
        vibraBot.setDescription("Vibra项目机器人");
        vibraBot.setEnabled(false);
        mockBotsConfig.put("vibra", vibraBot);

        when(botConfig.getBots()).thenReturn(mockBotsConfig);
        when(botConfig.isDebugMode()).thenReturn(true);

        // 模拟活跃的机器人
        Map<String, TelegramWebhookBot> activeBots = new HashMap<>();
        activeBots.put("trex", null);
        when(botManager.getAllBots()).thenReturn(activeBots);

        mockMvc.perform(get("/telegram/webhook/status"))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Total Active Bots: 1")))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Project: trex")))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Status: ACTIVE")))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Project: vibra")))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Status: INACTIVE")));
    }

    @Test
    void testGetBotInfoForExistingProject() throws Exception {
        String project = "trex";
        String webhookPath = "/telegram/webhook/trex";
        
        // 模拟机器人存在
        // TelegramWebhookBot mockBot = new TrexTelegramBot(project, createMockBotInfo(), mockMessageService);
        when(botManager.getBotByPath(webhookPath)).thenReturn(null); // 简化测试，直接返回null

        // 模拟配置存在
        Map<String, TelegramBotConfig.BotInfo> mockBotsConfig = new HashMap<>();
        mockBotsConfig.put(project, createMockBotInfo());
        when(botConfig.getBots()).thenReturn(mockBotsConfig);
        when(botConfig.isDebugMode()).thenReturn(false);

        mockMvc.perform(get("/telegram/webhook/trex/info"))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Project: trex")))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Bot Username: TrexBot")))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("Webhook Path: /telegram/webhook/trex")));
    }

    @Test
    void testGetBotInfoForNonExistentProject() throws Exception {
        String project = "nonexistent";
        String webhookPath = "/telegram/webhook/nonexistent";
        
        // 模拟机器人不存在
        when(botManager.getBotByPath(webhookPath)).thenReturn(null);

        mockMvc.perform(get("/telegram/webhook/nonexistent/info"))
                .andExpect(status().isNotFound())
                .andExpect(content().string("未找到项目: nonexistent"));
    }

    @Test
    void testWebhookCallbackForNonExistentProject() throws Exception {
        String project = "nonexistent";
        String webhookPath = "/telegram/webhook/nonexistent";
        String updateJson = "{\"update_id\": 123}";
        
        // 模拟机器人不存在
        when(botManager.getBotByPath(webhookPath)).thenReturn(null);

        mockMvc.perform(post("/telegram/webhook/nonexistent/callback")
                .contentType("application/json")
                .content(updateJson))
                .andExpect(status().isNotFound())
                .andExpect(content().string("未找到对应的机器人: nonexistent"));
    }

    /**
     * 创建模拟的机器人配置信息
     */
    private TelegramBotConfig.BotInfo createMockBotInfo() {
        TelegramBotConfig.BotInfo botInfo = new TelegramBotConfig.BotInfo();
        botInfo.setBotUsername("TrexBot");
        botInfo.setBotToken("mock_token");
        botInfo.setWebhookPath("/telegram/webhook/trex");
        botInfo.setDescription("Trex项目机器人");
        botInfo.setEnabled(true);
        return botInfo;
    }
}

/**
 * 配置类测试
 */
class TelegramBotConfigTest {

    @Test
    void testDefaultConfiguration() {
        TelegramBotConfig config = new TelegramBotConfig();
        
        // 验证默认配置
        assertFalse(config.isDebugMode());
        assertNotNull(config.getBots());
        assertTrue(config.getBots().containsKey("trex"));
        
        TelegramBotConfig.BotInfo trexBot = config.getBots().get("trex");
        assertNotNull(trexBot);
        assertEquals("lpannBot", trexBot.getBotUsername());
        assertEquals("/telegram/webhook/trex", trexBot.getWebhookPath());
        assertEquals("Trex项目机器人", trexBot.getDescription());
        assertTrue(trexBot.isEnabled());
    }

    @Test
    void testBotInfoSettersAndGetters() {
        TelegramBotConfig.BotInfo botInfo = new TelegramBotConfig.BotInfo();
        
        botInfo.setBotUsername("TestBot");
        botInfo.setBotToken("test_token");
        botInfo.setWebhookPath("/test-webhook");
        botInfo.setDescription("测试机器人");
        botInfo.setEnabled(true);
        
        assertEquals("TestBot", botInfo.getBotUsername());
        assertEquals("test_token", botInfo.getBotToken());
        assertEquals("/test-webhook", botInfo.getWebhookPath());
        assertEquals("测试机器人", botInfo.getDescription());
        assertTrue(botInfo.isEnabled());
    }
}
