openapi: 3.0.3
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
servers:
  - description: kweb server url
    url: http://api.dev.dipbit.xyz/s2
tags:
  - name: CustomerS2
    description: 用户相关接口描述
paths:
  /customer/event:
    post:
      summary: 第三方用户注册或绑定邀请关系
      description: 第三方用户注册或绑定邀请关系
      operationId: customerEvent
      tags:
        - CustomerS2
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          required: true
        - schema:
            type: string
          in: query
          name: name
          required: true
        - schema:
            type: string
          in: query
          name: customerId
          required: true
        - schema:
            type: string
          in: query
          name: body
          required: false
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResult'
  /customer/twitter/{cid}:
    get:
      summary: get twitter info of user
      description: 获取用户的 twitter 信息
      operationId: twitterInfo
      tags:
        - CustomerS2
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          required: true
        - schema:
            type: string
          in: path
          name: cid
          description: 业务方用户 id
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultTwitterVO'
  /customer/twitters/{cid}:
    get:
      summary: get twitter infos of user by all saasIds
      description: 获取用户的 twitter 信息，包含所有 saasId 平台
      operationId: twitterInfos
      tags:
        - CustomerS2
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          required: true
        - schema:
            type: string
          in: path
          name: cid
          description: 业务方用户 id
          required: true
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultTwittersVO'
  /customer/{cid}:
    get:
      summary: get quests customer info
      description: 获取 quests 平台用户信息
      operationId: customerInfo
      tags:
        - CustomerS2
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          required: true
        - schema:
            type: string
          in: path
          name: cid
          description: 业务方用户 id
          required: true
        - schema:
            type: string
          in: query
          name: businessType
          required: false
          description: 默认返回 twitterName, 支持 asset 等
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultCurrentUserVO'
  /customer/oauth/social/token:
    post:
      summary: get twitter/discord token
      description: 三方认证登录
      operationId: authToken
      tags:
        - CustomerS2
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          required: true
        - schema:
            type: string
          in: query
          name: cid
          description: 用户id
          required: true
        - schema:
            type: string
          in: query
          name: platform
          description: twitter、discord
          required: true
        - schema:
            type: string
          in: query
          name: code
          description: code(一次性)
          required: true
        - schema:
            type: string
          in: query
          name: redirectUri
          description: redirectUri
          required: false
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultSocialTokenVO'
components:
  schemas:
    WebResult:
      type: object
      discriminator:
        propertyName: code
    Map:
      type: object
    WebResultSocialTokenVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/SocialTokenVO'
    SocialTokenVO:
      type: object
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
    WebResultTwitterVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/TwitterVO'
    WebResultTwittersVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              type: array
              items:
                $ref: '#/components/schemas/TwitterVO'
    TwitterVO:
      type: object
      properties:
        id:
          type: string
        handleName:
          type: string
        created:
          type: integer
          format: int64
          description: 单位是秒
    WebResultCurrentUserVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/CurrentUserVO'
    CurrentUserVO:
      type: object
      properties:
        uid:
          type: string
        saasId:
          type: string
        twitterName:
          type: string
        discordName:
          type: string
        invitePoint:
          type: number
        dividePoint:
          type: number
        available:
          type: number
          description: 用户加成后的积分
        pfp:
          type: string
          example: 100%
          description: pfp 加成
        vip:
          type: string
          example: 100%
          description: vip 加成
        isVip:
          type: boolean
          description: 是否为 vip 用户
        rankValue:
          type: integer
        rankPercent:
          type: string
        expValue:
          type: number
