openapi: 3.0.3
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
servers:
  - description: kweb server url
    url: https://api.quests.dev.dipbit.xyz/v2
tags:
  - name: CustomerV2
    description: 用户相关接口描述
paths:
  /customer/login/oauth:
    post:
      summary: 三方认证登录
      description: 三方认证登录
      operationId: oauthLogin
      tags:
        - CustomerV2
      parameters:
        - schema:
            type: string
          in: header
          name: saas_id
          required: false
        - schema:
            type: string
          in: query
          name: idToken
          description: 三方 token
          required: true
        - schema:
            type: string
          in: query
          name: publicKey
          description: 三方 publicKey
          required: true
        - schema:
            type: string
          in: query
          name: walletAddress
          description: 钱包账户地址
          required: true
        - schema:
            type: string
          in: query
          name: walletType
          description: 钱包类型 (evm、sui), 默认值为 evm
          required: false
        - schema:
            type: string
          in: query
          name: thirdAuthSource
          description: 三方认证来源 (EXTERNAL_SOCIAL、EXTERNAL_WALLET)
          required: true
        - schema:
            default: false
            type: boolean
          in: query
          name: autoRegister
          required: false
          description: 是否自动注册 (即账户未注册的情况下，注册后登录)，默认 false
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultCustomerBindVO'
  /customer/oauth/social/token:
    post:
      summary: get twitter/discord token
      description: 三方认证登录
      operationId: authToken
      tags:
        - CustomerV2
      parameters:
        - schema:
            type: string
          in: header
          name: idToken
          required: false
        - schema:
            type: string
          in: header
          name: saas_id
          required: false
        - schema:
            type: string
          in: query
          name: app
          description: twitter、discord、google、facebook
          required: true
        - schema:
            type: string
          in: query
          name: platform
          description: twitter、discord
          required: true
        - schema:
            type: string
          in: query
          name: code
          description: code(一次性)
          required: true
        - schema:
            type: string
          in: query
          name: redirectUri
          description: redirectUri
          required: false
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultSocialTokenVO'
  /customer/me:
    get:
      summary: get current time
      description: 三方认证登录
      operationId: me
      tags:
        - CustomerV2
      parameters:
        - schema:
            type: string
          in: header
          name: idToken
          required: false
        - name: JWT_TOKEN
          schema:
            type: string
          in: header
          required: false
        - schema:
            type: string
          in: header
          name: saas_id
          required: false
        - schema:
            type: string
          in: query
          name: businessType
          required: false
          description: 默认返回 twitterName, 还支持 invite、asset、rank、exp、ticket、all
        - schema:
            type: string
          in: query
          name: handleName
          required: false
          description: osp handleName
        - schema:
            type: string
          in: query
          name: appId
          required: false
          description: osp appId
        - schema:
            type: string
          in: query
          name: chainId
          required: false
          description: osp chainId
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultCurrentUserVO'
  /customer/refreshVerified:
    post:
      summary: osp verify
      description: 刷新认证
      operationId: refreshVerified
      tags:
        - CustomerV2
      parameters:
        - schema:
            type: string
          in: header
          name: idToken
          required: false
        - name: JWT_TOKEN
          schema:
            type: string
          in: header
          required: false
        - schema:
            type: string
          in: header
          name: saas_id
          required: false
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultVerifyVO'
  /customer/sendVerifyCode:
    post:
      summary: send verify code
      description: 发送验证码
      operationId: sendVerifyCode
      tags:
        - CustomerV2
      parameters:
        - name: JWT_TOKEN
          schema:
            type: string
          in: header
          required: false
        - name: saas_id
          schema:
            type: string
          in: header
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendVerifyCodeRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResult'
components:
  schemas:
    WebResult:
      type: object
      discriminator:
        propertyName: code
    Map:
      type: object
    SendVerifyCodeRequest:
      type: object
      properties:
        channel:
          type: string
          description: 验证码类型，目前只支持 email
        receiver:
          type: string
          description: 接收者
        customerName:
          type: string
          description: 用户名
      required:
        - channel
        - receiver
    WebResultSocialTokenVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/SocialTokenVO'
    SocialTokenVO:
      type: object
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
    WebResultCurrentUserVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/CurrentUserVO'

    WebResultCustomerBindVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/CustomerBindVO'
    WebResultVerifyVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/VerifyVO'
    CustomerBindVO:
      type: object
      properties:
        cid:
          type: string
        uid:
          type: string
        saasId:
          type: string
        avatar:
          type: string
          description: 头像
        jwt:
          type: string
        mugenJwt:
          type: string
        available:
          type: number
        twitterName:
          type: string
    WebResultCustomerVO:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/CustomerVO'
    CustomerVO:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
          description: 邮箱
        userName:
          type: string
          description: 用户名
        status:
          type: integer
          description: 用户状态
        idCertifiedStatus:
          type: integer
          description: kyc 身份验证状态，目前废弃，使用 kycAuthLevel 字段
        isEmailCertified:
          type: boolean
          description: 是否认证邮箱
        isBindEmail:
          type: boolean
          description: 是否绑定了邮箱
        isPhoneCertified:
          type: boolean
          description: 是否认证手机
        isBindPhone:
          type: boolean
          description: 是否绑定了手机号码
        isGoogleCertified:
          type: boolean
          description: 是否认证 Google
        isBindGoogle:
          type: boolean
          description: 是否绑定 Google
        isBindWeb3:
          type: boolean
          description: 是否绑定 web3
        locale:
          type: string
        phone:
          type: string
          description: 手机号
        registerType:
          type: integer
          description: 注册类型
        isMigration:
          type: integer
        noPassword:
          type: boolean
          description: 是否存在密码
        isExistAssetPassword:
          type: boolean
          description: 是否存在资金密码
        region:
          type: string
          description: 国家地区
        kycLevel:
          type: string
          description: kyc 等级
        vipLevel:
          type: string
          description: vip 等级
        vipEndTime:
          type: integer
          format: int64
          description: vip 到期时间
          example: 1663836874000
        merchantId:
          type: string
        merchantName:
          type: string
        whatsAppId:
          type: string
        telegramId:
          type: string
        lineId:
          type: string
        middleName:
          type: string
        avatar:
          type: string
          description: 头像
        gender:
          type: integer
          description: 性别
        nickName:
          type: string
          description: 昵称
        uniqueName:
          type: string
        jwtKey:
          type: string
        jwtKeyExpireIn:
          type: integer
          format: int64
          description: jwtKey 过期时间
          example: 1663836874000
        wsKey:
          type: string
        refreshToken:
          type: string
        refreshTokenExpireIn:
          type: integer
          format: int64
          description: refreshToken 过期时间
          example: 1663836874000
        publicKey:
          type: string
          description: 三方 public_key(web3auth)
        walletAddress:
          type: string
          description: 钱包账户地址
        address:
          $ref: '#/components/schemas/WalletAddressVO'
    WalletAddressVO:
      type: object
      properties:
        web3auth:
          $ref: '#/components/schemas/Map'
    CurrentUserVO:
      type: object
      properties:
        uid:
          type: string
        saasId:
          type: string
        twitterName:
          type: string
        discordName:
          type: string
        invitePoint:
          type: number
        dividePoint:
          type: number
        available:
          type: number
          description: 用户加成后的积分
        pfp:
          type: string
          example: 100%
          description: pfp 加成
        vip:
          type: string
          example: 100%
          description: vip 加成
        isVip:
          type: boolean
          description: 是否为 vip 用户
        rankValue:
          type: integer
        rankPercent:
          type: string
        expValue:
          type: number
        ticketAvailable:
          type: number
          description: 用户加成后的积分
        ticketUsed:
          type: number
          description: 用户已使用的积分
        voucherAvailable:
          type: number
          description: 用户可用的抽奖券
        extraBoostValue:
          type: number
          description: 用户持有的有效额外加成
        boostValue:
          type: number
          description: 用户持有的基础加成
        ojoValue:
          type: number
          description: 用户持有的OJO资产
        credentialsAvailable:
          type: number
          default: 0
          description: 用户信用分总分
        OSPVerified:
          type: boolean
          default: false
          description: OSP认证标识
        XVerified:
          type: boolean
          default: false
          description: X加V认证标识
        XVerifiedType:
          type: string
          description: X加V认证类型
        ospAvatar:
          type: string
          description: OSP头像
        experienceVoucherAvailable:
          type: number
          description: 用户抽奖体验券
        seasonPointAvailable:
          type: number
          description: 用户本赛季的积分
    VerifyVO:
      type: object
      properties:
        OSPVerified:
          type: boolean
          default: false
        XVerified:
          type: boolean
          default: false

