package com.kikitrade.kweb.controller.v2.customer.request;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/11/24 14:39
 */
@Data
@Builder
public class OauthLoginOutRequest implements Serializable {

    private String idToken;
    private String publicKey;
    private String walletAddress;
    private String thirdAuthSource;
    private String saasId;
    private String walletType;
    private Boolean autoRegister;
}
