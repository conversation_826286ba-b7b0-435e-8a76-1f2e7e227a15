package com.kikitrade.kweb.controller.v2.customer.delegate.impl;

import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.RemoteAuthService;
import com.kikitrade.activity.api.model.AuthRequest;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.asset.api.RemoteAssetService;
import com.kikitrade.asset.model.AssetDTO;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.asset.model.constant.AssetCategory;
import com.kikitrade.asset.model.constant.AssetType;
import com.kikitrade.asset.model.request.AssetLedgersSumRequest;
import com.kikitrade.customer.generated.api.s2.CustomerS2ApiDelegate;
import com.kikitrade.customer.generated.model.s2.*;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.kweb.config.QuestWebProperties;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.interceptor.CustomerHolder;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.kweb.model.customer.QCustomer;
import com.kikitrade.kweb.service.KwebBaseService;
import com.kikitrade.kweb.utils.ResponseEntityUtil;
import com.kikitrade.member.model.TopRankDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ForkJoinPool;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/23 15:43
 */
@Service
@Slf4j
public class CustomerS2ApiDelegateImpl implements CustomerS2ApiDelegate {

    @DubboReference
    private RemoteAssetService remoteAssetService;
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;
    @Resource
    private OnsProducer onsProducer;
    @Resource
    private QuestWebProperties questWebProperties;
    @DubboReference
    private RemoteAuthService remoteAuthService;
    @Resource
    private KwebBaseService kwebBaseService;

    private ForkJoinPool joinPool = new ForkJoinPool(8);

    @Override
    public ResponseEntity<WebResultTwitterVO> twitterInfo(String authorization, String saasId, String cid) {
        try{
            CustomerBindDTO bindDTO = remoteCustomerBindService.findById(saasId, cid);
            if(bindDTO == null){
                return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_NOT_FOUND);
            }
            if(bindDTO.getTwitterCreated() == null){
                return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_NOT_AUTH);
            }
            TwitterVO twitterVO = new TwitterVO();
            twitterVO.setId(bindDTO.getTwitterId());
            twitterVO.setHandleName(bindDTO.getTwitterName());
            twitterVO.setCreated(bindDTO.getTwitterCreated());

            WebResultTwitterVO result = new WebResultTwitterVO();
            result.setResponseEnum(WebResponseEnum.SUCCESS);
            result.setObj(twitterVO);
            return ResponseEntityUtil.result(result);
        }catch (Exception ex){
            log.error("twitterInfo error:{}", cid, ex);
            return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_NOT_FOUND);
        }
    }

    @Override
    public ResponseEntity<WebResult> customerEvent(String authorization, String saasId, String name, String customerId, String body) {
        CustomerDTO customerDTO = new CustomerDTO();
        customerDTO.setSaasId(saasId);
        customerDTO.setId(customerId);

        switch (name){
            case "registration" -> {
                remoteCustomerBindService.register(customerDTO);
                WebResult webResult = new WebResult();
                webResult.setResponseEnum(WebResponseEnum.SUCCESS);
                return ResponseEntityUtil.result(webResult);
            }
            case "invite_relation" -> {
                Map map = JSON.parseObject(body, Map.class);
                remoteCustomerBindService.invite(customerDTO, Long.parseLong(String.valueOf(map.get("inviteCount"))));
                WebResult webResult = new WebResult();
                webResult.setResponseEnum(WebResponseEnum.SUCCESS);
                return ResponseEntityUtil.result(webResult);
            }
            default -> {
                return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_PARAMETER_INVALID);
            }
        }
    }

    @Override
    public ResponseEntity<WebResultTwittersVO> twitterInfos(String authorization, String saasId, String cid) {
        try{
            List<CustomerBindDTO> bindDTOs = remoteCustomerBindService.findAllByCid(cid);
            if(bindDTOs == null || bindDTOs.isEmpty()){
                return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_NOT_FOUND);
            }
            List<TwitterVO> list = new ArrayList<>();
            for (CustomerBindDTO bindDTO : bindDTOs) {
                if(bindDTO.getTwitterCreated() == null){
                    continue;
                }
                TwitterVO twitterVO = new TwitterVO();
                twitterVO.setId(bindDTO.getTwitterId());
                twitterVO.setHandleName(bindDTO.getTwitterName());
                twitterVO.setCreated(bindDTO.getTwitterCreated());
                list.add(twitterVO);
            }

            WebResultTwittersVO result = new WebResultTwittersVO();
            result.setResponseEnum(WebResponseEnum.SUCCESS);
            result.setObj(list);
            return ResponseEntityUtil.result(result);
        }catch (Exception ex){
            log.error("twittersInfo error:{}", cid, ex);
            return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_NOT_FOUND);
        }
    }

    @Override
    public ResponseEntity<WebResultCurrentUserVO> customerInfo(String authorization, String saasId, String cid, String businessType) {
        try{
            CustomerBindDTO qcustomer = remoteCustomerBindService.findById(saasId, cid);
            if(qcustomer == null){
                return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_NOT_FOUND);
            }
            CurrentUserVO bindVO = new CurrentUserVO();
            bindVO.setSaasId(qcustomer.getSaasId());
            bindVO.setUid(qcustomer.getUid());
            bindVO.setTwitterName(qcustomer.getTwitterName());
            if(StringUtils.isNotBlank(businessType)){
                joinPool.submit(() -> Arrays.asList(businessType.split(",")).parallelStream().forEach(type -> {
                    switch (type){
                        case "asset":
                            AssetDTO asset = remoteAssetService.asset(qcustomer.getSaasId(), qcustomer.getUid(), AssetType.POINT, AssetCategory.NORMAL);
                            TCustomerDTO tCustomerDTO = remoteCustomerBindService.findTCustomerByUid(qcustomer.getSaasId(), qcustomer.getUid());
                            if(tCustomerDTO != null){
                                bindVO.setAvailable(asset.getAvailable()
                                        .add(vipAdd(asset.getAvailable(), tCustomerDTO))
                                        .add(pfpAdd(asset.getAvailable(), tCustomerDTO)));
                                if(tCustomerDTO.getPfpCount() != null && tCustomerDTO.getPfpCount() > 0){
                                    bindVO.setPfp(String.format("%s%s",tCustomerDTO.getPfpCount() * 100, "%"));
                                }
                                bindVO.setIsVip(false);
                                if(BooleanUtils.isTrue(tCustomerDTO.getVip())){
                                    bindVO.setVip(String.format("%s%s",tCustomerDTO.getVipCount() * 20, "%"));
                                    bindVO.setIsVip(tCustomerDTO.getVip());
                                }
                            }else{
                                bindVO.setAvailable(asset.getAvailable());
                            }
                            break;
                        default:
                            break;
                    }
                })).join();
            }
            WebResultCurrentUserVO result = new WebResultCurrentUserVO();
            result.setResponseEnum(WebResponseEnum.SUCCESS);
            result.setObj(bindVO);
            return ResponseEntityUtil.result(result);
        }catch (Exception ex){
            log.error("customerInfo error:{}", cid, ex);
            return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_NOT_FOUND);
        }
    }

    @Override
    public ResponseEntity<WebResultSocialTokenVO> authToken(String authorization, String saasId, String cid, String platform, String code, String redirectUri) {
        try{
            log.info("[auth] saasId:{},cid:{},platform:{},code:{},redirectUri:{}", saasId, cid, platform, code, redirectUri);
            CustomerBindDTO customerTO = kwebBaseService.getCustomerById(saasId, cid);
            AuthRequest authRequest = new AuthRequest();
            authRequest.setSaasId(saasId);
            authRequest.setPlatform(platform);
            authRequest.setCode(code);
            authRequest.setRedirectUri(redirectUri);
            authRequest.setCustomerId(customerTO.getUid());
            Token token = remoteAuthService.auth(authRequest);
            if(token == null){
                log.info("[auth] error:{},{}", platform ,customerTO);
                return ResponseEntityUtil.result(WebResponseEnum.AUTH_CODE_INVALID);
            }
            SocialTokenVO tokenVO = new SocialTokenVO();
            tokenVO.setAccessToken(token.getRefreshToken());
            tokenVO.setRefreshToken(token.getRefreshToken());

            WebResultSocialTokenVO webResultSocialTokenVO = new WebResultSocialTokenVO();
            webResultSocialTokenVO.setResponseEnum(WebResponseEnum.SUCCESS);
            webResultSocialTokenVO.setObj(tokenVO);
            return ResponseEntityUtil.result(webResultSocialTokenVO);
        }catch (ActivityException ex){
            log.error("authToken error:{}", code, ex);
            if (ex.getCode() == ActivityResponseCode.AUTH_REPEAT) {
                return ResponseEntityUtil.result(WebResponseEnum.AUTH_REPEAT);
            } else if (ex.getCode() == ActivityResponseCode.AUTH_NO_SAME_LOGIN || ex.getCode() == ActivityResponseCode.AUTH_NO_SAME_LAST) {
                return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_AUTH_NOT_SAME_WITH_LOGIN);
            } else if (ex.getCode() == ActivityResponseCode.CHECK_CRATED_DAYS_FAIL) {
                return ResponseEntityUtil.result(WebResponseEnum.CHECK_CRATED_DAYS_FAIL);
            }
            return ResponseEntityUtil.result(WebResponseEnum.AUTH_CODE_INVALID);
        }catch (Exception e) {
            log.error("authToken error:{}", code, e);
            return ResponseEntityUtil.result(WebResponseEnum.AUTH_CODE_INVALID);
        }
    }

    private BigDecimal vipAdd(BigDecimal originalAvailable, TCustomerDTO customer){
        if(customer != null && BooleanUtils.isTrue(customer.getVip())){
            return originalAvailable.multiply(new BigDecimal(0.2).multiply(new BigDecimal(customer.getVipCount())));
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal pfpAdd(BigDecimal originalAvailable, TCustomerDTO customer) {
        if (customer.getPfpCount() == null) {
            return BigDecimal.ZERO;
        }
        return originalAvailable.multiply(new BigDecimal(customer.getPfpCount()));
    }
}
