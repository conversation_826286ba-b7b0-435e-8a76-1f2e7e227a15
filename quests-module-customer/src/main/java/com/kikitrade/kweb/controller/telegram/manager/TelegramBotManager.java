package com.kikitrade.kweb.controller.telegram.manager;

import com.kikitrade.kweb.controller.telegram.config.TelegramBotConfig;
import com.kikitrade.kweb.controller.telegram.factory.TelegramBotFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.bots.TelegramWebhookBot;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * Telegram Bot 管理器
 * 负责管理多个机器人实例，根据项目名称创建和获取对应的机器人
 * 
 * <AUTHOR>
 * @date 2025/7/23
 */
@Component
@Slf4j
public class TelegramBotManager {

    @Autowired
    private TelegramBotConfig telegramBotConfig;

    @Autowired
    private TelegramBotFactory telegramBotFactory;

    private final Map<String, TelegramWebhookBot> botInstances = new HashMap<>();
    private final Map<String, String> pathToProjectMap = new HashMap<>();
    
    @PostConstruct
    public void initializeBots() {
        log.info("开始初始化 Telegram 机器人...");
        
        for (Map.Entry<String, TelegramBotConfig.BotInfo> entry : telegramBotConfig.getBots().entrySet()) {
            String projectName = entry.getKey();
            TelegramBotConfig.BotInfo botInfo = entry.getValue();
            
            if (!botInfo.isEnabled()) {
                log.info("跳过未启用的机器人: {}", projectName);
                continue;
            }
            
            try {
                TelegramWebhookBot bot = createBotInstance(projectName, botInfo);
                if (bot != null) {
                    botInstances.put(projectName, bot);
                    pathToProjectMap.put(botInfo.getWebhookPath(), projectName);
                    log.info("成功初始化机器人 - 项目: {}, 用户名: {}, Webhook路径: {}", 
                            projectName, botInfo.getBotUsername(), botInfo.getWebhookPath());
                }
            } catch (Exception e) {
                log.error("初始化机器人失败 - 项目: {}, 错误: {}", projectName, e.getMessage(), e);
            }
        }
        
        log.info("机器人初始化完成，共初始化 {} 个机器人", botInstances.size());
    }
    
    /**
     * 根据项目名称创建机器人实例
     */
    private TelegramWebhookBot createBotInstance(String projectName, TelegramBotConfig.BotInfo botInfo) {
        return telegramBotFactory.createBot(projectName, botInfo, telegramBotConfig.isDebugMode());
    }
    
    /**
     * 根据项目名称获取机器人实例
     */
    public TelegramWebhookBot getBotByProject(String projectName) {
        return botInstances.get(projectName);
    }
    
    /**
     * 根据webhook路径获取对应的项目名称
     */
    public String getProjectByPath(String webhookPath) {
        return pathToProjectMap.get(webhookPath);
    }
    
    /**
     * 根据webhook路径获取机器人实例
     */
    public TelegramWebhookBot getBotByPath(String webhookPath) {
        String projectName = getProjectByPath(webhookPath);
        return projectName != null ? getBotByProject(projectName) : null;
    }
    
    /**
     * 获取所有已初始化的机器人
     */
    public Map<String, TelegramWebhookBot> getAllBots() {
        return new HashMap<>(botInstances);
    }
    
    /**
     * 获取路径到项目的映射
     */
    public Map<String, String> getPathToProjectMap() {
        return new HashMap<>(pathToProjectMap);
    }
}
