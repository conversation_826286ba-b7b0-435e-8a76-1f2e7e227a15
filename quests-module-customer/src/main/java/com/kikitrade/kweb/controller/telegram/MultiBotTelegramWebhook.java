package com.kikitrade.kweb.controller.telegram;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kikitrade.kweb.controller.telegram.config.TelegramBotConfig;
import com.kikitrade.kweb.controller.telegram.manager.TelegramBotManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.telegram.telegrambots.bots.TelegramWebhookBot;
import org.telegram.telegrambots.meta.api.methods.BotApiMethod;
import org.telegram.telegrambots.meta.api.objects.Update;

import java.util.Map;

/**
 * 多机器人Telegram Webhook 控制器
 * 负责接收和处理来自多个Telegram机器人的Webhook请求
 * 根据不同的webhook路径识别不同的项目并路由到对应的机器人
 *
 * <AUTHOR>
 * @date 2025/7/23
 */
@RestController
@Slf4j
public class MultiBotTelegramWebhook {

    @Autowired
    private TelegramBotManager botManager;

    @Autowired
    private TelegramBotConfig botConfig;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 全局健康检查端点
     */
    @GetMapping("/telegram/webhook/health")
    public ResponseEntity<String> globalHealth() {
        Map<String, TelegramWebhookBot> allBots = botManager.getAllBots();
        return ResponseEntity.ok(String.format("Multi-Bot Telegram Webhook is running. Active bots: %d", allBots.size()));
    }

    /**
     * 动态处理所有机器人的Webhook回调
     * 路径格式: /telegram/webhook/{project}/callback
     *
     * @param project 项目名称
     * @param update Telegram 发送的对象
     * @return HTTP 响应
     */
    @PostMapping("/telegram/webhook/{project}/callback")
    public ResponseEntity<?> onReceiveUpdate(@PathVariable String project, @RequestBody Update update) {
        try {
            String webhookPath = "/telegram/webhook/" + project;

            if (botConfig.isDebugMode()) {
                log.info("[{}] 收到 Telegram webhook 请求: {}", project, JSON.toJSONString(update));
            } else {
                log.info("[{}] 收到 Telegram webhook 请求，长度: {} 字符", project, JSON.toJSONString(update).length());
            }

            // 根据路径获取对应的机器人
            TelegramWebhookBot bot = botManager.getBotByPath(webhookPath);
            if (bot == null) {
                log.warn("未找到项目 {} 对应的机器人，webhook路径: {}", project, webhookPath);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body("未找到对应的机器人: " + project);
            }

            // 处理更新
            BotApiMethod<?> responseMethod = bot.onWebhookUpdateReceived(update);

            // 记录处理结果
            if (responseMethod != null) {
                log.debug("[{}] 生成响应方法: {}", project, responseMethod.getClass().getSimpleName());
            } else {
                log.debug("[{}] 无需响应", project);
            }

            return ResponseEntity.ok().build();

        } catch (Exception e) {
            log.error("[{}] 处理 Telegram webhook 请求失败: {}", project, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("处理请求失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定项目的Bot信息
     */
    @GetMapping("/telegram/webhook/{project}/info")
    public ResponseEntity<?> getBotInfo(@PathVariable String project) {
        try {
            String webhookPath = "/telegram/webhook/" + project;
            TelegramWebhookBot bot = botManager.getBotByPath(webhookPath);
            
            if (bot == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body("未找到项目: " + project);
            }

            TelegramBotConfig.BotInfo botInfo = botConfig.getBots().get(project);
            if (botInfo == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body("未找到项目配置: " + project);
            }

            return ResponseEntity.ok().body(
                "Project: " + project + "\n" +
                "Bot Username: " + botInfo.getBotUsername() + "\n" +
                "Webhook Path: " + botInfo.getWebhookPath() + "\n" +
                "Description: " + botInfo.getDescription() + "\n" +
                "Enabled: " + botInfo.isEnabled() + "\n" +
                "Debug Mode: " + botConfig.isDebugMode()
            );
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("获取 Bot 信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有机器人的状态信息
     */
    @GetMapping("/telegram/webhook/status")
    public ResponseEntity<?> getAllBotsStatus() {
        try {
            Map<String, TelegramWebhookBot> allBots = botManager.getAllBots();
            Map<String, String> pathToProjectMap = botManager.getPathToProjectMap();
            
            StringBuilder status = new StringBuilder();
            status.append("=== Telegram Multi-Bot Status ===\n");
            status.append("Total Active Bots: ").append(allBots.size()).append("\n");
            status.append("Debug Mode: ").append(botConfig.isDebugMode()).append("\n\n");
            
            for (Map.Entry<String, TelegramBotConfig.BotInfo> entry : botConfig.getBots().entrySet()) {
                String project = entry.getKey();
                TelegramBotConfig.BotInfo botInfo = entry.getValue();
                boolean isActive = allBots.containsKey(project);
                
                status.append("Project: ").append(project).append("\n");
                status.append("  Status: ").append(isActive ? "ACTIVE" : "INACTIVE").append("\n");
                status.append("  Bot Username: ").append(botInfo.getBotUsername()).append("\n");
                status.append("  Webhook Path: ").append(botInfo.getWebhookPath()).append("\n");
                status.append("  Description: ").append(botInfo.getDescription()).append("\n");
                status.append("  Enabled: ").append(botInfo.isEnabled()).append("\n\n");
            }
            
            return ResponseEntity.ok(status.toString());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("获取状态信息失败: " + e.getMessage());
        }
    }
}
