package com.kikitrade.kweb.controller.dingtalk;

import com.kikitrade.asset.api.RemoteAssetOperateService;
import com.kikitrade.kweb.annotation.V1RestController;
import com.kikitrade.kweb.controller.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 * @date 2024/11/13 下午5:48
 */
@V1RestController
@Slf4j
@RequestMapping("/dingtalk/webhook")
public class DingtalkController extends BaseController {

    @DubboReference
    private RemoteAssetOperateService remoteAssetOperateService;


    @PostMapping("/notify")
    public void dingTalkNotify(@RequestParam(value = "batch_id", required = false) String batchId,
                               @RequestParam(value = "operator", required = false) String operator) {
        log.info("receive dingtalk notification {}, {}", batchId, operator);
        remoteAssetOperateService.operateBatchAsset(batchId);
    }

}
