package com.kikitrade.kweb.controller.telegram.service;

import com.kikitrade.activity.api.RemoteAuthService;
import com.kikitrade.activity.api.model.AuthRequest;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.methods.BotApiMethod;
import org.telegram.telegrambots.meta.api.objects.Message;
import org.telegram.telegrambots.meta.api.objects.Update;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Trex项目专用的Telegram消息处理服务
 * 负责处理Trex项目的Telegram更新和消息
 *
 * <AUTHOR>
 * @date 2025/7/23
 */
@Service
@Slf4j
public class TrexTelegramMessageService {

    private String projectName = "trex";
    private boolean debugMode = false;

    @DubboReference
    private RemoteAuthService remoteAuthService;
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;

    // 匹配命令和参数的正则表达式，支持 /start=123 或 /start 123 格式
    private static final Pattern COMMAND_PATTERN = Pattern.compile("^/([a-zA-Z0-9_]+)(?:[=\\s]+(.+))?$");

    /**
     * 设置项目配置信息
     */
    public void setProjectConfig(String projectName, boolean debugMode) {
        this.projectName = projectName;
        this.debugMode = debugMode;
    }

    /**
     * 处理 Telegram 更新
     * 
     * @param update Telegram 更新对象
     * @return 返回给 Telegram 的方法
     */
    public BotApiMethod<?> processUpdate(Update update) {
        log.info("[{}] 开始处理Telegram更新", projectName);
        
        // 处理文本消息（包括命令）
        if (update.hasMessage() && update.getMessage().hasText()) {
            return processTextMessage(update.getMessage());
        }

        // 处理其他类型的更新
        log.debug("[{}] 收到未处理的更新类型: {}", projectName, update.getUpdateId());
        return null;
    }
    
    /**
     * 处理文本消息
     * 
     * @param message 消息对象
     * @return 处理结果
     */
    private BotApiMethod<?> processTextMessage(Message message) {
        String messageText = message.getText().trim();
        Long userId = message.getFrom().getId();
        String username = message.getFrom().getUserName();
        
        log.info("[{}] 收到用户消息 - 用户ID: {}, 用户名: {}, 消息内容: {}", 
                projectName, userId, username, debugMode ? messageText : "***");
        
        // 检查是否为命令
        Matcher matcher = COMMAND_PATTERN.matcher(messageText);
        if (matcher.matches()) {
            String command = matcher.group(1);
            String args = matcher.group(2);
            
            log.info("[{}] 检测到命令 - 命令: {}, 参数: {}", projectName, command, args);
            
            return processCommand(message, command, args);
        } else {
            // 处理普通文本消息
            log.info("[{}] 收到普通文本消息: {}", projectName, debugMode ? messageText : "***");
            return processPlainTextMessage(message, messageText);
        }
    }
    
    /**
     * 处理命令
     */
    private BotApiMethod<?> processCommand(Message message, String command, String args) {
        switch (command.toLowerCase()) {
            case "start":
                return handleStartCommand(message, args);
            case "help":
                return handleHelpCommand(message);
            default:
                log.info("[{}] 未知命令: {}", projectName, command);
                return null;
        }
    }
    
    /**
     * 处理start命令
     */
    private BotApiMethod<?> handleStartCommand(Message message, String args) {
        log.info("[{}] 处理start命令 - 参数: {}", projectName, args);

        CustomerBindDTO customerBindDTO = remoteCustomerBindService.findById("trex", args);
        if (customerBindDTO == null) {
            log.info("[{}] 用户不存在：{}", projectName, args);
            return null;
        }
        AuthRequest authRequest = new AuthRequest();
        authRequest.setSaasId("trex");
        authRequest.setPlatform("telegram");
        authRequest.setCustomerId(customerBindDTO.getUid());
        authRequest.setSocialId(message.getFrom().getId().toString());
        authRequest.setSocialName(message.getFrom().getFirstName() + " " + message.getFrom().getLastName());
        try {
            log.info("[{}] authRequest:{}", projectName, authRequest);
            remoteAuthService.auth(authRequest);
        }catch (ActivityException ex){
            if (ex.getCode() == ActivityResponseCode.AUTH_REPEAT) {
                log.info("[{}] auth.repeat", projectName);

                return null;
            }
        }catch (Exception e) {
            log.error("[{}] authRequest error:{}", projectName, authRequest, e);
        }
        return null;
    }
    
    /**
     * 处理help命令
     */
    private BotApiMethod<?> handleHelpCommand(Message message) {
        log.info("[{}] 处理help命令", projectName);
        
        // 这里可以返回帮助信息
        
        return null;
    }
    
    /**
     * 处理普通文本消息
     */
    private BotApiMethod<?> processPlainTextMessage(Message message, String messageText) {
        log.info("[{}] 处理普通文本消息", projectName);
        
        // 这里可以添加对普通文本消息的处理逻辑
        
        return null;
    }
}
