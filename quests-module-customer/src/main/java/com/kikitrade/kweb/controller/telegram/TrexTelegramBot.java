package com.kikitrade.kweb.controller.telegram;

import com.kikitrade.kweb.controller.telegram.config.TelegramBotConfig;
import com.kikitrade.kweb.controller.telegram.service.TrexTelegramMessageService;
import lombok.extern.slf4j.Slf4j;
import org.telegram.telegrambots.bots.TelegramWebhookBot;
import org.telegram.telegrambots.meta.api.methods.BotApiMethod;
import org.telegram.telegrambots.meta.api.objects.Update;

/**
 * Trex项目专用的Telegram Bot
 * 负责处理Trex项目的Webhook更新
 *
 * <AUTHOR>
 * @date 2025/7/22 16:49
 */
@Slf4j
public class TrexTelegramBot extends TelegramWebhookBot {

    private final String projectName;
    private final TelegramBotConfig.BotInfo botInfo;
    private final TrexTelegramMessageService messageService;

    public TrexTelegramBot(String projectName, TelegramBotConfig.BotInfo botInfo, TrexTelegramMessageService messageService) {
        this.projectName = projectName;
        this.botInfo = botInfo;
        this.messageService = messageService;
    }

    @Override
    public String getBotUsername() {
        return botInfo.getBotUsername();
    }

    @Override
    public String getBotToken() {
        return botInfo.getBotToken();
    }

    /**
     * 处理 Telegram 更新的核心方法
     *
     * @param update Telegram 更新对象
     * @return 返回给 Telegram 的方法，如果不需要回复则返回 null
     */
    @Override
    public BotApiMethod<?> onWebhookUpdateReceived(Update update) {
        log.info("[{}] 收到 Telegram 更新，ID: {}", projectName, update.getUpdateId());

        try {
            return messageService.processUpdate(update);
        } catch (Exception e) {
            log.error("[{}] 处理 Telegram 更新失败", projectName, e);
            return null;
        }
    }

    @Override
    public String getBotPath() {
        return botInfo.getWebhookPath();
    }

    /**
     * 获取项目名称
     */
    public String getProjectName() {
        return projectName;
    }
}
