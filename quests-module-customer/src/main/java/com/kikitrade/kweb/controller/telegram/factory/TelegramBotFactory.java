package com.kikitrade.kweb.controller.telegram.factory;

import com.kikitrade.kweb.controller.telegram.TrexTelegramBot;
import com.kikitrade.kweb.controller.telegram.config.TelegramBotConfig;
import com.kikitrade.kweb.controller.telegram.service.TrexTelegramMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.telegram.telegrambots.bots.TelegramWebhookBot;

/**
 * Telegram机器人工厂
 * 负责创建不同项目的机器人实例，确保依赖注入正常工作
 * 
 * <AUTHOR>
 * @date 2025/7/23
 */
@Component
@Slf4j
public class TelegramBotFactory {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    @Autowired
    private TrexTelegramMessageService trexTelegramMessageService;
    
    /**
     * 根据项目名称创建机器人实例
     * 
     * @param projectName 项目名称
     * @param botInfo 机器人配置信息
     * @param debugMode 调试模式
     * @return 机器人实例
     */
    public TelegramWebhookBot createBot(String projectName, TelegramBotConfig.BotInfo botInfo, boolean debugMode) {
        switch (projectName.toLowerCase()) {
            case "trex":
                return createTrexBot(projectName, botInfo, debugMode);
            // 可以在这里添加其他项目的机器人创建逻辑
            default:
                log.warn("未知的项目名称: {}，使用默认Trex机器人实现", projectName);
                return createTrexBot(projectName, botInfo, debugMode);
        }
    }
    
    /**
     * 创建Trex项目的机器人实例
     */
    private TelegramWebhookBot createTrexBot(String projectName, TelegramBotConfig.BotInfo botInfo, boolean debugMode) {
        // 设置消息服务的项目配置
        trexTelegramMessageService.setProjectConfig(projectName, debugMode);
        
        // 创建机器人实例
        TrexTelegramBot bot = new TrexTelegramBot(projectName, botInfo, trexTelegramMessageService);
        
        log.info("成功创建Trex机器人实例 - 项目: {}, 用户名: {}", projectName, botInfo.getBotUsername());
        return bot;
    }

}
