package com.kikitrade.kweb.controller.telegram.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Telegram Bot 多机器人配置类
 * 支持配置多个机器人，每个机器人对应一个项目
 *
 * <AUTHOR>
 * @date 2025/7/23
 */
@Data
@Component
@ConfigurationProperties(prefix = "telegram")
public class TelegramBotConfig {

    /**
     * 全局调试模式
     */
    private boolean debugMode = false;

    /**
     * 多机器人配置
     * key: 项目名称 (如: trex, vibra, kiki)
     * value: 机器人配置信息
     */
    private Map<String, BotInfo> bots = new HashMap<>();

    /**
     * 单个机器人配置信息
     */
    @Data
    public static class BotInfo {
        /**
         * Bot 用户名
         */
        private String botUsername;

        /**
         * Bot Token
         */
        private String botToken;

        /**
         * Webhook 路径前缀 (如: /telegram/webhook/trex)
         */
        private String webhookPath;

        /**
         * 项目描述
         */
        private String description;

        /**
         * 是否启用该机器人
         */
        private boolean enabled = true;
    }
}