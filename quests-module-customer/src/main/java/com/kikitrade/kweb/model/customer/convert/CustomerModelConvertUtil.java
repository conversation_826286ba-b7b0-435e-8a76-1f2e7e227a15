package com.kikitrade.kweb.model.customer.convert;

import com.kikitrade.customer.generated.model.v2.CustomerVO;
import com.kikitrade.customer.generated.model.v2.WalletAddressVO;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.model.CustomerExtraDTO;
import com.kikitrade.kcustomer.api.model.WalletAddressDTO;
import com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants;
import com.kikitrade.kcustomer.common.util.RegexChecker;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * Model 转换
 * @author: lizhifeng
 * @date: 2023/5/8 16:27
 */
public class CustomerModelConvertUtil {

    public static CustomerVO of(CustomerExtraDTO customerExtraDTO, WalletAddressDTO walletAddressDTO) {
        if (customerExtraDTO == null) {
            return null;
        }
        CustomerVO customerVO = new CustomerVO();
        customerVO.setId(customerExtraDTO.getId());
        customerVO.setEmail(customerExtraDTO.getEmail());
        customerVO.setUserName(regularUserNameString(customerExtraDTO.getUserName()));
        customerVO.setStatus(customerExtraDTO.getStatus());
        customerVO.setIdCertifiedStatus(customerExtraDTO.getIdCertifiedStatus());
        customerVO.setIsEmailCertified(customerExtraDTO.getIsEmailCertified());
        customerVO.setIsBindEmail(StringUtils.isNotBlank(customerExtraDTO.getEmail()));
        customerVO.setIsPhoneCertified(customerExtraDTO.getIsPhoneCertified());
        customerVO.setIsBindPhone(StringUtils.isNotBlank(customerExtraDTO.getPhone()));
        customerVO.setIsGoogleCertified(customerExtraDTO.getIsGoogleCertified());
        customerVO.setIsBindGoogle(customerExtraDTO.getIsBindGoogle());
        customerVO.setIsBindWeb3(StringUtils.isNotBlank(customerExtraDTO.getPublicKey()));
        customerVO.setLocale(customerExtraDTO.getLocale());
        customerVO.setPhone(regularPhoneString(customerExtraDTO.getPhone()));
        customerVO.setRegisterType(registerByPhone(customerExtraDTO) ? 1 : 0);
        customerVO.setIsMigration(1);
        customerVO.setNoPassword(!customerExtraDTO.isExistPassword());
        customerVO.setIsExistAssetPassword(customerExtraDTO.isExistAssetPassword());
        customerVO.setRegion(customerExtraDTO.getRegion());
        customerVO.setKycLevel(Optional.ofNullable(customerExtraDTO.getKycAuthLevel()).orElse(CustomerIdentityConstants.KycLevel.L0).name());
        customerVO.setVipEndTime(customerExtraDTO.getVipEndTime());
        customerVO.setMerchantId(customerExtraDTO.getMerchantId());
        customerVO.setMerchantName(customerExtraDTO.getMerchantName());
        customerVO.setWhatsAppId(customerExtraDTO.getWhatsAppId());
        customerVO.setTelegramId(customerExtraDTO.getTelegramId());
        customerVO.setLineId(customerExtraDTO.getLineId());
        customerVO.setMiddleName(customerExtraDTO.getMiddleName());
        customerVO.setAvatar(customerExtraDTO.getAvatar());
        customerVO.setGender(customerExtraDTO.getGender());
        customerVO.setNickName(customerExtraDTO.getNickName());
        customerVO.setPublicKey(customerExtraDTO.getPublicKey());
        customerVO.setWalletAddress(customerExtraDTO.getWalletAddress());
        // web3auth 钱包地址
        if (walletAddressDTO != null && MapUtils.isNotEmpty(walletAddressDTO.getWeb3auth())) {
            WalletAddressVO walletAddresses = new WalletAddressVO();
            walletAddresses.setWeb3auth(walletAddressDTO.getWeb3auth());
            customerVO.setAddress(walletAddresses);
        }
        return customerVO;
    }

    public static CustomerVO extractSimpleInfo(CustomerExtraDTO customerDTO) {
        CustomerVO customerVO = new CustomerVO();
        customerVO.setId(customerDTO.getId());
        customerVO.setEmail(regularEmailString(customerDTO.getEmail()));
        customerVO.setUserName(regularUserNameString(customerDTO.getUserName()));
        customerVO.setStatus(customerDTO.getStatus());
        customerVO.setIdCertifiedStatus(customerDTO.getIdCertifiedStatus());
        customerVO.setIsEmailCertified(customerDTO.getIsEmailCertified());
        customerVO.setIsBindEmail(StringUtils.isNotBlank(customerDTO.getEmail()));
        customerVO.setIsPhoneCertified(customerDTO.getIsPhoneCertified());
        customerVO.setIsBindPhone(StringUtils.isNotBlank(customerDTO.getPhone()));
        customerVO.setIsGoogleCertified(customerDTO.getIsGoogleCertified());
        customerVO.setIsBindGoogle(customerDTO.getIsBindGoogle());
        customerVO.setIsBindWeb3(StringUtils.isNotBlank(customerDTO.getPublicKey()));
        customerVO.setLocale(customerDTO.getLocale());
        customerVO.setPhone(regularPhoneString(customerDTO.getPhone()));
        customerVO.setRegisterType(registerByPhone(customerDTO) ? 1 : 0);
        customerVO.setIsMigration(1);    // 1-已经进行数据迁移；0-未进行数据迁移。既然能访问到kweb的这个接口，说明肯定是进行数据迁移过的
        customerVO.setIsExistAssetPassword(customerDTO.isExistAssetPassword());
        customerVO.setRegion(customerDTO.getRegion());
        if (null != customerDTO.getVipLevel()) {
            customerVO.setVipLevel(customerDTO.getVipLevel().name());
        }
        customerVO.setVipEndTime(customerDTO.getVipEndTime());
        if (StringUtils.isNotBlank(customerDTO.getNickName())) {
            customerVO.setNickName(customerDTO.getNickName());
        }
        if (StringUtils.isNotBlank(customerDTO.getAvatar())) {
            customerVO.setAvatar(customerDTO.getAvatar());
        }
        if (customerDTO.getGender() != null) {
            customerVO.setGender(customerDTO.getGender());
        }
        customerVO.setPublicKey(customerDTO.getPublicKey());
        customerVO.setWalletAddress(customerDTO.getWalletAddress());
        return customerVO;
    }

    public static boolean registerByPhone(CustomerDTO customerDTO) {
        return StringUtils.isNotBlank(customerDTO.getPhone()) && StringUtils.isNotBlank(customerDTO.getUserName()) && customerDTO.getUserName().equals(customerDTO.getPhone());
    }

    public static String regularUserNameString(String str) {
        if (StringUtils.isNotBlank(str) && str.length() > 4) {
            if (RegexChecker.isMail(str)) {
                return regularEmailString(str);
            } else {
                return regularPhoneString(str);
            }
        }
        return str;
    }

    public static String regularPhoneString(String str) {
        if (StringUtils.isNotBlank(str) && str.length() > 6) {
            return str.substring(0, 6) + "****" + str.substring(str.length() - 2);
        }
        return str;
    }

    public static String regularEmailString(String str) {
        if (StringUtils.isNotBlank(str) && str.length() > 4 && RegexChecker.isMail(str)) {
            return str.substring(0, 2) + "****" + str.substring(str.lastIndexOf("@"));
        }
        return str;
    }

}
