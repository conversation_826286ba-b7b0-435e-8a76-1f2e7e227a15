package com.kikitrade.kweb.model.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/5/29
 * @desc
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class CustomerTokenResponse implements Serializable {

    @Schema(description = "jwt token")
    private String jwtKey;

    @Schema(description = "jwt token 过期时间")
    private Long jwtKeyExpireIn;

    @Schema(description = "续期token")
    private String refreshToken;

    @Schema(description = "续期token过期时间")
    private Long refreshTokenExpireIn;

}
