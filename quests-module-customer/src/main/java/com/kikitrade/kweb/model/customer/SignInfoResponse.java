package com.kikitrade.kweb.model.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/25
 * @desc
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class SignInfoResponse implements Serializable {

    /**
     * IM签名信息
     */
    @Schema(description = "IM用户签名", example = "任意字符串", title = "签名")
    private String sign;
    /**
     * 三方渠道IM用户Id
     */
    @Schema(description = "IM用户ID", example = "32位以内字符串", title = "im用户ID")
    private String imUid;
}
