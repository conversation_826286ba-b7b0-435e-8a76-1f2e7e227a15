package com.kikitrade.kweb.service.customer;

import org.apache.dubbo.config.annotation.DubboReference;
import com.kikitrade.kcustomer.api.service.RemoteCredentialService;
import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import org.springframework.stereotype.Service;

/**
 * @author: penuel
 * @date: 2022/7/22 14:26
 * @desc: TODO
 */
@Service
public class CustomerServiceReference {

    @DubboReference
    private RemoteCustomerService remoteCustomerService;
    @DubboReference
    private RemoteCredentialService remoteCredentialService;

    public RemoteCustomerService getRemoteCustomerService() {
        return remoteCustomerService;
    }

    public RemoteCredentialService getRemoteCredentialService() {
        return remoteCredentialService;
    }


}
