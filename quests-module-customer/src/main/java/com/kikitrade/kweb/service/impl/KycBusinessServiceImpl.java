package com.kikitrade.kweb.service.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.kcustomer.api.model.CustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerService;
import com.kikitrade.kcustomer.common.constants.CustomerIdentityConstants;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.model.common.WebResponse;
import com.kikitrade.kweb.service.KycBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @author: wang
 * @date: 2022/3/16
 * @desc:
 */
@Service
@Slf4j
public class KycBusinessServiceImpl implements KycBusinessService {
    @DubboReference
    private RemoteCustomerService remoteCustomerService;

    @Override
    public WebResponse checkCustomer(CustomerDTO customer) {
        customer = remoteCustomerService.getById(customer.getSaasId(), customer.getId());
        log.info("KycBusinessServiceImpl checkCustomer {}", JSONObject.toJSONString(customer));
        // 检查手机是否绑定
        if (!customer.getIsPhoneCertified()) {
            return WebResponse.result(WebResponseEnum.CUSTOMER_VERIFY_PHONE_UNBIND);
        }
        if (!customer.getIsEmailCertified()) {
            return WebResponse.result(WebResponseEnum.CUSTOMER_VERIFY_EMAIL_UNBIND);
        }
        return WebResponse.success();
    }

    @Override
    public WebResponse checkKyc1(CustomerDTO customer) {
        customer = remoteCustomerService.getById(customer.getSaasId(), customer.getId());
        log.info("KycBusinessServiceImpl checkKyc1 {}", JSONObject.toJSONString(customer));
        boolean check = customer.getKycAuthLevel().greaterThanOrEqual(CustomerIdentityConstants.KycLevel.L1);
        if (!check) {
            return WebResponse.result(WebResponseEnum.CUSTOMER_KYC1_NOT_SUBMIT);
        }
        return WebResponse.success();
    }
}
