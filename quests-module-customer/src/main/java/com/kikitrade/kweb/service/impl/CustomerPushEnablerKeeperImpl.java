package com.kikitrade.kweb.service.impl;

import com.kikitrade.kweb.service.CustomerPushEnabler;
import com.kikitrade.kweb.service.CustomerPushEnablerKeeper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class CustomerPushEnablerKeeperImpl implements CustomerPushEnablerKeeper {
    @Autowired(required = false)
    List<CustomerPushEnabler> customerPushEnablerList;

    @Override
    public boolean pushEnabled(String name, String customerId) {
        if (customerPushEnablerList == null) {
            return false;
        }
        Optional<CustomerPushEnabler> first = customerPushEnablerList.stream().filter(e -> e.name().equals(name)).findFirst();
        if (!first.isPresent()) {
            return false;
        }
        return first.get().enabled(customerId);
    }
}
