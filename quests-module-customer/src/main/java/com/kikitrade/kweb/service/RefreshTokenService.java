package com.kikitrade.kweb.service;

import com.alibaba.fastjson.JSON;
import com.kikitrade.kweb.constants.RedisKeyConstants;
import com.kikitrade.kweb.model.customer.CustomerTokenResponse;
import com.kikitrade.kweb.utils.JwtUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * refresh token service
 */
@Service
public class RefreshTokenService {


    @Value("${kweb.customer.jwt.refresh.expire:604800000}") // 7天
    private Long refreshTokenExpireMs;

    public final RedisTemplate<String, String> redisTemplate;

    private String getKey(String refreshToken) {
        return RedisKeyConstants.CUSTOMER_REFRESH_TOKEN + refreshToken;
    }

    public RefreshTokenService(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public JwtUtil.JwtTokenBody findByToken(String refreshToken) {
        String body = redisTemplate.opsForValue().get(getKey(refreshToken));
        return JSON.parseObject(body, JwtUtil.JwtTokenBody.class);
    }

    public CustomerTokenResponse createRefreshToken(JwtUtil.JwtTokenBody body) {
        String refreshToken = UUID.randomUUID().toString();
        long expireIn = System.currentTimeMillis() + refreshTokenExpireMs;
        redisTemplate.opsForValue().set(getKey(refreshToken), JSON.toJSONString(body), refreshTokenExpireMs, TimeUnit.MILLISECONDS);

        CustomerTokenResponse refreshTokenDTO = new CustomerTokenResponse();
        refreshTokenDTO.setRefreshToken(refreshToken);
        refreshTokenDTO.setRefreshTokenExpireIn(expireIn);
        return refreshTokenDTO;
    }

    public void removeToken(String refreshToken) {
        redisTemplate.delete(getKey(refreshToken));
    }
}
