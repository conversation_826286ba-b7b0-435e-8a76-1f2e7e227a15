package com.kikitrade.customer.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * CustomerBindVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class CustomerBindVO {

  @JsonProperty("cid")
  private String cid;

  @JsonProperty("uid")
  private String uid;

  @JsonProperty("saasId")
  private String saasId;

  @JsonProperty("avatar")
  private String avatar;

  @JsonProperty("jwt")
  private String jwt;

  @JsonProperty("mugenJwt")
  private String mugenJwt;

  @JsonProperty("available")
  private BigDecimal available;

  @JsonProperty("twitterName")
  private String twitterName;

  public CustomerBindVO cid(String cid) {
    this.cid = cid;
    return this;
  }

  /**
   * Get cid
   * @return cid
  */
  
  @Schema(name = "cid", required = false)
  public String getCid() {
    return cid;
  }

  public void setCid(String cid) {
    this.cid = cid;
  }

  public CustomerBindVO uid(String uid) {
    this.uid = uid;
    return this;
  }

  /**
   * Get uid
   * @return uid
  */
  
  @Schema(name = "uid", required = false)
  public String getUid() {
    return uid;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }

  public CustomerBindVO saasId(String saasId) {
    this.saasId = saasId;
    return this;
  }

  /**
   * Get saasId
   * @return saasId
  */
  
  @Schema(name = "saasId", required = false)
  public String getSaasId() {
    return saasId;
  }

  public void setSaasId(String saasId) {
    this.saasId = saasId;
  }

  public CustomerBindVO avatar(String avatar) {
    this.avatar = avatar;
    return this;
  }

  /**
   * 头像
   * @return avatar
  */
  
  @Schema(name = "avatar", description = "头像", required = false)
  public String getAvatar() {
    return avatar;
  }

  public void setAvatar(String avatar) {
    this.avatar = avatar;
  }

  public CustomerBindVO jwt(String jwt) {
    this.jwt = jwt;
    return this;
  }

  /**
   * Get jwt
   * @return jwt
  */
  
  @Schema(name = "jwt", required = false)
  public String getJwt() {
    return jwt;
  }

  public void setJwt(String jwt) {
    this.jwt = jwt;
  }

  public CustomerBindVO mugenJwt(String mugenJwt) {
    this.mugenJwt = mugenJwt;
    return this;
  }

  /**
   * Get mugenJwt
   * @return mugenJwt
  */
  
  @Schema(name = "mugenJwt", required = false)
  public String getMugenJwt() {
    return mugenJwt;
  }

  public void setMugenJwt(String mugenJwt) {
    this.mugenJwt = mugenJwt;
  }

  public CustomerBindVO available(BigDecimal available) {
    this.available = available;
    return this;
  }

  /**
   * Get available
   * @return available
  */
  @Valid 
  @Schema(name = "available", required = false)
  public BigDecimal getAvailable() {
    return available;
  }

  public void setAvailable(BigDecimal available) {
    this.available = available;
  }

  public CustomerBindVO twitterName(String twitterName) {
    this.twitterName = twitterName;
    return this;
  }

  /**
   * Get twitterName
   * @return twitterName
  */
  
  @Schema(name = "twitterName", required = false)
  public String getTwitterName() {
    return twitterName;
  }

  public void setTwitterName(String twitterName) {
    this.twitterName = twitterName;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomerBindVO customerBindVO = (CustomerBindVO) o;
    return Objects.equals(this.cid, customerBindVO.cid) &&
        Objects.equals(this.uid, customerBindVO.uid) &&
        Objects.equals(this.saasId, customerBindVO.saasId) &&
        Objects.equals(this.avatar, customerBindVO.avatar) &&
        Objects.equals(this.jwt, customerBindVO.jwt) &&
        Objects.equals(this.mugenJwt, customerBindVO.mugenJwt) &&
        Objects.equals(this.available, customerBindVO.available) &&
        Objects.equals(this.twitterName, customerBindVO.twitterName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(cid, uid, saasId, avatar, jwt, mugenJwt, available, twitterName);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomerBindVO {\n");
    sb.append("    cid: ").append(toIndentedString(cid)).append("\n");
    sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
    sb.append("    saasId: ").append(toIndentedString(saasId)).append("\n");
    sb.append("    avatar: ").append(toIndentedString(avatar)).append("\n");
    sb.append("    jwt: ").append(toIndentedString(jwt)).append("\n");
    sb.append("    mugenJwt: ").append(toIndentedString(mugenJwt)).append("\n");
    sb.append("    available: ").append(toIndentedString(available)).append("\n");
    sb.append("    twitterName: ").append(toIndentedString(twitterName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

