package com.kikitrade.customer.generated.api.v2;

import com.kikitrade.customer.generated.model.v2.SendVerifyCodeRequest;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.customer.generated.model.v2.WebResultCurrentUserVO;
import com.kikitrade.customer.generated.model.v2.WebResultCustomerBindVO;
import com.kikitrade.customer.generated.model.v2.WebResultSocialTokenVO;
import com.kikitrade.customer.generated.model.v2.WebResultVerifyVO;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.*;
import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Controller
@RequestMapping("${openapi.kweb.base-path:/v2}")
public class CustomerV2ApiController implements CustomerV2Api {

    private final CustomerV2ApiDelegate delegate;

    public CustomerV2ApiController(@Autowired(required = false) CustomerV2ApiDelegate delegate) {
        this.delegate = Optional.ofNullable(delegate).orElse(new CustomerV2ApiDelegate() {});
    }

    @Override
    public CustomerV2ApiDelegate getDelegate() {
        return delegate;
    }

}
