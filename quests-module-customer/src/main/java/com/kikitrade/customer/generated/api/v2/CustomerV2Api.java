/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.0.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.kikitrade.customer.generated.api.v2;

import com.kikitrade.customer.generated.model.v2.SendVerifyCodeRequest;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.customer.generated.model.v2.WebResultCurrentUserVO;
import com.kikitrade.customer.generated.model.v2.WebResultCustomerBindVO;
import com.kikitrade.customer.generated.model.v2.WebResultSocialTokenVO;
import com.kikitrade.customer.generated.model.v2.WebResultVerifyVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "CustomerV2", description = "用户相关接口描述")
public interface CustomerV2Api {

    default CustomerV2ApiDelegate getDelegate() {
        return new CustomerV2ApiDelegate() {};
    }

    /**
     * POST /customer/oauth/social/token : get twitter/discord token
     * 三方认证登录
     *
     * @param app twitter、discord、google、facebook (required)
     * @param platform twitter、discord (required)
     * @param code code(一次性) (required)
     * @param idToken  (optional)
     * @param saasId  (optional)
     * @param redirectUri redirectUri (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "authToken",
        summary = "get twitter/discord token",
        tags = { "CustomerV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultSocialTokenVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customer/oauth/social/token",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultSocialTokenVO> authToken(
        @NotNull @Parameter(name = "app", description = "twitter、discord、google、facebook", required = true) @Valid @RequestParam(value = "app", required = true) String app,
        @NotNull @Parameter(name = "platform", description = "twitter、discord", required = true) @Valid @RequestParam(value = "platform", required = true) String platform,
        @NotNull @Parameter(name = "code", description = "code(一次性)", required = true) @Valid @RequestParam(value = "code", required = true) String code,
        @Parameter(name = "idToken", description = "") @RequestHeader(value = "idToken", required = false) String idToken,
        @Parameter(name = "saas_id", description = "") @RequestHeader(value = "saas_id", required = false) String saasId,
        @Parameter(name = "redirectUri", description = "redirectUri") @Valid @RequestParam(value = "redirectUri", required = false) String redirectUri
    ) {
        return getDelegate().authToken(app, platform, code, idToken, saasId, redirectUri);
    }


    /**
     * GET /customer/me : get current time
     * 三方认证登录
     *
     * @param idToken  (optional)
     * @param JWT_TOKEN  (optional)
     * @param saasId  (optional)
     * @param businessType 默认返回 twitterName, 还支持 invite、asset、rank、exp、ticket、all (optional)
     * @param handleName osp handleName (optional)
     * @param appId osp appId (optional)
     * @param chainId osp chainId (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "me",
        summary = "get current time",
        tags = { "CustomerV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultCurrentUserVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/customer/me",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultCurrentUserVO> me(
        @Parameter(name = "idToken", description = "") @RequestHeader(value = "idToken", required = false) String idToken,
        @Parameter(name = "JWT_TOKEN", description = "") @RequestHeader(value = "JWT_TOKEN", required = false) String JWT_TOKEN,
        @Parameter(name = "saas_id", description = "") @RequestHeader(value = "saas_id", required = false) String saasId,
        @Parameter(name = "businessType", description = "默认返回 twitterName, 还支持 invite、asset、rank、exp、ticket、all") @Valid @RequestParam(value = "businessType", required = false) String businessType,
        @Parameter(name = "handleName", description = "osp handleName") @Valid @RequestParam(value = "handleName", required = false) String handleName,
        @Parameter(name = "appId", description = "osp appId") @Valid @RequestParam(value = "appId", required = false) String appId,
        @Parameter(name = "chainId", description = "osp chainId") @Valid @RequestParam(value = "chainId", required = false) String chainId
    ) {
        return getDelegate().me(idToken, JWT_TOKEN, saasId, businessType, handleName, appId, chainId);
    }


    /**
     * POST /customer/login/oauth : 三方认证登录
     * 三方认证登录
     *
     * @param idToken 三方 token (required)
     * @param publicKey 三方 publicKey (required)
     * @param walletAddress 钱包账户地址 (required)
     * @param thirdAuthSource 三方认证来源 (EXTERNAL_SOCIAL、EXTERNAL_WALLET) (required)
     * @param saasId  (optional)
     * @param walletType 钱包类型 (evm、sui), 默认值为 evm (optional)
     * @param autoRegister 是否自动注册 (即账户未注册的情况下，注册后登录)，默认 false (optional, default to false)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "oauthLogin",
        summary = "三方认证登录",
        tags = { "CustomerV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultCustomerBindVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customer/login/oauth",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultCustomerBindVO> oauthLogin(
        @NotNull @Parameter(name = "idToken", description = "三方 token", required = true) @Valid @RequestParam(value = "idToken", required = true) String idToken,
        @NotNull @Parameter(name = "publicKey", description = "三方 publicKey", required = true) @Valid @RequestParam(value = "publicKey", required = true) String publicKey,
        @NotNull @Parameter(name = "walletAddress", description = "钱包账户地址", required = true) @Valid @RequestParam(value = "walletAddress", required = true) String walletAddress,
        @NotNull @Parameter(name = "thirdAuthSource", description = "三方认证来源 (EXTERNAL_SOCIAL、EXTERNAL_WALLET)", required = true) @Valid @RequestParam(value = "thirdAuthSource", required = true) String thirdAuthSource,
        @Parameter(name = "saas_id", description = "") @RequestHeader(value = "saas_id", required = false) String saasId,
        @Parameter(name = "walletType", description = "钱包类型 (evm、sui), 默认值为 evm") @Valid @RequestParam(value = "walletType", required = false) String walletType,
        @Parameter(name = "autoRegister", description = "是否自动注册 (即账户未注册的情况下，注册后登录)，默认 false") @Valid @RequestParam(value = "autoRegister", required = false, defaultValue = "false") Boolean autoRegister
    ) {
        return getDelegate().oauthLogin(idToken, publicKey, walletAddress, thirdAuthSource, saasId, walletType, autoRegister);
    }


    /**
     * POST /customer/refreshVerified : osp verify
     * 刷新认证
     *
     * @param idToken  (optional)
     * @param JWT_TOKEN  (optional)
     * @param saasId  (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "refreshVerified",
        summary = "osp verify",
        tags = { "CustomerV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultVerifyVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customer/refreshVerified",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultVerifyVO> refreshVerified(
        @Parameter(name = "idToken", description = "") @RequestHeader(value = "idToken", required = false) String idToken,
        @Parameter(name = "JWT_TOKEN", description = "") @RequestHeader(value = "JWT_TOKEN", required = false) String JWT_TOKEN,
        @Parameter(name = "saas_id", description = "") @RequestHeader(value = "saas_id", required = false) String saasId
    ) {
        return getDelegate().refreshVerified(idToken, JWT_TOKEN, saasId);
    }


    /**
     * POST /customer/sendVerifyCode : send verify code
     * 发送验证码
     *
     * @param saasId  (required)
     * @param JWT_TOKEN  (optional)
     * @param sendVerifyCodeRequest  (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "sendVerifyCode",
        summary = "send verify code",
        tags = { "CustomerV2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResult.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customer/sendVerifyCode",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<WebResult> sendVerifyCode(
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "JWT_TOKEN", description = "") @RequestHeader(value = "JWT_TOKEN", required = false) String JWT_TOKEN,
        @Parameter(name = "SendVerifyCodeRequest", description = "") @Valid @RequestBody(required = false) SendVerifyCodeRequest sendVerifyCodeRequest
    ) {
        return getDelegate().sendVerifyCode(saasId, JWT_TOKEN, sendVerifyCodeRequest);
    }

}
