package com.kikitrade.customer.generated.api.v2;

import com.kikitrade.customer.generated.model.v2.SendVerifyCodeRequest;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.customer.generated.model.v2.WebResultCurrentUserVO;
import com.kikitrade.customer.generated.model.v2.WebResultCustomerBindVO;
import com.kikitrade.customer.generated.model.v2.WebResultSocialTokenVO;
import com.kikitrade.customer.generated.model.v2.WebResultVerifyVO;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link CustomerV2ApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface CustomerV2ApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * POST /customer/oauth/social/token : get twitter/discord token
     * 三方认证登录
     *
     * @param app twitter、discord、google、facebook (required)
     * @param platform twitter、discord (required)
     * @param code code(一次性) (required)
     * @param idToken  (optional)
     * @param saasId  (optional)
     * @param redirectUri redirectUri (optional)
     * @return Successful operation (status code 200)
     * @see CustomerV2Api#authToken
     */
    default ResponseEntity<WebResultSocialTokenVO> authToken(String app,
        String platform,
        String code,
        String idToken,
        String saasId,
        String redirectUri) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /customer/me : get current time
     * 三方认证登录
     *
     * @param idToken  (optional)
     * @param JWT_TOKEN  (optional)
     * @param saasId  (optional)
     * @param businessType 默认返回 twitterName, 还支持 invite、asset、rank、exp、ticket、all (optional)
     * @param handleName osp handleName (optional)
     * @param appId osp appId (optional)
     * @param chainId osp chainId (optional)
     * @return Successful operation (status code 200)
     * @see CustomerV2Api#me
     */
    default ResponseEntity<WebResultCurrentUserVO> me(String idToken,
        String JWT_TOKEN,
        String saasId,
        String businessType,
        String handleName,
        String appId,
        String chainId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /customer/login/oauth : 三方认证登录
     * 三方认证登录
     *
     * @param idToken 三方 token (required)
     * @param publicKey 三方 publicKey (required)
     * @param walletAddress 钱包账户地址 (required)
     * @param thirdAuthSource 三方认证来源 (EXTERNAL_SOCIAL、EXTERNAL_WALLET) (required)
     * @param saasId  (optional)
     * @param walletType 钱包类型 (evm、sui), 默认值为 evm (optional)
     * @param autoRegister 是否自动注册 (即账户未注册的情况下，注册后登录)，默认 false (optional, default to false)
     * @return Successful operation (status code 200)
     * @see CustomerV2Api#oauthLogin
     */
    default ResponseEntity<WebResultCustomerBindVO> oauthLogin(String idToken,
        String publicKey,
        String walletAddress,
        String thirdAuthSource,
        String saasId,
        String walletType,
        Boolean autoRegister) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /customer/refreshVerified : osp verify
     * 刷新认证
     *
     * @param idToken  (optional)
     * @param JWT_TOKEN  (optional)
     * @param saasId  (optional)
     * @return Successful operation (status code 200)
     * @see CustomerV2Api#refreshVerified
     */
    default ResponseEntity<WebResultVerifyVO> refreshVerified(String idToken,
        String JWT_TOKEN,
        String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /customer/sendVerifyCode : send verify code
     * 发送验证码
     *
     * @param saasId  (required)
     * @param JWT_TOKEN  (optional)
     * @param sendVerifyCodeRequest  (optional)
     * @return Successful operation (status code 200)
     * @see CustomerV2Api#sendVerifyCode
     */
    default ResponseEntity<WebResult> sendVerifyCode(String saasId,
        String JWT_TOKEN,
        SendVerifyCodeRequest sendVerifyCodeRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
