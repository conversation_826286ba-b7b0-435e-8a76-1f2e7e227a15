package com.kikitrade.customer.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * CurrentUserVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class CurrentUserVO {

  @JsonProperty("uid")
  private String uid;

  @JsonProperty("saasId")
  private String saasId;

  @JsonProperty("twitterName")
  private String twitterName;

  @JsonProperty("discordName")
  private String discordName;

  @JsonProperty("invitePoint")
  private BigDecimal invitePoint;

  @JsonProperty("dividePoint")
  private BigDecimal dividePoint;

  @JsonProperty("available")
  private BigDecimal available;

  @JsonProperty("pfp")
  private String pfp;

  @JsonProperty("vip")
  private String vip;

  @JsonProperty("isVip")
  private Boolean isVip;

  @JsonProperty("rankValue")
  private Integer rankValue;

  @JsonProperty("rankPercent")
  private String rankPercent;

  @JsonProperty("expValue")
  private BigDecimal expValue;

  @JsonProperty("ticketAvailable")
  private BigDecimal ticketAvailable;

  @JsonProperty("ticketUsed")
  private BigDecimal ticketUsed;

  @JsonProperty("voucherAvailable")
  private BigDecimal voucherAvailable;

  @JsonProperty("extraBoostValue")
  private BigDecimal extraBoostValue;

  @JsonProperty("boostValue")
  private BigDecimal boostValue;

  @JsonProperty("ojoValue")
  private BigDecimal ojoValue;

  @JsonProperty("credentialsAvailable")
  private BigDecimal credentialsAvailable = new BigDecimal("0");

  @JsonProperty("OSPVerified")
  private Boolean osPVerified = false;

  @JsonProperty("XVerified")
  private Boolean xverified = false;

  @JsonProperty("XVerifiedType")
  private String xverifiedType;

  @JsonProperty("ospAvatar")
  private String ospAvatar;

  @JsonProperty("experienceVoucherAvailable")
  private BigDecimal experienceVoucherAvailable;

  @JsonProperty("seasonPointAvailable")
  private BigDecimal seasonPointAvailable;

  public CurrentUserVO uid(String uid) {
    this.uid = uid;
    return this;
  }

  /**
   * Get uid
   * @return uid
  */
  
  @Schema(name = "uid", required = false)
  public String getUid() {
    return uid;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }

  public CurrentUserVO saasId(String saasId) {
    this.saasId = saasId;
    return this;
  }

  /**
   * Get saasId
   * @return saasId
  */
  
  @Schema(name = "saasId", required = false)
  public String getSaasId() {
    return saasId;
  }

  public void setSaasId(String saasId) {
    this.saasId = saasId;
  }

  public CurrentUserVO twitterName(String twitterName) {
    this.twitterName = twitterName;
    return this;
  }

  /**
   * Get twitterName
   * @return twitterName
  */
  
  @Schema(name = "twitterName", required = false)
  public String getTwitterName() {
    return twitterName;
  }

  public void setTwitterName(String twitterName) {
    this.twitterName = twitterName;
  }

  public CurrentUserVO discordName(String discordName) {
    this.discordName = discordName;
    return this;
  }

  /**
   * Get discordName
   * @return discordName
  */
  
  @Schema(name = "discordName", required = false)
  public String getDiscordName() {
    return discordName;
  }

  public void setDiscordName(String discordName) {
    this.discordName = discordName;
  }

  public CurrentUserVO invitePoint(BigDecimal invitePoint) {
    this.invitePoint = invitePoint;
    return this;
  }

  /**
   * Get invitePoint
   * @return invitePoint
  */
  @Valid 
  @Schema(name = "invitePoint", required = false)
  public BigDecimal getInvitePoint() {
    return invitePoint;
  }

  public void setInvitePoint(BigDecimal invitePoint) {
    this.invitePoint = invitePoint;
  }

  public CurrentUserVO dividePoint(BigDecimal dividePoint) {
    this.dividePoint = dividePoint;
    return this;
  }

  /**
   * Get dividePoint
   * @return dividePoint
  */
  @Valid 
  @Schema(name = "dividePoint", required = false)
  public BigDecimal getDividePoint() {
    return dividePoint;
  }

  public void setDividePoint(BigDecimal dividePoint) {
    this.dividePoint = dividePoint;
  }

  public CurrentUserVO available(BigDecimal available) {
    this.available = available;
    return this;
  }

  /**
   * 用户加成后的积分
   * @return available
  */
  @Valid 
  @Schema(name = "available", description = "用户加成后的积分", required = false)
  public BigDecimal getAvailable() {
    return available;
  }

  public void setAvailable(BigDecimal available) {
    this.available = available;
  }

  public CurrentUserVO pfp(String pfp) {
    this.pfp = pfp;
    return this;
  }

  /**
   * pfp 加成
   * @return pfp
  */
  
  @Schema(name = "pfp", example = "100%", description = "pfp 加成", required = false)
  public String getPfp() {
    return pfp;
  }

  public void setPfp(String pfp) {
    this.pfp = pfp;
  }

  public CurrentUserVO vip(String vip) {
    this.vip = vip;
    return this;
  }

  /**
   * vip 加成
   * @return vip
  */
  
  @Schema(name = "vip", example = "100%", description = "vip 加成", required = false)
  public String getVip() {
    return vip;
  }

  public void setVip(String vip) {
    this.vip = vip;
  }

  public CurrentUserVO isVip(Boolean isVip) {
    this.isVip = isVip;
    return this;
  }

  /**
   * 是否为 vip 用户
   * @return isVip
  */
  
  @Schema(name = "isVip", description = "是否为 vip 用户", required = false)
  public Boolean getIsVip() {
    return isVip;
  }

  public void setIsVip(Boolean isVip) {
    this.isVip = isVip;
  }

  public CurrentUserVO rankValue(Integer rankValue) {
    this.rankValue = rankValue;
    return this;
  }

  /**
   * Get rankValue
   * @return rankValue
  */
  
  @Schema(name = "rankValue", required = false)
  public Integer getRankValue() {
    return rankValue;
  }

  public void setRankValue(Integer rankValue) {
    this.rankValue = rankValue;
  }

  public CurrentUserVO rankPercent(String rankPercent) {
    this.rankPercent = rankPercent;
    return this;
  }

  /**
   * Get rankPercent
   * @return rankPercent
  */
  
  @Schema(name = "rankPercent", required = false)
  public String getRankPercent() {
    return rankPercent;
  }

  public void setRankPercent(String rankPercent) {
    this.rankPercent = rankPercent;
  }

  public CurrentUserVO expValue(BigDecimal expValue) {
    this.expValue = expValue;
    return this;
  }

  /**
   * Get expValue
   * @return expValue
  */
  @Valid 
  @Schema(name = "expValue", required = false)
  public BigDecimal getExpValue() {
    return expValue;
  }

  public void setExpValue(BigDecimal expValue) {
    this.expValue = expValue;
  }

  public CurrentUserVO ticketAvailable(BigDecimal ticketAvailable) {
    this.ticketAvailable = ticketAvailable;
    return this;
  }

  /**
   * 用户加成后的积分
   * @return ticketAvailable
  */
  @Valid 
  @Schema(name = "ticketAvailable", description = "用户加成后的积分", required = false)
  public BigDecimal getTicketAvailable() {
    return ticketAvailable;
  }

  public void setTicketAvailable(BigDecimal ticketAvailable) {
    this.ticketAvailable = ticketAvailable;
  }

  public CurrentUserVO ticketUsed(BigDecimal ticketUsed) {
    this.ticketUsed = ticketUsed;
    return this;
  }

  /**
   * 用户已使用的积分
   * @return ticketUsed
  */
  @Valid 
  @Schema(name = "ticketUsed", description = "用户已使用的积分", required = false)
  public BigDecimal getTicketUsed() {
    return ticketUsed;
  }

  public void setTicketUsed(BigDecimal ticketUsed) {
    this.ticketUsed = ticketUsed;
  }

  public CurrentUserVO voucherAvailable(BigDecimal voucherAvailable) {
    this.voucherAvailable = voucherAvailable;
    return this;
  }

  /**
   * 用户可用的抽奖券
   * @return voucherAvailable
  */
  @Valid 
  @Schema(name = "voucherAvailable", description = "用户可用的抽奖券", required = false)
  public BigDecimal getVoucherAvailable() {
    return voucherAvailable;
  }

  public void setVoucherAvailable(BigDecimal voucherAvailable) {
    this.voucherAvailable = voucherAvailable;
  }

  public CurrentUserVO extraBoostValue(BigDecimal extraBoostValue) {
    this.extraBoostValue = extraBoostValue;
    return this;
  }

  /**
   * 用户持有的有效额外加成
   * @return extraBoostValue
  */
  @Valid 
  @Schema(name = "extraBoostValue", description = "用户持有的有效额外加成", required = false)
  public BigDecimal getExtraBoostValue() {
    return extraBoostValue;
  }

  public void setExtraBoostValue(BigDecimal extraBoostValue) {
    this.extraBoostValue = extraBoostValue;
  }

  public CurrentUserVO boostValue(BigDecimal boostValue) {
    this.boostValue = boostValue;
    return this;
  }

  /**
   * 用户持有的基础加成
   * @return boostValue
  */
  @Valid 
  @Schema(name = "boostValue", description = "用户持有的基础加成", required = false)
  public BigDecimal getBoostValue() {
    return boostValue;
  }

  public void setBoostValue(BigDecimal boostValue) {
    this.boostValue = boostValue;
  }

  public CurrentUserVO ojoValue(BigDecimal ojoValue) {
    this.ojoValue = ojoValue;
    return this;
  }

  /**
   * 用户持有的OJO资产
   * @return ojoValue
  */
  @Valid 
  @Schema(name = "ojoValue", description = "用户持有的OJO资产", required = false)
  public BigDecimal getOjoValue() {
    return ojoValue;
  }

  public void setOjoValue(BigDecimal ojoValue) {
    this.ojoValue = ojoValue;
  }

  public CurrentUserVO credentialsAvailable(BigDecimal credentialsAvailable) {
    this.credentialsAvailable = credentialsAvailable;
    return this;
  }

  /**
   * 用户信用分总分
   * @return credentialsAvailable
  */
  @Valid 
  @Schema(name = "credentialsAvailable", description = "用户信用分总分", required = false)
  public BigDecimal getCredentialsAvailable() {
    return credentialsAvailable;
  }

  public void setCredentialsAvailable(BigDecimal credentialsAvailable) {
    this.credentialsAvailable = credentialsAvailable;
  }

  public CurrentUserVO osPVerified(Boolean osPVerified) {
    this.osPVerified = osPVerified;
    return this;
  }

  /**
   * OSP认证标识
   * @return osPVerified
  */
  
  @Schema(name = "OSPVerified", description = "OSP认证标识", required = false)
  public Boolean getOsPVerified() {
    return osPVerified;
  }

  public void setOsPVerified(Boolean osPVerified) {
    this.osPVerified = osPVerified;
  }

  public CurrentUserVO xverified(Boolean xverified) {
    this.xverified = xverified;
    return this;
  }

  /**
   * X加V认证标识
   * @return xverified
  */
  
  @Schema(name = "XVerified", description = "X加V认证标识", required = false)
  public Boolean getXverified() {
    return xverified;
  }

  public void setXverified(Boolean xverified) {
    this.xverified = xverified;
  }

  public CurrentUserVO xverifiedType(String xverifiedType) {
    this.xverifiedType = xverifiedType;
    return this;
  }

  /**
   * X加V认证类型
   * @return xverifiedType
  */
  
  @Schema(name = "XVerifiedType", description = "X加V认证类型", required = false)
  public String getXverifiedType() {
    return xverifiedType;
  }

  public void setXverifiedType(String xverifiedType) {
    this.xverifiedType = xverifiedType;
  }

  public CurrentUserVO ospAvatar(String ospAvatar) {
    this.ospAvatar = ospAvatar;
    return this;
  }

  /**
   * OSP头像
   * @return ospAvatar
  */
  
  @Schema(name = "ospAvatar", description = "OSP头像", required = false)
  public String getOspAvatar() {
    return ospAvatar;
  }

  public void setOspAvatar(String ospAvatar) {
    this.ospAvatar = ospAvatar;
  }

  public CurrentUserVO experienceVoucherAvailable(BigDecimal experienceVoucherAvailable) {
    this.experienceVoucherAvailable = experienceVoucherAvailable;
    return this;
  }

  /**
   * 用户抽奖体验券
   * @return experienceVoucherAvailable
  */
  @Valid 
  @Schema(name = "experienceVoucherAvailable", description = "用户抽奖体验券", required = false)
  public BigDecimal getExperienceVoucherAvailable() {
    return experienceVoucherAvailable;
  }

  public void setExperienceVoucherAvailable(BigDecimal experienceVoucherAvailable) {
    this.experienceVoucherAvailable = experienceVoucherAvailable;
  }

  public CurrentUserVO seasonPointAvailable(BigDecimal seasonPointAvailable) {
    this.seasonPointAvailable = seasonPointAvailable;
    return this;
  }

  /**
   * 用户本赛季的积分
   * @return seasonPointAvailable
  */
  @Valid 
  @Schema(name = "seasonPointAvailable", description = "用户本赛季的积分", required = false)
  public BigDecimal getSeasonPointAvailable() {
    return seasonPointAvailable;
  }

  public void setSeasonPointAvailable(BigDecimal seasonPointAvailable) {
    this.seasonPointAvailable = seasonPointAvailable;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CurrentUserVO currentUserVO = (CurrentUserVO) o;
    return Objects.equals(this.uid, currentUserVO.uid) &&
        Objects.equals(this.saasId, currentUserVO.saasId) &&
        Objects.equals(this.twitterName, currentUserVO.twitterName) &&
        Objects.equals(this.discordName, currentUserVO.discordName) &&
        Objects.equals(this.invitePoint, currentUserVO.invitePoint) &&
        Objects.equals(this.dividePoint, currentUserVO.dividePoint) &&
        Objects.equals(this.available, currentUserVO.available) &&
        Objects.equals(this.pfp, currentUserVO.pfp) &&
        Objects.equals(this.vip, currentUserVO.vip) &&
        Objects.equals(this.isVip, currentUserVO.isVip) &&
        Objects.equals(this.rankValue, currentUserVO.rankValue) &&
        Objects.equals(this.rankPercent, currentUserVO.rankPercent) &&
        Objects.equals(this.expValue, currentUserVO.expValue) &&
        Objects.equals(this.ticketAvailable, currentUserVO.ticketAvailable) &&
        Objects.equals(this.ticketUsed, currentUserVO.ticketUsed) &&
        Objects.equals(this.voucherAvailable, currentUserVO.voucherAvailable) &&
        Objects.equals(this.extraBoostValue, currentUserVO.extraBoostValue) &&
        Objects.equals(this.boostValue, currentUserVO.boostValue) &&
        Objects.equals(this.ojoValue, currentUserVO.ojoValue) &&
        Objects.equals(this.credentialsAvailable, currentUserVO.credentialsAvailable) &&
        Objects.equals(this.osPVerified, currentUserVO.osPVerified) &&
        Objects.equals(this.xverified, currentUserVO.xverified) &&
        Objects.equals(this.xverifiedType, currentUserVO.xverifiedType) &&
        Objects.equals(this.ospAvatar, currentUserVO.ospAvatar) &&
        Objects.equals(this.experienceVoucherAvailable, currentUserVO.experienceVoucherAvailable) &&
        Objects.equals(this.seasonPointAvailable, currentUserVO.seasonPointAvailable);
  }

  @Override
  public int hashCode() {
    return Objects.hash(uid, saasId, twitterName, discordName, invitePoint, dividePoint, available, pfp, vip, isVip, rankValue, rankPercent, expValue, ticketAvailable, ticketUsed, voucherAvailable, extraBoostValue, boostValue, ojoValue, credentialsAvailable, osPVerified, xverified, xverifiedType, ospAvatar, experienceVoucherAvailable, seasonPointAvailable);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CurrentUserVO {\n");
    sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
    sb.append("    saasId: ").append(toIndentedString(saasId)).append("\n");
    sb.append("    twitterName: ").append(toIndentedString(twitterName)).append("\n");
    sb.append("    discordName: ").append(toIndentedString(discordName)).append("\n");
    sb.append("    invitePoint: ").append(toIndentedString(invitePoint)).append("\n");
    sb.append("    dividePoint: ").append(toIndentedString(dividePoint)).append("\n");
    sb.append("    available: ").append(toIndentedString(available)).append("\n");
    sb.append("    pfp: ").append(toIndentedString(pfp)).append("\n");
    sb.append("    vip: ").append(toIndentedString(vip)).append("\n");
    sb.append("    isVip: ").append(toIndentedString(isVip)).append("\n");
    sb.append("    rankValue: ").append(toIndentedString(rankValue)).append("\n");
    sb.append("    rankPercent: ").append(toIndentedString(rankPercent)).append("\n");
    sb.append("    expValue: ").append(toIndentedString(expValue)).append("\n");
    sb.append("    ticketAvailable: ").append(toIndentedString(ticketAvailable)).append("\n");
    sb.append("    ticketUsed: ").append(toIndentedString(ticketUsed)).append("\n");
    sb.append("    voucherAvailable: ").append(toIndentedString(voucherAvailable)).append("\n");
    sb.append("    extraBoostValue: ").append(toIndentedString(extraBoostValue)).append("\n");
    sb.append("    boostValue: ").append(toIndentedString(boostValue)).append("\n");
    sb.append("    ojoValue: ").append(toIndentedString(ojoValue)).append("\n");
    sb.append("    credentialsAvailable: ").append(toIndentedString(credentialsAvailable)).append("\n");
    sb.append("    osPVerified: ").append(toIndentedString(osPVerified)).append("\n");
    sb.append("    xverified: ").append(toIndentedString(xverified)).append("\n");
    sb.append("    xverifiedType: ").append(toIndentedString(xverifiedType)).append("\n");
    sb.append("    ospAvatar: ").append(toIndentedString(ospAvatar)).append("\n");
    sb.append("    experienceVoucherAvailable: ").append(toIndentedString(experienceVoucherAvailable)).append("\n");
    sb.append("    seasonPointAvailable: ").append(toIndentedString(seasonPointAvailable)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

