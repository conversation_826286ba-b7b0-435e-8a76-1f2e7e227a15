package com.kikitrade.customer.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * SendVerifyCodeRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class SendVerifyCodeRequest {

  @JsonProperty("channel")
  private String channel;

  @JsonProperty("receiver")
  private String receiver;

   @JsonProperty("customerName")
  private String customerName;

  public SendVerifyCodeRequest channel(String channel) {
    this.channel = channel;
    return this;
  }

  /**
   * 验证码类型，目前只支持 email
   * @return channel
  */
  @NotNull 
  @Schema(name = "channel", description = "验证码类型，目前只支持 email", required = true)
  public String getChannel() {
    return channel;
  }

  public void setChannel(String channel) {
    this.channel = channel;
  }

  public SendVerifyCodeRequest receiver(String receiver) {
    this.receiver = receiver;
    return this;
  }

  /**
   * 接收者
   * @return receiver
  */
  @NotNull 
  @Schema(name = "receiver", description = "接收者", required = true)
  public String getReceiver() {
    return receiver;
  }

  public void setReceiver(String receiver) {
    this.receiver = receiver;
  }

  public SendVerifyCodeRequest customerName(String customerName) {
    this.customerName = customerName;
    return this;
  }

  /**
   * 用户名
   * @return customerName
  */
  
  @Schema(name = "customerName", description = "用户名", required = false)
  public String getCustomerName() {
    return customerName;
  }

  public void setCustomerName(String customerName) {
    this.customerName = customerName;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SendVerifyCodeRequest sendVerifyCodeRequest = (SendVerifyCodeRequest) o;
    return Objects.equals(this.channel, sendVerifyCodeRequest.channel) &&
        Objects.equals(this.receiver, sendVerifyCodeRequest.receiver) &&
        Objects.equals(this.customerName, sendVerifyCodeRequest.customerName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(channel, receiver, customerName);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SendVerifyCodeRequest {\n");
    sb.append("    channel: ").append(toIndentedString(channel)).append("\n");
    sb.append("    receiver: ").append(toIndentedString(receiver)).append("\n");
    sb.append("    customerName: ").append(toIndentedString(customerName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

