/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.0.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.kikitrade.customer.generated.api.s2;

import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.customer.generated.model.s2.WebResultCurrentUserVO;
import com.kikitrade.customer.generated.model.s2.WebResultSocialTokenVO;
import com.kikitrade.customer.generated.model.s2.WebResultTwitterVO;
import com.kikitrade.customer.generated.model.s2.WebResultTwittersVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "CustomerS2", description = "用户相关接口描述")
public interface CustomerS2Api {

    default CustomerS2ApiDelegate getDelegate() {
        return new CustomerS2ApiDelegate() {};
    }

    /**
     * POST /customer/oauth/social/token : get twitter/discord token
     * 三方认证登录
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cid 用户id (required)
     * @param platform twitter、discord (required)
     * @param code code(一次性) (required)
     * @param redirectUri redirectUri (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "authToken",
        summary = "get twitter/discord token",
        tags = { "CustomerS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultSocialTokenVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customer/oauth/social/token",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultSocialTokenVO> authToken(
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "cid", description = "用户id", required = true) @Valid @RequestParam(value = "cid", required = true) String cid,
        @NotNull @Parameter(name = "platform", description = "twitter、discord", required = true) @Valid @RequestParam(value = "platform", required = true) String platform,
        @NotNull @Parameter(name = "code", description = "code(一次性)", required = true) @Valid @RequestParam(value = "code", required = true) String code,
        @Parameter(name = "redirectUri", description = "redirectUri") @Valid @RequestParam(value = "redirectUri", required = false) String redirectUri
    ) {
        return getDelegate().authToken(authorization, saasId, cid, platform, code, redirectUri);
    }


    /**
     * POST /customer/event : 第三方用户注册或绑定邀请关系
     * 第三方用户注册或绑定邀请关系
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param name  (required)
     * @param customerId  (required)
     * @param body  (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "customerEvent",
        summary = "第三方用户注册或绑定邀请关系",
        tags = { "CustomerS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResult.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/customer/event",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResult> customerEvent(
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "name", description = "", required = true) @Valid @RequestParam(value = "name", required = true) String name,
        @NotNull @Parameter(name = "customerId", description = "", required = true) @Valid @RequestParam(value = "customerId", required = true) String customerId,
        @Parameter(name = "body", description = "") @Valid @RequestParam(value = "body", required = false) String body
    ) {
        return getDelegate().customerEvent(authorization, saasId, name, customerId, body);
    }


    /**
     * GET /customer/{cid} : get quests customer info
     * 获取 quests 平台用户信息
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cid 业务方用户 id (required)
     * @param businessType 默认返回 twitterName, 支持 asset 等 (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "customerInfo",
        summary = "get quests customer info",
        tags = { "CustomerS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultCurrentUserVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/customer/{cid}",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultCurrentUserVO> customerInfo(
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "cid", description = "业务方用户 id", required = true) @PathVariable("cid") String cid,
        @Parameter(name = "businessType", description = "默认返回 twitterName, 支持 asset 等") @Valid @RequestParam(value = "businessType", required = false) String businessType
    ) {
        return getDelegate().customerInfo(authorization, saasId, cid, businessType);
    }


    /**
     * GET /customer/twitter/{cid} : get twitter info of user
     * 获取用户的 twitter 信息
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cid 业务方用户 id (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "twitterInfo",
        summary = "get twitter info of user",
        tags = { "CustomerS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultTwitterVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/customer/twitter/{cid}",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultTwitterVO> twitterInfo(
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "cid", description = "业务方用户 id", required = true) @PathVariable("cid") String cid
    ) {
        return getDelegate().twitterInfo(authorization, saasId, cid);
    }


    /**
     * GET /customer/twitters/{cid} : get twitter infos of user by all saasIds
     * 获取用户的 twitter 信息，包含所有 saasId 平台
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cid 业务方用户 id (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "twitterInfos",
        summary = "get twitter infos of user by all saasIds",
        tags = { "CustomerS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultTwittersVO.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/customer/twitters/{cid}",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultTwittersVO> twitterInfos(
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "cid", description = "业务方用户 id", required = true) @PathVariable("cid") String cid
    ) {
        return getDelegate().twitterInfos(authorization, saasId, cid);
    }

}
