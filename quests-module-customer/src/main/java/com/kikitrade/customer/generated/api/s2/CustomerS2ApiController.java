package com.kikitrade.customer.generated.api.s2;

import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.customer.generated.model.s2.WebResultCurrentUserVO;
import com.kikitrade.customer.generated.model.s2.WebResultSocialTokenVO;
import com.kikitrade.customer.generated.model.s2.WebResultTwitterVO;
import com.kikitrade.customer.generated.model.s2.WebResultTwittersVO;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.*;
import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Controller
@RequestMapping("${openapi.kweb.base-path:/s2}")
public class CustomerS2ApiController implements CustomerS2Api {

    private final CustomerS2ApiDelegate delegate;

    public CustomerS2ApiController(@Autowired(required = false) CustomerS2ApiDelegate delegate) {
        this.delegate = Optional.ofNullable(delegate).orElse(new CustomerS2ApiDelegate() {});
    }

    @Override
    public CustomerS2ApiDelegate getDelegate() {
        return delegate;
    }

}
