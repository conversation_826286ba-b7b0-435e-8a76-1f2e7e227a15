package com.kikitrade.customer.generated.model.s2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * CurrentUserVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class CurrentUserVO {

  @JsonProperty("uid")
  private String uid;

  @JsonProperty("saasId")
  private String saasId;

  @JsonProperty("twitterName")
  private String twitterName;

  @JsonProperty("discordName")
  private String discordName;

  @JsonProperty("invitePoint")
  private BigDecimal invitePoint;

  @JsonProperty("dividePoint")
  private BigDecimal dividePoint;

  @JsonProperty("available")
  private BigDecimal available;

  @JsonProperty("pfp")
  private String pfp;

  @JsonProperty("vip")
  private String vip;

  @JsonProperty("isVip")
  private Boolean isVip;

  @JsonProperty("rankValue")
  private Integer rankValue;

  @JsonProperty("rankPercent")
  private String rankPercent;

  @JsonProperty("expValue")
  private BigDecimal expValue;

  public CurrentUserVO uid(String uid) {
    this.uid = uid;
    return this;
  }

  /**
   * Get uid
   * @return uid
  */
  
  @Schema(name = "uid", required = false)
  public String getUid() {
    return uid;
  }

  public void setUid(String uid) {
    this.uid = uid;
  }

  public CurrentUserVO saasId(String saasId) {
    this.saasId = saasId;
    return this;
  }

  /**
   * Get saasId
   * @return saasId
  */
  
  @Schema(name = "saasId", required = false)
  public String getSaasId() {
    return saasId;
  }

  public void setSaasId(String saasId) {
    this.saasId = saasId;
  }

  public CurrentUserVO twitterName(String twitterName) {
    this.twitterName = twitterName;
    return this;
  }

  /**
   * Get twitterName
   * @return twitterName
  */
  
  @Schema(name = "twitterName", required = false)
  public String getTwitterName() {
    return twitterName;
  }

  public void setTwitterName(String twitterName) {
    this.twitterName = twitterName;
  }

  public CurrentUserVO discordName(String discordName) {
    this.discordName = discordName;
    return this;
  }

  /**
   * Get discordName
   * @return discordName
  */
  
  @Schema(name = "discordName", required = false)
  public String getDiscordName() {
    return discordName;
  }

  public void setDiscordName(String discordName) {
    this.discordName = discordName;
  }

  public CurrentUserVO invitePoint(BigDecimal invitePoint) {
    this.invitePoint = invitePoint;
    return this;
  }

  /**
   * Get invitePoint
   * @return invitePoint
  */
  @Valid 
  @Schema(name = "invitePoint", required = false)
  public BigDecimal getInvitePoint() {
    return invitePoint;
  }

  public void setInvitePoint(BigDecimal invitePoint) {
    this.invitePoint = invitePoint;
  }

  public CurrentUserVO dividePoint(BigDecimal dividePoint) {
    this.dividePoint = dividePoint;
    return this;
  }

  /**
   * Get dividePoint
   * @return dividePoint
  */
  @Valid 
  @Schema(name = "dividePoint", required = false)
  public BigDecimal getDividePoint() {
    return dividePoint;
  }

  public void setDividePoint(BigDecimal dividePoint) {
    this.dividePoint = dividePoint;
  }

  public CurrentUserVO available(BigDecimal available) {
    this.available = available;
    return this;
  }

  /**
   * 用户加成后的积分
   * @return available
  */
  @Valid 
  @Schema(name = "available", description = "用户加成后的积分", required = false)
  public BigDecimal getAvailable() {
    return available;
  }

  public void setAvailable(BigDecimal available) {
    this.available = available;
  }

  public CurrentUserVO pfp(String pfp) {
    this.pfp = pfp;
    return this;
  }

  /**
   * pfp 加成
   * @return pfp
  */
  
  @Schema(name = "pfp", example = "100%", description = "pfp 加成", required = false)
  public String getPfp() {
    return pfp;
  }

  public void setPfp(String pfp) {
    this.pfp = pfp;
  }

  public CurrentUserVO vip(String vip) {
    this.vip = vip;
    return this;
  }

  /**
   * vip 加成
   * @return vip
  */
  
  @Schema(name = "vip", example = "100%", description = "vip 加成", required = false)
  public String getVip() {
    return vip;
  }

  public void setVip(String vip) {
    this.vip = vip;
  }

  public CurrentUserVO isVip(Boolean isVip) {
    this.isVip = isVip;
    return this;
  }

  /**
   * 是否为 vip 用户
   * @return isVip
  */
  
  @Schema(name = "isVip", description = "是否为 vip 用户", required = false)
  public Boolean getIsVip() {
    return isVip;
  }

  public void setIsVip(Boolean isVip) {
    this.isVip = isVip;
  }

  public CurrentUserVO rankValue(Integer rankValue) {
    this.rankValue = rankValue;
    return this;
  }

  /**
   * Get rankValue
   * @return rankValue
  */
  
  @Schema(name = "rankValue", required = false)
  public Integer getRankValue() {
    return rankValue;
  }

  public void setRankValue(Integer rankValue) {
    this.rankValue = rankValue;
  }

  public CurrentUserVO rankPercent(String rankPercent) {
    this.rankPercent = rankPercent;
    return this;
  }

  /**
   * Get rankPercent
   * @return rankPercent
  */
  
  @Schema(name = "rankPercent", required = false)
  public String getRankPercent() {
    return rankPercent;
  }

  public void setRankPercent(String rankPercent) {
    this.rankPercent = rankPercent;
  }

  public CurrentUserVO expValue(BigDecimal expValue) {
    this.expValue = expValue;
    return this;
  }

  /**
   * Get expValue
   * @return expValue
  */
  @Valid 
  @Schema(name = "expValue", required = false)
  public BigDecimal getExpValue() {
    return expValue;
  }

  public void setExpValue(BigDecimal expValue) {
    this.expValue = expValue;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CurrentUserVO currentUserVO = (CurrentUserVO) o;
    return Objects.equals(this.uid, currentUserVO.uid) &&
        Objects.equals(this.saasId, currentUserVO.saasId) &&
        Objects.equals(this.twitterName, currentUserVO.twitterName) &&
        Objects.equals(this.discordName, currentUserVO.discordName) &&
        Objects.equals(this.invitePoint, currentUserVO.invitePoint) &&
        Objects.equals(this.dividePoint, currentUserVO.dividePoint) &&
        Objects.equals(this.available, currentUserVO.available) &&
        Objects.equals(this.pfp, currentUserVO.pfp) &&
        Objects.equals(this.vip, currentUserVO.vip) &&
        Objects.equals(this.isVip, currentUserVO.isVip) &&
        Objects.equals(this.rankValue, currentUserVO.rankValue) &&
        Objects.equals(this.rankPercent, currentUserVO.rankPercent) &&
        Objects.equals(this.expValue, currentUserVO.expValue);
  }

  @Override
  public int hashCode() {
    return Objects.hash(uid, saasId, twitterName, discordName, invitePoint, dividePoint, available, pfp, vip, isVip, rankValue, rankPercent, expValue);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CurrentUserVO {\n");
    sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
    sb.append("    saasId: ").append(toIndentedString(saasId)).append("\n");
    sb.append("    twitterName: ").append(toIndentedString(twitterName)).append("\n");
    sb.append("    discordName: ").append(toIndentedString(discordName)).append("\n");
    sb.append("    invitePoint: ").append(toIndentedString(invitePoint)).append("\n");
    sb.append("    dividePoint: ").append(toIndentedString(dividePoint)).append("\n");
    sb.append("    available: ").append(toIndentedString(available)).append("\n");
    sb.append("    pfp: ").append(toIndentedString(pfp)).append("\n");
    sb.append("    vip: ").append(toIndentedString(vip)).append("\n");
    sb.append("    isVip: ").append(toIndentedString(isVip)).append("\n");
    sb.append("    rankValue: ").append(toIndentedString(rankValue)).append("\n");
    sb.append("    rankPercent: ").append(toIndentedString(rankPercent)).append("\n");
    sb.append("    expValue: ").append(toIndentedString(expValue)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

