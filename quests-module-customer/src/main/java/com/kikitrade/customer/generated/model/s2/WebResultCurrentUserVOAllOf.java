package com.kikitrade.customer.generated.model.s2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.kikitrade.customer.generated.model.s2.CurrentUserVO;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * WebResultCurrentUserVOAllOf
 */

@JsonTypeName("WebResultCurrentUserVO_allOf")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class WebResultCurrentUserVOAllOf {

  @JsonProperty("obj")
  private CurrentUserVO obj;

  public WebResultCurrentUserVOAllOf obj(CurrentUserVO obj) {
    this.obj = obj;
    return this;
  }

  /**
   * Get obj
   * @return obj
  */
  @Valid 
  @Schema(name = "obj", required = false)
  public CurrentUserVO getObj() {
    return obj;
  }

  public void setObj(CurrentUserVO obj) {
    this.obj = obj;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WebResultCurrentUserVOAllOf webResultCurrentUserVOAllOf = (WebResultCurrentUserVOAllOf) o;
    return Objects.equals(this.obj, webResultCurrentUserVOAllOf.obj);
  }

  @Override
  public int hashCode() {
    return Objects.hash(obj);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WebResultCurrentUserVOAllOf {\n");
    sb.append("    obj: ").append(toIndentedString(obj)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

