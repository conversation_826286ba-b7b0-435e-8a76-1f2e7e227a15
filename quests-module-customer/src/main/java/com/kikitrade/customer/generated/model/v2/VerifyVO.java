package com.kikitrade.customer.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * VerifyVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class VerifyVO {

  @JsonProperty("OSPVerified")
  private Boolean osPVerified = false;

  @JsonProperty("XVerified")
  private Boolean xverified = false;

  public VerifyVO osPVerified(Boolean osPVerified) {
    this.osPVerified = osPVerified;
    return this;
  }

  /**
   * Get osPVerified
   * @return osPVerified
  */
  
  @Schema(name = "OSPVerified", required = false)
  public Boolean getOsPVerified() {
    return osPVerified;
  }

  public void setOsPVerified(Boolean osPVerified) {
    this.osPVerified = osPVerified;
  }

  public VerifyVO xverified(Boolean xverified) {
    this.xverified = xverified;
    return this;
  }

  /**
   * Get xverified
   * @return xverified
  */
  
  @Schema(name = "XVerified", required = false)
  public Boolean getXverified() {
    return xverified;
  }

  public void setXverified(Boolean xverified) {
    this.xverified = xverified;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    VerifyVO verifyVO = (VerifyVO) o;
    return Objects.equals(this.osPVerified, verifyVO.osPVerified) &&
        Objects.equals(this.xverified, verifyVO.xverified);
  }

  @Override
  public int hashCode() {
    return Objects.hash(osPVerified, xverified);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class VerifyVO {\n");
    sb.append("    osPVerified: ").append(toIndentedString(osPVerified)).append("\n");
    sb.append("    xverified: ").append(toIndentedString(xverified)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

