package com.kikitrade.customer.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * WebResultOspVerifyVOAllOf
 */

@JsonTypeName("WebResultOspVerifyVO_allOf")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class WebResultOspVerifyVOAllOf {

  @JsonProperty("verifyResult")
  private Boolean verifyResult;

  public WebResultOspVerifyVOAllOf verifyResult(Boolean verifyResult) {
    this.verifyResult = verifyResult;
    return this;
  }

  /**
   * Get verifyResult
   * @return verifyResult
  */
  
  @Schema(name = "verifyResult", required = false)
  public Boolean getVerifyResult() {
    return verifyResult;
  }

  public void setVerifyResult(Boolean verifyResult) {
    this.verifyResult = verifyResult;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WebResultOspVerifyVOAllOf webResultOspVerifyVOAllOf = (WebResultOspVerifyVOAllOf) o;
    return Objects.equals(this.verifyResult, webResultOspVerifyVOAllOf.verifyResult);
  }

  @Override
  public int hashCode() {
    return Objects.hash(verifyResult);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WebResultOspVerifyVOAllOf {\n");
    sb.append("    verifyResult: ").append(toIndentedString(verifyResult)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

