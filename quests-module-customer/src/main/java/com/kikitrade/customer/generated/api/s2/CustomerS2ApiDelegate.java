package com.kikitrade.customer.generated.api.s2;

import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.customer.generated.model.s2.WebResultCurrentUserVO;
import com.kikitrade.customer.generated.model.s2.WebResultSocialTokenVO;
import com.kikitrade.customer.generated.model.s2.WebResultTwitterVO;
import com.kikitrade.customer.generated.model.s2.WebResultTwittersVO;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link CustomerS2ApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface CustomerS2ApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * POST /customer/oauth/social/token : get twitter/discord token
     * 三方认证登录
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cid 用户id (required)
     * @param platform twitter、discord (required)
     * @param code code(一次性) (required)
     * @param redirectUri redirectUri (optional)
     * @return Successful operation (status code 200)
     * @see CustomerS2Api#authToken
     */
    default ResponseEntity<WebResultSocialTokenVO> authToken(String authorization,
        String saasId,
        String cid,
        String platform,
        String code,
        String redirectUri) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /customer/event : 第三方用户注册或绑定邀请关系
     * 第三方用户注册或绑定邀请关系
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param name  (required)
     * @param customerId  (required)
     * @param body  (optional)
     * @return Successful operation (status code 200)
     * @see CustomerS2Api#customerEvent
     */
    default ResponseEntity<WebResult> customerEvent(String authorization,
        String saasId,
        String name,
        String customerId,
        String body) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /customer/{cid} : get quests customer info
     * 获取 quests 平台用户信息
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cid 业务方用户 id (required)
     * @param businessType 默认返回 twitterName, 支持 asset 等 (optional)
     * @return Successful operation (status code 200)
     * @see CustomerS2Api#customerInfo
     */
    default ResponseEntity<WebResultCurrentUserVO> customerInfo(String authorization,
        String saasId,
        String cid,
        String businessType) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /customer/twitter/{cid} : get twitter info of user
     * 获取用户的 twitter 信息
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cid 业务方用户 id (required)
     * @return Successful operation (status code 200)
     * @see CustomerS2Api#twitterInfo
     */
    default ResponseEntity<WebResultTwitterVO> twitterInfo(String authorization,
        String saasId,
        String cid) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /customer/twitters/{cid} : get twitter infos of user by all saasIds
     * 获取用户的 twitter 信息，包含所有 saasId 平台
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cid 业务方用户 id (required)
     * @return Successful operation (status code 200)
     * @see CustomerS2Api#twitterInfos
     */
    default ResponseEntity<WebResultTwittersVO> twitterInfos(String authorization,
        String saasId,
        String cid) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
