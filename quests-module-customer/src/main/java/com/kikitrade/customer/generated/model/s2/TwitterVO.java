package com.kikitrade.customer.generated.model.s2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TwitterVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TwitterVO {

  @JsonProperty("id")
  private String id;

  @JsonProperty("handleName")
  private String handleName;

  @JsonProperty("created")
  private Long created;

  public TwitterVO id(String id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  
  @Schema(name = "id", required = false)
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public TwitterVO handleName(String handleName) {
    this.handleName = handleName;
    return this;
  }

  /**
   * Get handleName
   * @return handleName
  */
  
  @Schema(name = "handleName", required = false)
  public String getHandleName() {
    return handleName;
  }

  public void setHandleName(String handleName) {
    this.handleName = handleName;
  }

  public TwitterVO created(Long created) {
    this.created = created;
    return this;
  }

  /**
   * 单位是秒
   * @return created
  */
  
  @Schema(name = "created", description = "单位是秒", required = false)
  public Long getCreated() {
    return created;
  }

  public void setCreated(Long created) {
    this.created = created;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TwitterVO twitterVO = (TwitterVO) o;
    return Objects.equals(this.id, twitterVO.id) &&
        Objects.equals(this.handleName, twitterVO.handleName) &&
        Objects.equals(this.created, twitterVO.created);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, handleName, created);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TwitterVO {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    handleName: ").append(toIndentedString(handleName)).append("\n");
    sb.append("    created: ").append(toIndentedString(created)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

