package com.kikitrade.customer.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.customer.generated.model.v2.WalletAddressVO;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * CustomerVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class CustomerVO {

  @JsonProperty("id")
  private String id;

  @JsonProperty("email")
  private String email;

  @JsonProperty("userName")
  private String userName;

  @JsonProperty("status")
  private Integer status;

  @JsonProperty("idCertifiedStatus")
  private Integer idCertifiedStatus;

  @JsonProperty("isEmailCertified")
  private Boolean isEmailCertified;

  @JsonProperty("isBindEmail")
  private Boolean isBindEmail;

  @JsonProperty("isPhoneCertified")
  private Boolean isPhoneCertified;

  @JsonProperty("isBindPhone")
  private Boolean isBindPhone;

  @JsonProperty("isGoogleCertified")
  private Boolean isGoogleCertified;

  @JsonProperty("isBindGoogle")
  private Boolean isBindGoogle;

  @JsonProperty("isBindWeb3")
  private Boolean isBindWeb3;

  @JsonProperty("locale")
  private String locale;

  @JsonProperty("phone")
  private String phone;

  @JsonProperty("registerType")
  private Integer registerType;

  @JsonProperty("isMigration")
  private Integer isMigration;

  @JsonProperty("noPassword")
  private Boolean noPassword;

  @JsonProperty("isExistAssetPassword")
  private Boolean isExistAssetPassword;

  @JsonProperty("region")
  private String region;

  @JsonProperty("kycLevel")
  private String kycLevel;

  @JsonProperty("vipLevel")
  private String vipLevel;

  @JsonProperty("vipEndTime")
  private Long vipEndTime;

  @JsonProperty("merchantId")
  private String merchantId;

  @JsonProperty("merchantName")
  private String merchantName;

  @JsonProperty("whatsAppId")
  private String whatsAppId;

  @JsonProperty("telegramId")
  private String telegramId;

  @JsonProperty("lineId")
  private String lineId;

  @JsonProperty("middleName")
  private String middleName;

  @JsonProperty("avatar")
  private String avatar;

  @JsonProperty("gender")
  private Integer gender;

  @JsonProperty("nickName")
  private String nickName;

  @JsonProperty("uniqueName")
  private String uniqueName;

  @JsonProperty("jwtKey")
  private String jwtKey;

  @JsonProperty("jwtKeyExpireIn")
  private Long jwtKeyExpireIn;

  @JsonProperty("wsKey")
  private String wsKey;

  @JsonProperty("refreshToken")
  private String refreshToken;

  @JsonProperty("refreshTokenExpireIn")
  private Long refreshTokenExpireIn;

  @JsonProperty("publicKey")
  private String publicKey;

  @JsonProperty("walletAddress")
  private String walletAddress;

  @JsonProperty("address")
  private WalletAddressVO address;

  public CustomerVO id(String id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  
  @Schema(name = "id", required = false)
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public CustomerVO email(String email) {
    this.email = email;
    return this;
  }

  /**
   * 邮箱
   * @return email
  */
  
  @Schema(name = "email", description = "邮箱", required = false)
  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public CustomerVO userName(String userName) {
    this.userName = userName;
    return this;
  }

  /**
   * 用户名
   * @return userName
  */
  
  @Schema(name = "userName", description = "用户名", required = false)
  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public CustomerVO status(Integer status) {
    this.status = status;
    return this;
  }

  /**
   * 用户状态
   * @return status
  */
  
  @Schema(name = "status", description = "用户状态", required = false)
  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public CustomerVO idCertifiedStatus(Integer idCertifiedStatus) {
    this.idCertifiedStatus = idCertifiedStatus;
    return this;
  }

  /**
   * kyc 身份验证状态，目前废弃，使用 kycAuthLevel 字段
   * @return idCertifiedStatus
  */
  
  @Schema(name = "idCertifiedStatus", description = "kyc 身份验证状态，目前废弃，使用 kycAuthLevel 字段", required = false)
  public Integer getIdCertifiedStatus() {
    return idCertifiedStatus;
  }

  public void setIdCertifiedStatus(Integer idCertifiedStatus) {
    this.idCertifiedStatus = idCertifiedStatus;
  }

  public CustomerVO isEmailCertified(Boolean isEmailCertified) {
    this.isEmailCertified = isEmailCertified;
    return this;
  }

  /**
   * 是否认证邮箱
   * @return isEmailCertified
  */
  
  @Schema(name = "isEmailCertified", description = "是否认证邮箱", required = false)
  public Boolean getIsEmailCertified() {
    return isEmailCertified;
  }

  public void setIsEmailCertified(Boolean isEmailCertified) {
    this.isEmailCertified = isEmailCertified;
  }

  public CustomerVO isBindEmail(Boolean isBindEmail) {
    this.isBindEmail = isBindEmail;
    return this;
  }

  /**
   * 是否绑定了邮箱
   * @return isBindEmail
  */
  
  @Schema(name = "isBindEmail", description = "是否绑定了邮箱", required = false)
  public Boolean getIsBindEmail() {
    return isBindEmail;
  }

  public void setIsBindEmail(Boolean isBindEmail) {
    this.isBindEmail = isBindEmail;
  }

  public CustomerVO isPhoneCertified(Boolean isPhoneCertified) {
    this.isPhoneCertified = isPhoneCertified;
    return this;
  }

  /**
   * 是否认证手机
   * @return isPhoneCertified
  */
  
  @Schema(name = "isPhoneCertified", description = "是否认证手机", required = false)
  public Boolean getIsPhoneCertified() {
    return isPhoneCertified;
  }

  public void setIsPhoneCertified(Boolean isPhoneCertified) {
    this.isPhoneCertified = isPhoneCertified;
  }

  public CustomerVO isBindPhone(Boolean isBindPhone) {
    this.isBindPhone = isBindPhone;
    return this;
  }

  /**
   * 是否绑定了手机号码
   * @return isBindPhone
  */
  
  @Schema(name = "isBindPhone", description = "是否绑定了手机号码", required = false)
  public Boolean getIsBindPhone() {
    return isBindPhone;
  }

  public void setIsBindPhone(Boolean isBindPhone) {
    this.isBindPhone = isBindPhone;
  }

  public CustomerVO isGoogleCertified(Boolean isGoogleCertified) {
    this.isGoogleCertified = isGoogleCertified;
    return this;
  }

  /**
   * 是否认证 Google
   * @return isGoogleCertified
  */
  
  @Schema(name = "isGoogleCertified", description = "是否认证 Google", required = false)
  public Boolean getIsGoogleCertified() {
    return isGoogleCertified;
  }

  public void setIsGoogleCertified(Boolean isGoogleCertified) {
    this.isGoogleCertified = isGoogleCertified;
  }

  public CustomerVO isBindGoogle(Boolean isBindGoogle) {
    this.isBindGoogle = isBindGoogle;
    return this;
  }

  /**
   * 是否绑定 Google
   * @return isBindGoogle
  */
  
  @Schema(name = "isBindGoogle", description = "是否绑定 Google", required = false)
  public Boolean getIsBindGoogle() {
    return isBindGoogle;
  }

  public void setIsBindGoogle(Boolean isBindGoogle) {
    this.isBindGoogle = isBindGoogle;
  }

  public CustomerVO isBindWeb3(Boolean isBindWeb3) {
    this.isBindWeb3 = isBindWeb3;
    return this;
  }

  /**
   * 是否绑定 web3
   * @return isBindWeb3
  */
  
  @Schema(name = "isBindWeb3", description = "是否绑定 web3", required = false)
  public Boolean getIsBindWeb3() {
    return isBindWeb3;
  }

  public void setIsBindWeb3(Boolean isBindWeb3) {
    this.isBindWeb3 = isBindWeb3;
  }

  public CustomerVO locale(String locale) {
    this.locale = locale;
    return this;
  }

  /**
   * Get locale
   * @return locale
  */
  
  @Schema(name = "locale", required = false)
  public String getLocale() {
    return locale;
  }

  public void setLocale(String locale) {
    this.locale = locale;
  }

  public CustomerVO phone(String phone) {
    this.phone = phone;
    return this;
  }

  /**
   * 手机号
   * @return phone
  */
  
  @Schema(name = "phone", description = "手机号", required = false)
  public String getPhone() {
    return phone;
  }

  public void setPhone(String phone) {
    this.phone = phone;
  }

  public CustomerVO registerType(Integer registerType) {
    this.registerType = registerType;
    return this;
  }

  /**
   * 注册类型
   * @return registerType
  */
  
  @Schema(name = "registerType", description = "注册类型", required = false)
  public Integer getRegisterType() {
    return registerType;
  }

  public void setRegisterType(Integer registerType) {
    this.registerType = registerType;
  }

  public CustomerVO isMigration(Integer isMigration) {
    this.isMigration = isMigration;
    return this;
  }

  /**
   * Get isMigration
   * @return isMigration
  */
  
  @Schema(name = "isMigration", required = false)
  public Integer getIsMigration() {
    return isMigration;
  }

  public void setIsMigration(Integer isMigration) {
    this.isMigration = isMigration;
  }

  public CustomerVO noPassword(Boolean noPassword) {
    this.noPassword = noPassword;
    return this;
  }

  /**
   * 是否存在密码
   * @return noPassword
  */
  
  @Schema(name = "noPassword", description = "是否存在密码", required = false)
  public Boolean getNoPassword() {
    return noPassword;
  }

  public void setNoPassword(Boolean noPassword) {
    this.noPassword = noPassword;
  }

  public CustomerVO isExistAssetPassword(Boolean isExistAssetPassword) {
    this.isExistAssetPassword = isExistAssetPassword;
    return this;
  }

  /**
   * 是否存在资金密码
   * @return isExistAssetPassword
  */
  
  @Schema(name = "isExistAssetPassword", description = "是否存在资金密码", required = false)
  public Boolean getIsExistAssetPassword() {
    return isExistAssetPassword;
  }

  public void setIsExistAssetPassword(Boolean isExistAssetPassword) {
    this.isExistAssetPassword = isExistAssetPassword;
  }

  public CustomerVO region(String region) {
    this.region = region;
    return this;
  }

  /**
   * 国家地区
   * @return region
  */
  
  @Schema(name = "region", description = "国家地区", required = false)
  public String getRegion() {
    return region;
  }

  public void setRegion(String region) {
    this.region = region;
  }

  public CustomerVO kycLevel(String kycLevel) {
    this.kycLevel = kycLevel;
    return this;
  }

  /**
   * kyc 等级
   * @return kycLevel
  */
  
  @Schema(name = "kycLevel", description = "kyc 等级", required = false)
  public String getKycLevel() {
    return kycLevel;
  }

  public void setKycLevel(String kycLevel) {
    this.kycLevel = kycLevel;
  }

  public CustomerVO vipLevel(String vipLevel) {
    this.vipLevel = vipLevel;
    return this;
  }

  /**
   * vip 等级
   * @return vipLevel
  */
  
  @Schema(name = "vipLevel", description = "vip 等级", required = false)
  public String getVipLevel() {
    return vipLevel;
  }

  public void setVipLevel(String vipLevel) {
    this.vipLevel = vipLevel;
  }

  public CustomerVO vipEndTime(Long vipEndTime) {
    this.vipEndTime = vipEndTime;
    return this;
  }

  /**
   * vip 到期时间
   * @return vipEndTime
  */
  
  @Schema(name = "vipEndTime", example = "1663836874000", description = "vip 到期时间", required = false)
  public Long getVipEndTime() {
    return vipEndTime;
  }

  public void setVipEndTime(Long vipEndTime) {
    this.vipEndTime = vipEndTime;
  }

  public CustomerVO merchantId(String merchantId) {
    this.merchantId = merchantId;
    return this;
  }

  /**
   * Get merchantId
   * @return merchantId
  */
  
  @Schema(name = "merchantId", required = false)
  public String getMerchantId() {
    return merchantId;
  }

  public void setMerchantId(String merchantId) {
    this.merchantId = merchantId;
  }

  public CustomerVO merchantName(String merchantName) {
    this.merchantName = merchantName;
    return this;
  }

  /**
   * Get merchantName
   * @return merchantName
  */
  
  @Schema(name = "merchantName", required = false)
  public String getMerchantName() {
    return merchantName;
  }

  public void setMerchantName(String merchantName) {
    this.merchantName = merchantName;
  }

  public CustomerVO whatsAppId(String whatsAppId) {
    this.whatsAppId = whatsAppId;
    return this;
  }

  /**
   * Get whatsAppId
   * @return whatsAppId
  */
  
  @Schema(name = "whatsAppId", required = false)
  public String getWhatsAppId() {
    return whatsAppId;
  }

  public void setWhatsAppId(String whatsAppId) {
    this.whatsAppId = whatsAppId;
  }

  public CustomerVO telegramId(String telegramId) {
    this.telegramId = telegramId;
    return this;
  }

  /**
   * Get telegramId
   * @return telegramId
  */
  
  @Schema(name = "telegramId", required = false)
  public String getTelegramId() {
    return telegramId;
  }

  public void setTelegramId(String telegramId) {
    this.telegramId = telegramId;
  }

  public CustomerVO lineId(String lineId) {
    this.lineId = lineId;
    return this;
  }

  /**
   * Get lineId
   * @return lineId
  */
  
  @Schema(name = "lineId", required = false)
  public String getLineId() {
    return lineId;
  }

  public void setLineId(String lineId) {
    this.lineId = lineId;
  }

  public CustomerVO middleName(String middleName) {
    this.middleName = middleName;
    return this;
  }

  /**
   * Get middleName
   * @return middleName
  */
  
  @Schema(name = "middleName", required = false)
  public String getMiddleName() {
    return middleName;
  }

  public void setMiddleName(String middleName) {
    this.middleName = middleName;
  }

  public CustomerVO avatar(String avatar) {
    this.avatar = avatar;
    return this;
  }

  /**
   * 头像
   * @return avatar
  */
  
  @Schema(name = "avatar", description = "头像", required = false)
  public String getAvatar() {
    return avatar;
  }

  public void setAvatar(String avatar) {
    this.avatar = avatar;
  }

  public CustomerVO gender(Integer gender) {
    this.gender = gender;
    return this;
  }

  /**
   * 性别
   * @return gender
  */
  
  @Schema(name = "gender", description = "性别", required = false)
  public Integer getGender() {
    return gender;
  }

  public void setGender(Integer gender) {
    this.gender = gender;
  }

  public CustomerVO nickName(String nickName) {
    this.nickName = nickName;
    return this;
  }

  /**
   * 昵称
   * @return nickName
  */
  
  @Schema(name = "nickName", description = "昵称", required = false)
  public String getNickName() {
    return nickName;
  }

  public void setNickName(String nickName) {
    this.nickName = nickName;
  }

  public CustomerVO uniqueName(String uniqueName) {
    this.uniqueName = uniqueName;
    return this;
  }

  /**
   * Get uniqueName
   * @return uniqueName
  */
  
  @Schema(name = "uniqueName", required = false)
  public String getUniqueName() {
    return uniqueName;
  }

  public void setUniqueName(String uniqueName) {
    this.uniqueName = uniqueName;
  }

  public CustomerVO jwtKey(String jwtKey) {
    this.jwtKey = jwtKey;
    return this;
  }

  /**
   * Get jwtKey
   * @return jwtKey
  */
  
  @Schema(name = "jwtKey", required = false)
  public String getJwtKey() {
    return jwtKey;
  }

  public void setJwtKey(String jwtKey) {
    this.jwtKey = jwtKey;
  }

  public CustomerVO jwtKeyExpireIn(Long jwtKeyExpireIn) {
    this.jwtKeyExpireIn = jwtKeyExpireIn;
    return this;
  }

  /**
   * jwtKey 过期时间
   * @return jwtKeyExpireIn
  */
  
  @Schema(name = "jwtKeyExpireIn", example = "1663836874000", description = "jwtKey 过期时间", required = false)
  public Long getJwtKeyExpireIn() {
    return jwtKeyExpireIn;
  }

  public void setJwtKeyExpireIn(Long jwtKeyExpireIn) {
    this.jwtKeyExpireIn = jwtKeyExpireIn;
  }

  public CustomerVO wsKey(String wsKey) {
    this.wsKey = wsKey;
    return this;
  }

  /**
   * Get wsKey
   * @return wsKey
  */
  
  @Schema(name = "wsKey", required = false)
  public String getWsKey() {
    return wsKey;
  }

  public void setWsKey(String wsKey) {
    this.wsKey = wsKey;
  }

  public CustomerVO refreshToken(String refreshToken) {
    this.refreshToken = refreshToken;
    return this;
  }

  /**
   * Get refreshToken
   * @return refreshToken
  */
  
  @Schema(name = "refreshToken", required = false)
  public String getRefreshToken() {
    return refreshToken;
  }

  public void setRefreshToken(String refreshToken) {
    this.refreshToken = refreshToken;
  }

  public CustomerVO refreshTokenExpireIn(Long refreshTokenExpireIn) {
    this.refreshTokenExpireIn = refreshTokenExpireIn;
    return this;
  }

  /**
   * refreshToken 过期时间
   * @return refreshTokenExpireIn
  */
  
  @Schema(name = "refreshTokenExpireIn", example = "1663836874000", description = "refreshToken 过期时间", required = false)
  public Long getRefreshTokenExpireIn() {
    return refreshTokenExpireIn;
  }

  public void setRefreshTokenExpireIn(Long refreshTokenExpireIn) {
    this.refreshTokenExpireIn = refreshTokenExpireIn;
  }

  public CustomerVO publicKey(String publicKey) {
    this.publicKey = publicKey;
    return this;
  }

  /**
   * 三方 public_key(web3auth)
   * @return publicKey
  */
  
  @Schema(name = "publicKey", description = "三方 public_key(web3auth)", required = false)
  public String getPublicKey() {
    return publicKey;
  }

  public void setPublicKey(String publicKey) {
    this.publicKey = publicKey;
  }

  public CustomerVO walletAddress(String walletAddress) {
    this.walletAddress = walletAddress;
    return this;
  }

  /**
   * 钱包账户地址
   * @return walletAddress
  */
  
  @Schema(name = "walletAddress", description = "钱包账户地址", required = false)
  public String getWalletAddress() {
    return walletAddress;
  }

  public void setWalletAddress(String walletAddress) {
    this.walletAddress = walletAddress;
  }

  public CustomerVO address(WalletAddressVO address) {
    this.address = address;
    return this;
  }

  /**
   * Get address
   * @return address
  */
  @Valid 
  @Schema(name = "address", required = false)
  public WalletAddressVO getAddress() {
    return address;
  }

  public void setAddress(WalletAddressVO address) {
    this.address = address;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomerVO customerVO = (CustomerVO) o;
    return Objects.equals(this.id, customerVO.id) &&
        Objects.equals(this.email, customerVO.email) &&
        Objects.equals(this.userName, customerVO.userName) &&
        Objects.equals(this.status, customerVO.status) &&
        Objects.equals(this.idCertifiedStatus, customerVO.idCertifiedStatus) &&
        Objects.equals(this.isEmailCertified, customerVO.isEmailCertified) &&
        Objects.equals(this.isBindEmail, customerVO.isBindEmail) &&
        Objects.equals(this.isPhoneCertified, customerVO.isPhoneCertified) &&
        Objects.equals(this.isBindPhone, customerVO.isBindPhone) &&
        Objects.equals(this.isGoogleCertified, customerVO.isGoogleCertified) &&
        Objects.equals(this.isBindGoogle, customerVO.isBindGoogle) &&
        Objects.equals(this.isBindWeb3, customerVO.isBindWeb3) &&
        Objects.equals(this.locale, customerVO.locale) &&
        Objects.equals(this.phone, customerVO.phone) &&
        Objects.equals(this.registerType, customerVO.registerType) &&
        Objects.equals(this.isMigration, customerVO.isMigration) &&
        Objects.equals(this.noPassword, customerVO.noPassword) &&
        Objects.equals(this.isExistAssetPassword, customerVO.isExistAssetPassword) &&
        Objects.equals(this.region, customerVO.region) &&
        Objects.equals(this.kycLevel, customerVO.kycLevel) &&
        Objects.equals(this.vipLevel, customerVO.vipLevel) &&
        Objects.equals(this.vipEndTime, customerVO.vipEndTime) &&
        Objects.equals(this.merchantId, customerVO.merchantId) &&
        Objects.equals(this.merchantName, customerVO.merchantName) &&
        Objects.equals(this.whatsAppId, customerVO.whatsAppId) &&
        Objects.equals(this.telegramId, customerVO.telegramId) &&
        Objects.equals(this.lineId, customerVO.lineId) &&
        Objects.equals(this.middleName, customerVO.middleName) &&
        Objects.equals(this.avatar, customerVO.avatar) &&
        Objects.equals(this.gender, customerVO.gender) &&
        Objects.equals(this.nickName, customerVO.nickName) &&
        Objects.equals(this.uniqueName, customerVO.uniqueName) &&
        Objects.equals(this.jwtKey, customerVO.jwtKey) &&
        Objects.equals(this.jwtKeyExpireIn, customerVO.jwtKeyExpireIn) &&
        Objects.equals(this.wsKey, customerVO.wsKey) &&
        Objects.equals(this.refreshToken, customerVO.refreshToken) &&
        Objects.equals(this.refreshTokenExpireIn, customerVO.refreshTokenExpireIn) &&
        Objects.equals(this.publicKey, customerVO.publicKey) &&
        Objects.equals(this.walletAddress, customerVO.walletAddress) &&
        Objects.equals(this.address, customerVO.address);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, email, userName, status, idCertifiedStatus, isEmailCertified, isBindEmail, isPhoneCertified, isBindPhone, isGoogleCertified, isBindGoogle, isBindWeb3, locale, phone, registerType, isMigration, noPassword, isExistAssetPassword, region, kycLevel, vipLevel, vipEndTime, merchantId, merchantName, whatsAppId, telegramId, lineId, middleName, avatar, gender, nickName, uniqueName, jwtKey, jwtKeyExpireIn, wsKey, refreshToken, refreshTokenExpireIn, publicKey, walletAddress, address);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomerVO {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    userName: ").append(toIndentedString(userName)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    idCertifiedStatus: ").append(toIndentedString(idCertifiedStatus)).append("\n");
    sb.append("    isEmailCertified: ").append(toIndentedString(isEmailCertified)).append("\n");
    sb.append("    isBindEmail: ").append(toIndentedString(isBindEmail)).append("\n");
    sb.append("    isPhoneCertified: ").append(toIndentedString(isPhoneCertified)).append("\n");
    sb.append("    isBindPhone: ").append(toIndentedString(isBindPhone)).append("\n");
    sb.append("    isGoogleCertified: ").append(toIndentedString(isGoogleCertified)).append("\n");
    sb.append("    isBindGoogle: ").append(toIndentedString(isBindGoogle)).append("\n");
    sb.append("    isBindWeb3: ").append(toIndentedString(isBindWeb3)).append("\n");
    sb.append("    locale: ").append(toIndentedString(locale)).append("\n");
    sb.append("    phone: ").append(toIndentedString(phone)).append("\n");
    sb.append("    registerType: ").append(toIndentedString(registerType)).append("\n");
    sb.append("    isMigration: ").append(toIndentedString(isMigration)).append("\n");
    sb.append("    noPassword: ").append(toIndentedString(noPassword)).append("\n");
    sb.append("    isExistAssetPassword: ").append(toIndentedString(isExistAssetPassword)).append("\n");
    sb.append("    region: ").append(toIndentedString(region)).append("\n");
    sb.append("    kycLevel: ").append(toIndentedString(kycLevel)).append("\n");
    sb.append("    vipLevel: ").append(toIndentedString(vipLevel)).append("\n");
    sb.append("    vipEndTime: ").append(toIndentedString(vipEndTime)).append("\n");
    sb.append("    merchantId: ").append(toIndentedString(merchantId)).append("\n");
    sb.append("    merchantName: ").append(toIndentedString(merchantName)).append("\n");
    sb.append("    whatsAppId: ").append(toIndentedString(whatsAppId)).append("\n");
    sb.append("    telegramId: ").append(toIndentedString(telegramId)).append("\n");
    sb.append("    lineId: ").append(toIndentedString(lineId)).append("\n");
    sb.append("    middleName: ").append(toIndentedString(middleName)).append("\n");
    sb.append("    avatar: ").append(toIndentedString(avatar)).append("\n");
    sb.append("    gender: ").append(toIndentedString(gender)).append("\n");
    sb.append("    nickName: ").append(toIndentedString(nickName)).append("\n");
    sb.append("    uniqueName: ").append(toIndentedString(uniqueName)).append("\n");
    sb.append("    jwtKey: ").append(toIndentedString(jwtKey)).append("\n");
    sb.append("    jwtKeyExpireIn: ").append(toIndentedString(jwtKeyExpireIn)).append("\n");
    sb.append("    wsKey: ").append(toIndentedString(wsKey)).append("\n");
    sb.append("    refreshToken: ").append(toIndentedString(refreshToken)).append("\n");
    sb.append("    refreshTokenExpireIn: ").append(toIndentedString(refreshTokenExpireIn)).append("\n");
    sb.append("    publicKey: ").append(toIndentedString(publicKey)).append("\n");
    sb.append("    walletAddress: ").append(toIndentedString(walletAddress)).append("\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

