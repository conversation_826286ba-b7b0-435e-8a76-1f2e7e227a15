openapi: 3.0.1
info:
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  description: The Kweb API v1.0
  title: Kweb API
  version: '1.0'
tags:
  - name: MemberS2
    description: 会员相关接口描述
servers:
  - description: Generated server url
    url: http://api.quests.dev.dipbit.xyz/s2
paths:
  /member/top/ladder:
    get:
      summary: 积分天梯top榜
      description: 积分天梯top榜
      operationId: topLadder
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultTopLadder'
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          required: true
        - schema:
            type: string
          in: query
          name: cid
          required: true
          description: 业务方用户id
        - schema:
            default: POINT
            type: string
          in: query
          name: assetType
          required: false
          description: 资产类型 默认 POINT
        - schema:
            default: History
            type: string
          in: query
          name: ladderType
          required: false
          description: 榜单类型 默认 History
        - schema:
            default: 100
            format: int32
            type: integer
          in: query
          name: limit
          required: false
        - schema:
            default: fold
            type: string
          in: query
          name: strategy
          required: false
          description: 排序策略，fold(默认)：相同分数排名相同， unfold：相同分数排名不同
      tags:
        - MemberS2
  /member/order/paid:
    get:
      summary: 订单完成支付
      description: 订单完成支付
      operationId: orderPaid
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResult'
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          required: true
        - schema:
            type: string
          in: query
          name: orderId
          required: true
          description: 订单id
      tags:
        - MemberS2
  /member/transferout:
    post:
      summary: /member/transferout
      description: 消耗积分
      operationId: transferOut
      tags:
        - MemberS2
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransferOutRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResult'
  /member/ladder:
    get:
      summary: 积分天梯排行榜
      description: 获取积分排行榜
      operationId: globalLadder
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultLadder'
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          required: true
        - schema:
            type: integer
            format: int32
          in: query
          name: level
          description: 徽章等级
          required: false
        - schema:
            default: 30
            format: int32
            type: integer
          in: query
          name: limit
          description: 排行榜数量,范围：[0,200]
          required: false
        - name: cid
          in: query
          description: 其他平台用户ID
          required: true
          schema:
            type: string
      tags:
        - MemberS2
  /member/ladder/history:
    get:
      summary: 积分历史排行榜
      description: 获取积分历史排行榜
      operationId: historyLadder
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebResultLadder'
      parameters:
        - schema:
            type: string
          in: header
          name: Authorization
          required: true
        - schema:
            type: string
          in: header
          name: saas_id
          required: true
        - name: cycle
          in: query
          description: 查询赛季周数（必传）
          required: true
          schema:
            type: string
        - schema:
            type: integer
            format: int32
          in: query
          name: level
          description: 徽章等级
          required: false
        - schema:
            default: 30
            format: int32
            type: integer
          in: query
          name: limit
          description: 排行榜数量,范围：[0,200]
          required: false
        - name: cid
          in: query
          description: 其他平台用户ID
          required: true
          schema:
            type: string
      tags:
        - MemberS2
components:
  schemas:
    WebResult:
      type: object
      discriminator:
        propertyName: code
    Date:
      type: object
    WebResultTopLadder:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/LadderVO'

    LadderVO:
      properties:
        myLadder:
          $ref: '#/components/schemas/Ladder'
        ladders:
          type: array
          items:
            $ref: '#/components/schemas/Ladder'
    Ladder:
      description: 排行榜数据
      type: object
      properties:
        customerId:

          description: 用户ID
          type: string
          example: '10023123123221111'
        rank:
          format: int32
          description: 排名
          type: integer
          example: 1
        point:
          description: 积分数量
          type: number
          example: 100
        globalPoint:
          description: 全服积分总数量
          type: number
          example: 100
        assetRatio:
          description: 资产占比
          type: number
          example: 0.15
        level:
          description: 等级
          type: string
          x-field-extra-annotation: "@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)"
          example: 10
    TransferOutRequest:
      description: 积分消耗请求
      type: object
      properties:
        customerId:
          description: 用户id
          type: string
          example: '10023123123221111'
        businessType:
          type: string
        businessId:
          description: 业务id
          type: string
        saasId:
          description: saasId
          type: string
        type:
          required: false
          $ref: '#/components/schemas/AssetTypeEnum'
        amount:
          description: 数量
          type: string
        desc:
          description: 积分流水描述信息
          type: string
    BusinessTypeEnum:
      description: businessType enum
      enum:
        - USER_BET
        - CLAIM_TICKET
        - BUY_OJO
        - WITHDRAW_OJO
      type: string
    AssetTypeEnum:
      description: asset type enum
      enum:
        - POINT
        - EXPERIENCE
        - TICKET
        - VOUCHER
        - BOOST
        - OJO
      type: string
    AssetCategoryEnum:
      description: assetCategory enum
      enum:
        - NORMAL
      type: string

    WebResultLadder:
      allOf:
        - $ref: '#/components/schemas/WebResult'
        - type: object
          properties:
            obj:
              $ref: '#/components/schemas/GlobalLadder'
    GlobalLadder:
      type: object
      required:
        - type
      properties:
        startTime:
          type: integer
          format: int64
          x-field-extra-annotation: "@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)"
        endTime:
          type: integer
          format: int64
        calculating:
          type: boolean
        cycle:
          type: integer
        myLadder:
          $ref: '#/components/schemas/LadderItem'
        globalLadder:
          description: 排行榜数据
          type: array
          items:
            $ref: '#/components/schemas/LadderItem'
    LadderItem:
      description: 排行榜数据
      type: object
      properties:
        level:
          description: 徽章等级
          type: integer
          example: 1 ～ 11
        customerId:
          description: 用户ID
          type: string
          example: '10023123123221111'
        nickName:
          type: string
        icon:
          type: string
        rank:
          format: int32
          description: 排名
          type: integer
          example: 1
        point:
          description: 积分数量
          type: number
          example: 100