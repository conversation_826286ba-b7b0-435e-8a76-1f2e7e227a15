package com.kikitrade.member.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.kikitrade.member.generated.model.v2.WebResultAssetVO;
import com.kikitrade.member.generated.model.v2.WebResultLadder;
import com.kikitrade.member.generated.model.v2.WebResultMemberOrderVO;
import com.kikitrade.member.generated.model.v2.WebResultMembershipVO;
import com.kikitrade.member.generated.model.v2.WebResultOrderVO;
import com.kikitrade.member.generated.model.v2.WebResultPointLedgerList;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * WebResult
 */

@JsonIgnoreProperties(
  value = "code", // ignore manually set code, it will be automatically generated by Jackson during serialization
  allowSetters = true // allows the code to be set during deserialization
)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "code", visible = true)
@JsonSubTypes({
  @JsonSubTypes.Type(value = WebResultAssetVO.class, name = "WebResultAssetVO"),
  @JsonSubTypes.Type(value = WebResultLadder.class, name = "WebResultLadder"),
  @JsonSubTypes.Type(value = WebResultMemberOrderVO.class, name = "WebResultMemberOrderVO"),
  @JsonSubTypes.Type(value = WebResultMembershipVO.class, name = "WebResultMembershipVO"),
  @JsonSubTypes.Type(value = WebResultOrderVO.class, name = "WebResultOrderVO"),
  @JsonSubTypes.Type(value = WebResultPointLedgerList.class, name = "WebResultPointLedgerList")
})

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class WebResult {

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    return true;
  }

  @Override
  public int hashCode() {
    return Objects.hash();
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WebResult {\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

