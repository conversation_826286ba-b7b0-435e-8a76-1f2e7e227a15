package com.kikitrade.member.generated.api.v2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Generated;
import java.util.Optional;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Controller
@RequestMapping("${openapi.kweb.base-path:}")
public class MemberApiController implements MemberApi {

    private final MemberApiDelegate delegate;

    public MemberApiController(@Autowired(required = false) MemberApiDelegate delegate) {
        this.delegate = Optional.ofNullable(delegate).orElse(new MemberApiDelegate() {});
    }

    @Override
    public MemberApiDelegate getDelegate() {
        return delegate;
    }

}
