package com.kikitrade.member.generated.api.v2;

import org.springframework.web.context.request.NativeWebRequest;

import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link MemberApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface MemberApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }
}
