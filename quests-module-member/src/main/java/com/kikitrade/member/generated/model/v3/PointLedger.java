package com.kikitrade.member.generated.model.v3;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * 积分流水数据
 */

@Schema(name = "PointLedger", description = "积分流水数据")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class PointLedger {

  @JsonProperty("amount")
  private BigDecimal amount;

  @JsonProperty("descI18n")
  private String descI18n;

  @JsonProperty("created")
  private Long created;

  @JsonProperty("operateType")
  private Integer operateType;

  @JsonProperty("id")
  private String id;

  @JsonProperty("businessType")
  private String businessType;

  @JsonProperty("menu")
  private String menu;

  public PointLedger amount(BigDecimal amount) {
    this.amount = amount;
    return this;
  }

  /**
   * 积分流水ID
   * @return amount
  */
  @Valid 
  @Schema(name = "amount", example = "90", description = "积分流水ID", required = false)
  public BigDecimal getAmount() {
    return amount;
  }

  public void setAmount(BigDecimal amount) {
    this.amount = amount;
  }

  public PointLedger descI18n(String descI18n) {
    this.descI18n = descI18n;
    return this;
  }

  /**
   * 流水描述 ｜ ***国际化***
   * @return descI18n
  */
  
  @Schema(name = "descI18n", description = "流水描述 ｜ ***国际化***", required = false)
  public String getDescI18n() {
    return descI18n;
  }

  public void setDescI18n(String descI18n) {
    this.descI18n = descI18n;
  }

  public PointLedger created(Long created) {
    this.created = created;
    return this;
  }

  /**
   * Get created
   * @return created
  */
  
  @Schema(name = "created", required = false)
  public Long getCreated() {
    return created;
  }

  public void setCreated(Long created) {
    this.created = created;
  }

  public PointLedger operateType(Integer operateType) {
    this.operateType = operateType;
    return this;
  }

  /**
   * 操作类型 (4)转入 (5)转出
   * @return operateType
  */
  
  @Schema(name = "operateType", example = "4", description = "操作类型 (4)转入 (5)转出", required = false)
  public Integer getOperateType() {
    return operateType;
  }

  public void setOperateType(Integer operateType) {
    this.operateType = operateType;
  }

  public PointLedger id(String id) {
    this.id = id;
    return this;
  }

  /**
   * 积分流水ID
   * @return id
  */
  
  @Schema(name = "id", example = "2020023232", description = "积分流水ID", required = false)
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public PointLedger businessType(String businessType) {
    this.businessType = businessType;
    return this;
  }

  /**
   * Get businessType
   * @return businessType
  */
  
  @Schema(name = "businessType", example = "Post", required = false)
  public String getBusinessType() {
    return businessType;
  }

  public void setBusinessType(String businessType) {
    this.businessType = businessType;
  }

  public PointLedger menu(String menu) {
    this.menu = menu;
    return this;
  }

  /**
   * menu
   * @return menu
  */
  
  @Schema(name = "menu", description = "menu", required = false)
  public String getMenu() {
    return menu;
  }

  public void setMenu(String menu) {
    this.menu = menu;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PointLedger pointLedger = (PointLedger) o;
    return Objects.equals(this.amount, pointLedger.amount) &&
        Objects.equals(this.descI18n, pointLedger.descI18n) &&
        Objects.equals(this.created, pointLedger.created) &&
        Objects.equals(this.operateType, pointLedger.operateType) &&
        Objects.equals(this.id, pointLedger.id) &&
        Objects.equals(this.businessType, pointLedger.businessType) &&
        Objects.equals(this.menu, pointLedger.menu);
  }

  @Override
  public int hashCode() {
    return Objects.hash(amount, descI18n, created, operateType, id, businessType, menu);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PointLedger {\n");
    sb.append("    amount: ").append(toIndentedString(amount)).append("\n");
    sb.append("    descI18n: ").append(toIndentedString(descI18n)).append("\n");
    sb.append("    created: ").append(toIndentedString(created)).append("\n");
    sb.append("    operateType: ").append(toIndentedString(operateType)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    businessType: ").append(toIndentedString(businessType)).append("\n");
    sb.append("    menu: ").append(toIndentedString(menu)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

