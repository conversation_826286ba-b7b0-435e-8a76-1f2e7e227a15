package com.kikitrade.member.generated.model.v3;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * 排行榜数据
 */

@Schema(name = "topLadder", description = "排行榜数据")
@JsonTypeName("topLadder")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TopLadder {

  @JsonProperty("customerId")
  private String customerId;

  @JsonProperty("rank")
  private Integer rank;

  @JsonProperty("point")
  private BigDecimal point;

  @JsonProperty("level")
  @com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
  private String level;

  @JsonProperty("earnedPoint")
  private BigDecimal earnedPoint;

  @JsonProperty("referralPoint")
  private BigDecimal referralPoint;

  @JsonProperty("postCount")
  private Long postCount;

  @JsonProperty("ospAvatar")
  private String ospAvatar;

  @JsonProperty("ospHandle")
  private String ospHandle;

  @JsonProperty("twitterAvatar")
  private String twitterAvatar;

  @JsonProperty("twitterDisplayName")
  private String twitterDisplayName;

  @JsonProperty("rankPercent")
  private BigDecimal rankPercent;

  public TopLadder customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

  /**
   * 用户ID
   * @return customerId
  */
  
  @Schema(name = "customerId", example = "10023123123221111", description = "用户ID", required = false)
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public TopLadder rank(Integer rank) {
    this.rank = rank;
    return this;
  }

  /**
   * 排名
   * @return rank
  */
  
  @Schema(name = "rank", example = "1", description = "排名", required = false)
  public Integer getRank() {
    return rank;
  }

  public void setRank(Integer rank) {
    this.rank = rank;
  }

  public TopLadder point(BigDecimal point) {
    this.point = point;
    return this;
  }

  /**
   * 积分数量
   * @return point
  */
  @Valid 
  @Schema(name = "point", example = "100", description = "积分数量", required = false)
  public BigDecimal getPoint() {
    return point;
  }

  public void setPoint(BigDecimal point) {
    this.point = point;
  }

  public TopLadder level(String level) {
    this.level = level;
    return this;
  }

  /**
   * 等级
   * @return level
  */
  
  @Schema(name = "level", example = "10", description = "等级", required = false)
  public String getLevel() {
    return level;
  }

  public void setLevel(String level) {
    this.level = level;
  }

  public TopLadder earnedPoint(BigDecimal earnedPoint) {
    this.earnedPoint = earnedPoint;
    return this;
  }

  /**
   * 自己获得积分数量
   * @return earnedPoint
  */
  @Valid 
  @Schema(name = "earnedPoint", example = "100", description = "自己获得积分数量", required = false)
  public BigDecimal getEarnedPoint() {
    return earnedPoint;
  }

  public void setEarnedPoint(BigDecimal earnedPoint) {
    this.earnedPoint = earnedPoint;
  }

  public TopLadder referralPoint(BigDecimal referralPoint) {
    this.referralPoint = referralPoint;
    return this;
  }

  /**
   * 邀请继承积分数量
   * @return referralPoint
  */
  @Valid 
  @Schema(name = "referralPoint", example = "100", description = "邀请继承积分数量", required = false)
  public BigDecimal getReferralPoint() {
    return referralPoint;
  }

  public void setReferralPoint(BigDecimal referralPoint) {
    this.referralPoint = referralPoint;
  }

  public TopLadder postCount(Long postCount) {
    this.postCount = postCount;
    return this;
  }

  /**
   * aura帖子数
   * @return postCount
  */
  
  @Schema(name = "postCount", example = "100", description = "aura帖子数", required = false)
  public Long getPostCount() {
    return postCount;
  }

  public void setPostCount(Long postCount) {
    this.postCount = postCount;
  }

  public TopLadder ospAvatar(String ospAvatar) {
    this.ospAvatar = ospAvatar;
    return this;
  }

  /**
   * osp 头像
   * @return ospAvatar
  */
  
  @Schema(name = "ospAvatar", description = "osp 头像", required = false)
  public String getOspAvatar() {
    return ospAvatar;
  }

  public void setOspAvatar(String ospAvatar) {
    this.ospAvatar = ospAvatar;
  }

  public TopLadder ospHandle(String ospHandle) {
    this.ospHandle = ospHandle;
    return this;
  }

  /**
   * osp handle名
   * @return ospHandle
  */
  
  @Schema(name = "ospHandle", description = "osp handle名", required = false)
  public String getOspHandle() {
    return ospHandle;
  }

  public void setOspHandle(String ospHandle) {
    this.ospHandle = ospHandle;
  }

  public TopLadder twitterAvatar(String twitterAvatar) {
    this.twitterAvatar = twitterAvatar;
    return this;
  }

  /**
   * twitter头像
   * @return twitterAvatar
  */
  
  @Schema(name = "twitterAvatar", description = "twitter头像", required = false)
  public String getTwitterAvatar() {
    return twitterAvatar;
  }

  public void setTwitterAvatar(String twitterAvatar) {
    this.twitterAvatar = twitterAvatar;
  }

  public TopLadder twitterDisplayName(String twitterDisplayName) {
    this.twitterDisplayName = twitterDisplayName;
    return this;
  }

  /**
   * twitter展示名称
   * @return twitterDisplayName
  */
  
  @Schema(name = "twitterDisplayName", description = "twitter展示名称", required = false)
  public String getTwitterDisplayName() {
    return twitterDisplayName;
  }

  public void setTwitterDisplayName(String twitterDisplayName) {
    this.twitterDisplayName = twitterDisplayName;
  }

  public TopLadder rankPercent(BigDecimal rankPercent) {
    this.rankPercent = rankPercent;
    return this;
  }

  /**
   * 排行位置比例
   * @return rankPercent
  */
  @Valid 
  @Schema(name = "rankPercent", description = "排行位置比例", required = false)
  public BigDecimal getRankPercent() {
    return rankPercent;
  }

  public void setRankPercent(BigDecimal rankPercent) {
    this.rankPercent = rankPercent;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TopLadder topLadder = (TopLadder) o;
    return Objects.equals(this.customerId, topLadder.customerId) &&
        Objects.equals(this.rank, topLadder.rank) &&
        Objects.equals(this.point, topLadder.point) &&
        Objects.equals(this.level, topLadder.level) &&
        Objects.equals(this.earnedPoint, topLadder.earnedPoint) &&
        Objects.equals(this.referralPoint, topLadder.referralPoint) &&
        Objects.equals(this.postCount, topLadder.postCount) &&
        Objects.equals(this.ospAvatar, topLadder.ospAvatar) &&
        Objects.equals(this.ospHandle, topLadder.ospHandle) &&
        Objects.equals(this.twitterAvatar, topLadder.twitterAvatar) &&
        Objects.equals(this.twitterDisplayName, topLadder.twitterDisplayName) &&
        Objects.equals(this.rankPercent, topLadder.rankPercent);
  }

  @Override
  public int hashCode() {
    return Objects.hash(customerId, rank, point, level, earnedPoint, referralPoint, postCount, ospAvatar, ospHandle, twitterAvatar, twitterDisplayName, rankPercent);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TopLadder {\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    rank: ").append(toIndentedString(rank)).append("\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("    level: ").append(toIndentedString(level)).append("\n");
    sb.append("    earnedPoint: ").append(toIndentedString(earnedPoint)).append("\n");
    sb.append("    referralPoint: ").append(toIndentedString(referralPoint)).append("\n");
    sb.append("    postCount: ").append(toIndentedString(postCount)).append("\n");
    sb.append("    ospAvatar: ").append(toIndentedString(ospAvatar)).append("\n");
    sb.append("    ospHandle: ").append(toIndentedString(ospHandle)).append("\n");
    sb.append("    twitterAvatar: ").append(toIndentedString(twitterAvatar)).append("\n");
    sb.append("    twitterDisplayName: ").append(toIndentedString(twitterDisplayName)).append("\n");
    sb.append("    rankPercent: ").append(toIndentedString(rankPercent)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

