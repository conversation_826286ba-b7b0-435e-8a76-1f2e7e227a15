package com.kikitrade.member.generated.model.v3;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * 积分日流水
 */

@Schema(name = "PointsDailyLedger", description = "积分日流水")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class PointsDailyLedger {

  @JsonProperty("date")
  private Long date;

  @JsonProperty("increment")
  private BigDecimal increment;

  public PointsDailyLedger date(Long date) {
    this.date = date;
    return this;
  }

  /**
   * 数据发生日期
   * @return date
  */
  
  @Schema(name = "date", description = "数据发生日期", required = false)
  public Long getDate() {
    return date;
  }

  public void setDate(Long date) {
    this.date = date;
  }

  public PointsDailyLedger increment(BigDecimal increment) {
    this.increment = increment;
    return this;
  }

  /**
   * 当日增量
   * @return increment
  */
  @Valid 
  @Schema(name = "increment", example = "100", description = "当日增量", required = false)
  public BigDecimal getIncrement() {
    return increment;
  }

  public void setIncrement(BigDecimal increment) {
    this.increment = increment;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PointsDailyLedger pointsDailyLedger = (PointsDailyLedger) o;
    return Objects.equals(this.date, pointsDailyLedger.date) &&
        Objects.equals(this.increment, pointsDailyLedger.increment);
  }

  @Override
  public int hashCode() {
    return Objects.hash(date, increment);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PointsDailyLedger {\n");
    sb.append("    date: ").append(toIndentedString(date)).append("\n");
    sb.append("    increment: ").append(toIndentedString(increment)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

