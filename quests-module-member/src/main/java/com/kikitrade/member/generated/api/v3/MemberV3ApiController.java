package com.kikitrade.member.generated.api.v3;

import com.kikitrade.member.generated.model.v3.WebResultEdgenPoint;
import com.kikitrade.member.generated.model.v3.WebResultLadder;
import com.kikitrade.member.generated.model.v3.WebResultPointLedgerList;
import com.kikitrade.member.generated.model.v3.WebResultPointStaticList;
import com.kikitrade.member.generated.model.v3.WebResultTopLadder;
import com.kikitrade.member.generated.model.v3.WebResultTopRank;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.*;
import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Controller
@RequestMapping("${openapi.kweb.base-path:}")
public class MemberV3ApiController implements MemberV3Api {

    private final MemberV3ApiDelegate delegate;

    public MemberV3ApiController(@Autowired(required = false) MemberV3ApiDelegate delegate) {
        this.delegate = Optional.ofNullable(delegate).orElse(new MemberV3ApiDelegate() {});
    }

    @Override
    public MemberV3ApiDelegate getDelegate() {
        return delegate;
    }

}
