package com.kikitrade.member.generated.api.v2;

import com.kikitrade.member.generated.model.v2.TransferOutRequest;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.member.generated.model.v2.WebResultAssetVO;
import com.kikitrade.member.generated.model.v2.WebResultLadder;
import com.kikitrade.member.generated.model.v2.WebResultMemberOrderVO;
import com.kikitrade.member.generated.model.v2.WebResultMembershipVO;
import com.kikitrade.member.generated.model.v2.WebResultOrderVO;
import com.kikitrade.member.generated.model.v2.WebResultPointLedgerList;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.*;
import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Controller
@RequestMapping("${openapi.kweb.base-path:}")
public class MemberV2ApiController implements MemberV2Api {

    private final MemberV2ApiDelegate delegate;

    public MemberV2ApiController(@Autowired(required = false) MemberV2ApiDelegate delegate) {
        this.delegate = Optional.ofNullable(delegate).orElse(new MemberV2ApiDelegate() {});
    }

    @Override
    public MemberV2ApiDelegate getDelegate() {
        return delegate;
    }

}
