package com.kikitrade.member.generated.api.v2;

import com.kikitrade.member.generated.model.v2.TransferOutRequest;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.member.generated.model.v2.WebResultAssetVO;
import com.kikitrade.member.generated.model.v2.WebResultLadder;
import com.kikitrade.member.generated.model.v2.WebResultMemberOrderVO;
import com.kikitrade.member.generated.model.v2.WebResultMembershipVO;
import com.kikitrade.member.generated.model.v2.WebResultOrderVO;
import com.kikitrade.member.generated.model.v2.WebResultPointLedgerList;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link MemberV2ApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface MemberV2ApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * GET /v2/member/ladder : 积分天梯排行榜
     * 获取积分排行榜
     *
     * @param saasId  (required)
     * @param season  (required)
     * @param level 徽章等级 (optional)
     * @param limit 排行榜数量,范围：[0,200] (optional, default to 30)
     * @return Successful operation (status code 200)
     * @see MemberV2Api#globalLadder
     */
    default ResponseEntity<WebResultLadder> globalLadder(String saasId,
        String season,
        Integer level,
        Integer limit) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/member/ladder/history : 积分历史排行榜
     * 获取积分历史排行榜
     *
     * @param saasId  (required)
     * @param season 查询赛季（不传默认为当前赛季） (required)
     * @param cycle 查询赛季周数（必传） (required)
     * @param level 徽章等级 (optional)
     * @param limit 排行榜数量,范围：[0,200] (optional, default to 30)
     * @return Successful operation (status code 200)
     * @see MemberV2Api#historyLadder
     */
    default ResponseEntity<WebResultLadder> historyLadder(String saasId,
        String season,
        String cycle,
        Integer level,
        Integer limit) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/member/asset/{type} : 查询用户某个资产
     * 查询用户某个资产
     *
     * @param type 查询用户某个资产, 传nft (required)
     * @return Successful operation (status code 200)
     * @see MemberV2Api#memberAsset
     */
    default ResponseEntity<WebResultAssetVO> memberAsset(String type) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/member/order : 查询订单信息
     * 查询订单信息
     *
     * @param saasId saasId (required)
     * @param orderId orderId (required)
     * @return Successful operation (status code 200)
     * @see MemberV2Api#memberBusinessOrder
     */
    default ResponseEntity<WebResultMemberOrderVO> memberBusinessOrder(String saasId,
        String orderId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/member/membership : 会员明细信息
     * 会员明细信息
     *
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     * @see MemberV2Api#membership
     */
    default ResponseEntity<WebResultMembershipVO> membership(String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v2/member/points/ledgers/{type} : 积分流水
     * 积分流水-已领取|已使用
     *
     * @param type 积分操作类型(transfer_in：获得积分,transfer_out：消耗积分) (required)
     * @param offset 分页offset，默认0 (required)
     * @param limit 分页limit,默认10 (required)
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     * @see MemberV2Api#pointLedgers
     */
    default ResponseEntity<WebResultPointLedgerList> pointLedgers(String type,
        Integer offset,
        Integer limit,
        String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v1/member/points/ledgers/{type} : 积分流水
     * 积分流水-已领取|已使用
     *
     * @param type 积分操作类型(transfer_in：获得积分,transfer_out：消耗积分) (required)
     * @param offset 分页offset，默认0 (required)
     * @param limit 分页limit,默认10 (required)
     * @param saasId saas_id (required)
     * @return Successful operation (status code 200)
     * @see MemberV2Api#pointLedgersV1
     */
    default ResponseEntity<WebResultPointLedgerList> pointLedgersV1(String type,
        Integer offset,
        Integer limit,
        String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /v2/member/transferout : /member/transferout
     * 消耗积分
     *
     * @param JWT_TOKEN JWT_TOKEN (required)
     * @param saasId  (required)
     * @param transferOutRequest  (optional)
     * @return OK (status code 200)
     * @see MemberV2Api#transferOut
     */
    default ResponseEntity<WebResult> transferOut(String JWT_TOKEN,
        String saasId,
        TransferOutRequest transferOutRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /v2/member/upgrade : 升级会员
     * 升级会员操作，消耗对应等级的积分来获取会员身份
     *
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     * @see MemberV2Api#upgrade
     */
    default ResponseEntity<WebResultOrderVO> upgrade(String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /v2/member/{code}/expansion : 城堡扩容
     * 城堡扩容
     *
     * @param code 需要兑换黄金的业务code，本次传kingdom (required)
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     * @see MemberV2Api#v2MemberCodeExpansionPost
     */
    default ResponseEntity<WebResultOrderVO> v2MemberCodeExpansionPost(String code,
        String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /v2/member/{code}/harvest : 城堡harvest
     * 城堡harvest
     *
     * @param code 需要兑换黄金的业务code，本次传kingdom (required)
     * @param saasId saasId (required)
     * @return Successful operation (status code 200)
     * @see MemberV2Api#v2MemberCodeHarvestPost
     */
    default ResponseEntity<WebResultOrderVO> v2MemberCodeHarvestPost(String code,
        String saasId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
