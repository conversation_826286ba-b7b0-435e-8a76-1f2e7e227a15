package com.kikitrade.member.generated.api.s2;

import com.kikitrade.member.generated.model.s2.TransferOutRequest;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.member.generated.model.s2.WebResultLadder;
import com.kikitrade.member.generated.model.s2.WebResultTopLadder;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.*;
import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Controller
@RequestMapping("${openapi.kweb.base-path:/s2}")
public class MemberS2ApiController implements MemberS2Api {

    private final MemberS2ApiDelegate delegate;

    public MemberS2ApiController(@Autowired(required = false) MemberS2ApiDelegate delegate) {
        this.delegate = Optional.ofNullable(delegate).orElse(new MemberS2ApiDelegate() {});
    }

    @Override
    public MemberS2ApiDelegate getDelegate() {
        return delegate;
    }

}
