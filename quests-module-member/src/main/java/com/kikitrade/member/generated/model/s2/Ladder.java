package com.kikitrade.member.generated.model.s2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * 排行榜数据
 */

@Schema(name = "Ladder", description = "排行榜数据")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class Ladder {

  @JsonProperty("customerId")
  private String customerId;

  @JsonProperty("rank")
  private Integer rank;

  @JsonProperty("point")
  private BigDecimal point;

  @JsonProperty("globalPoint")
  private BigDecimal globalPoint;

  @JsonProperty("assetRatio")
  private BigDecimal assetRatio;

  @JsonProperty("level")
  @com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
  private String level;

  public Ladder customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

  /**
   * 用户ID
   * @return customerId
  */
  
  @Schema(name = "customerId", example = "10023123123221111", description = "用户ID", required = false)
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public Ladder rank(Integer rank) {
    this.rank = rank;
    return this;
  }

  /**
   * 排名
   * @return rank
  */
  
  @Schema(name = "rank", example = "1", description = "排名", required = false)
  public Integer getRank() {
    return rank;
  }

  public void setRank(Integer rank) {
    this.rank = rank;
  }

  public Ladder point(BigDecimal point) {
    this.point = point;
    return this;
  }

  /**
   * 积分数量
   * @return point
  */
  @Valid 
  @Schema(name = "point", example = "100", description = "积分数量", required = false)
  public BigDecimal getPoint() {
    return point;
  }

  public void setPoint(BigDecimal point) {
    this.point = point;
  }

  public Ladder globalPoint(BigDecimal globalPoint) {
    this.globalPoint = globalPoint;
    return this;
  }

  /**
   * 全服积分总数量
   * @return globalPoint
  */
  @Valid 
  @Schema(name = "globalPoint", example = "100", description = "全服积分总数量", required = false)
  public BigDecimal getGlobalPoint() {
    return globalPoint;
  }

  public void setGlobalPoint(BigDecimal globalPoint) {
    this.globalPoint = globalPoint;
  }

  public Ladder assetRatio(BigDecimal assetRatio) {
    this.assetRatio = assetRatio;
    return this;
  }

  /**
   * 资产占比
   * @return assetRatio
  */
  @Valid 
  @Schema(name = "assetRatio", example = "0.15", description = "资产占比", required = false)
  public BigDecimal getAssetRatio() {
    return assetRatio;
  }

  public void setAssetRatio(BigDecimal assetRatio) {
    this.assetRatio = assetRatio;
  }

  public Ladder level(String level) {
    this.level = level;
    return this;
  }

  /**
   * 等级
   * @return level
  */
  
  @Schema(name = "level", example = "10", description = "等级", required = false)
  public String getLevel() {
    return level;
  }

  public void setLevel(String level) {
    this.level = level;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Ladder ladder = (Ladder) o;
    return Objects.equals(this.customerId, ladder.customerId) &&
        Objects.equals(this.rank, ladder.rank) &&
        Objects.equals(this.point, ladder.point) &&
        Objects.equals(this.globalPoint, ladder.globalPoint) &&
        Objects.equals(this.assetRatio, ladder.assetRatio) &&
        Objects.equals(this.level, ladder.level);
  }

  @Override
  public int hashCode() {
    return Objects.hash(customerId, rank, point, globalPoint, assetRatio, level);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Ladder {\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    rank: ").append(toIndentedString(rank)).append("\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("    globalPoint: ").append(toIndentedString(globalPoint)).append("\n");
    sb.append("    assetRatio: ").append(toIndentedString(assetRatio)).append("\n");
    sb.append("    level: ").append(toIndentedString(level)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

