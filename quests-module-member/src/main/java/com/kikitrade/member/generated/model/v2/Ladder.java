package com.kikitrade.member.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.member.generated.model.v2.LadderItem;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * Ladder
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class Ladder {

  @JsonProperty("startTime")
  @com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
  private Long startTime;

  @JsonProperty("endTime")
  private Long endTime;

  @JsonProperty("calculating")
  private Boolean calculating;

  @JsonProperty("cycle")
  private Integer cycle;

  @JsonProperty("myLadder")
  private LadderItem myLadder;

  @JsonProperty("globalLadder")
  @Valid
  private List<LadderItem> globalLadder = null;

  public Ladder startTime(Long startTime) {
    this.startTime = startTime;
    return this;
  }

  /**
   * Get startTime
   * @return startTime
  */
  
  @Schema(name = "startTime", required = false)
  public Long getStartTime() {
    return startTime;
  }

  public void setStartTime(Long startTime) {
    this.startTime = startTime;
  }

  public Ladder endTime(Long endTime) {
    this.endTime = endTime;
    return this;
  }

  /**
   * Get endTime
   * @return endTime
  */
  
  @Schema(name = "endTime", required = false)
  public Long getEndTime() {
    return endTime;
  }

  public void setEndTime(Long endTime) {
    this.endTime = endTime;
  }

  public Ladder calculating(Boolean calculating) {
    this.calculating = calculating;
    return this;
  }

  /**
   * Get calculating
   * @return calculating
  */
  
  @Schema(name = "calculating", required = false)
  public Boolean getCalculating() {
    return calculating;
  }

  public void setCalculating(Boolean calculating) {
    this.calculating = calculating;
  }

  public Ladder cycle(Integer cycle) {
    this.cycle = cycle;
    return this;
  }

  /**
   * Get cycle
   * @return cycle
  */
  
  @Schema(name = "cycle", required = false)
  public Integer getCycle() {
    return cycle;
  }

  public void setCycle(Integer cycle) {
    this.cycle = cycle;
  }

  public Ladder myLadder(LadderItem myLadder) {
    this.myLadder = myLadder;
    return this;
  }

  /**
   * Get myLadder
   * @return myLadder
  */
  @Valid 
  @Schema(name = "myLadder", required = false)
  public LadderItem getMyLadder() {
    return myLadder;
  }

  public void setMyLadder(LadderItem myLadder) {
    this.myLadder = myLadder;
  }

  public Ladder globalLadder(List<LadderItem> globalLadder) {
    this.globalLadder = globalLadder;
    return this;
  }

  public Ladder addGlobalLadderItem(LadderItem globalLadderItem) {
    if (this.globalLadder == null) {
      this.globalLadder = new ArrayList<>();
    }
    this.globalLadder.add(globalLadderItem);
    return this;
  }

  /**
   * 排行榜数据
   * @return globalLadder
  */
  @Valid 
  @Schema(name = "globalLadder", description = "排行榜数据", required = false)
  public List<LadderItem> getGlobalLadder() {
    return globalLadder;
  }

  public void setGlobalLadder(List<LadderItem> globalLadder) {
    this.globalLadder = globalLadder;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Ladder ladder = (Ladder) o;
    return Objects.equals(this.startTime, ladder.startTime) &&
        Objects.equals(this.endTime, ladder.endTime) &&
        Objects.equals(this.calculating, ladder.calculating) &&
        Objects.equals(this.cycle, ladder.cycle) &&
        Objects.equals(this.myLadder, ladder.myLadder) &&
        Objects.equals(this.globalLadder, ladder.globalLadder);
  }

  @Override
  public int hashCode() {
    return Objects.hash(startTime, endTime, calculating, cycle, myLadder, globalLadder);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Ladder {\n");
    sb.append("    startTime: ").append(toIndentedString(startTime)).append("\n");
    sb.append("    endTime: ").append(toIndentedString(endTime)).append("\n");
    sb.append("    calculating: ").append(toIndentedString(calculating)).append("\n");
    sb.append("    cycle: ").append(toIndentedString(cycle)).append("\n");
    sb.append("    myLadder: ").append(toIndentedString(myLadder)).append("\n");
    sb.append("    globalLadder: ").append(toIndentedString(globalLadder)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

