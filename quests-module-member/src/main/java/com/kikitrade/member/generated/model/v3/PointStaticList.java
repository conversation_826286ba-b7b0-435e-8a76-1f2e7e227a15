package com.kikitrade.member.generated.model.v3;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.member.generated.model.v3.PointsDailyLedger;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * PointStaticList
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class PointStaticList {

  @JsonProperty("todayReceived")
  private BigDecimal todayReceived = new BigDecimal("0");

  @JsonProperty("history")
  @Valid
  private List<PointsDailyLedger> history = null;

  public PointStaticList todayReceived(BigDecimal todayReceived) {
    this.todayReceived = todayReceived;
    return this;
  }

  /**
   * 今日获得积分
   * @return todayReceived
  */
  @Valid 
  @Schema(name = "todayReceived", example = "100", description = "今日获得积分", required = false)
  public BigDecimal getTodayReceived() {
    return todayReceived;
  }

  public void setTodayReceived(BigDecimal todayReceived) {
    this.todayReceived = todayReceived;
  }

  public PointStaticList history(List<PointsDailyLedger> history) {
    this.history = history;
    return this;
  }

  public PointStaticList addHistoryItem(PointsDailyLedger historyItem) {
    if (this.history == null) {
      this.history = new ArrayList<>();
    }
    this.history.add(historyItem);
    return this;
  }

  /**
   * 积分每日数据
   * @return history
  */
  @Valid 
  @Schema(name = "history", description = "积分每日数据", required = false)
  public List<PointsDailyLedger> getHistory() {
    return history;
  }

  public void setHistory(List<PointsDailyLedger> history) {
    this.history = history;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PointStaticList pointStaticList = (PointStaticList) o;
    return Objects.equals(this.todayReceived, pointStaticList.todayReceived) &&
        Objects.equals(this.history, pointStaticList.history);
  }

  @Override
  public int hashCode() {
    return Objects.hash(todayReceived, history);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PointStaticList {\n");
    sb.append("    todayReceived: ").append(toIndentedString(todayReceived)).append("\n");
    sb.append("    history: ").append(toIndentedString(history)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

