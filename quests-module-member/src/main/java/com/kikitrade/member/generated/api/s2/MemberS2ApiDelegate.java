package com.kikitrade.member.generated.api.s2;

import com.kikitrade.member.generated.model.s2.TransferOutRequest;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.member.generated.model.s2.WebResultLadder;
import com.kikitrade.member.generated.model.s2.WebResultTopLadder;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link MemberS2ApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface MemberS2ApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * GET /member/ladder : 积分天梯排行榜
     * 获取积分排行榜
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cid 其他平台用户ID (required)
     * @param level 徽章等级 (optional)
     * @param limit 排行榜数量,范围：[0,200] (optional, default to 30)
     * @return Successful operation (status code 200)
     * @see MemberS2Api#globalLadder
     */
    default ResponseEntity<WebResultLadder> globalLadder(String authorization,
        String saasId,
        String cid,
        Integer level,
        Integer limit) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /member/ladder/history : 积分历史排行榜
     * 获取积分历史排行榜
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cycle 查询赛季周数（必传） (required)
     * @param cid 其他平台用户ID (required)
     * @param level 徽章等级 (optional)
     * @param limit 排行榜数量,范围：[0,200] (optional, default to 30)
     * @return Successful operation (status code 200)
     * @see MemberS2Api#historyLadder
     */
    default ResponseEntity<WebResultLadder> historyLadder(String authorization,
        String saasId,
        String cycle,
        String cid,
        Integer level,
        Integer limit) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /member/order/paid : 订单完成支付
     * 订单完成支付
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param orderId 订单id (required)
     * @return Successful operation (status code 200)
     * @see MemberS2Api#orderPaid
     */
    default ResponseEntity<WebResult> orderPaid(String authorization,
        String saasId,
        String orderId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /member/top/ladder : 积分天梯top榜
     * 积分天梯top榜
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cid 业务方用户id (required)
     * @param assetType 资产类型 默认 POINT (optional, default to POINT)
     * @param ladderType 榜单类型 默认 History (optional, default to History)
     * @param limit  (optional, default to 100)
     * @param strategy 排序策略，fold(默认)：相同分数排名相同， unfold：相同分数排名不同 (optional, default to fold)
     * @return Successful operation (status code 200)
     * @see MemberS2Api#topLadder
     */
    default ResponseEntity<WebResultTopLadder> topLadder(String authorization,
        String saasId,
        String cid,
        String assetType,
        String ladderType,
        Integer limit,
        String strategy) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * POST /member/transferout : /member/transferout
     * 消耗积分
     *
     * @param transferOutRequest  (optional)
     * @return OK (status code 200)
     * @see MemberS2Api#transferOut
     */
    default ResponseEntity<WebResult> transferOut(TransferOutRequest transferOutRequest) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
