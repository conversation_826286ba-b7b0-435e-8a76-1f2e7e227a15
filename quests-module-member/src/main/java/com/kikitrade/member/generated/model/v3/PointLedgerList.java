package com.kikitrade.member.generated.model.v3;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.member.generated.model.v3.PointLedger;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * PointLedgerList
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class PointLedgerList {

  @JsonProperty("customerId")
  private String customerId;

  @JsonProperty("point")
  private BigDecimal point;

  @JsonProperty("available")
  private BigDecimal available;

  @JsonProperty("originalAvailable")
  private BigDecimal originalAvailable;

  @JsonProperty("todayPoint")
  private BigDecimal todayPoint;

  @JsonProperty("appPoint")
  private BigDecimal appPoint;

  @JsonProperty("rank")
  private String rank;

  @JsonProperty("rankValue")
  private String rankValue;

  @JsonProperty("pointLedgers")
  @Valid
  private List<PointLedger> pointLedgers = null;

  public PointLedgerList customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

  /**
   * 用户id
   * @return customerId
  */
  @NotNull 
  @Schema(name = "customerId", example = "10023123123221111", description = "用户id", required = true)
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public PointLedgerList point(BigDecimal point) {
    this.point = point;
    return this;
  }

  /**
   * 会员拥有的积分总数量
   * @return point
  */
  @NotNull @Valid 
  @Schema(name = "point", example = "100", description = "会员拥有的积分总数量", required = true)
  public BigDecimal getPoint() {
    return point;
  }

  public void setPoint(BigDecimal point) {
    this.point = point;
  }

  public PointLedgerList available(BigDecimal available) {
    this.available = available;
    return this;
  }

  /**
   * 加成后的积分
   * @return available
  */
  @Valid 
  @Schema(name = "available", example = "100", description = "加成后的积分", required = false)
  public BigDecimal getAvailable() {
    return available;
  }

  public void setAvailable(BigDecimal available) {
    this.available = available;
  }

  public PointLedgerList originalAvailable(BigDecimal originalAvailable) {
    this.originalAvailable = originalAvailable;
    return this;
  }

  /**
   * 原始积分
   * @return originalAvailable
  */
  @Valid 
  @Schema(name = "originalAvailable", example = "100", description = "原始积分", required = false)
  public BigDecimal getOriginalAvailable() {
    return originalAvailable;
  }

  public void setOriginalAvailable(BigDecimal originalAvailable) {
    this.originalAvailable = originalAvailable;
  }

  public PointLedgerList todayPoint(BigDecimal todayPoint) {
    this.todayPoint = todayPoint;
    return this;
  }

  /**
   * 今日获取的积分
   * @return todayPoint
  */
  @Valid 
  @Schema(name = "todayPoint", example = "100", description = "今日获取的积分", required = false)
  public BigDecimal getTodayPoint() {
    return todayPoint;
  }

  public void setTodayPoint(BigDecimal todayPoint) {
    this.todayPoint = todayPoint;
  }

  public PointLedgerList appPoint(BigDecimal appPoint) {
    this.appPoint = appPoint;
    return this;
  }

  /**
   * 平台总积分
   * @return appPoint
  */
  @Valid 
  @Schema(name = "appPoint", example = "100", description = "平台总积分", required = false)
  public BigDecimal getAppPoint() {
    return appPoint;
  }

  public void setAppPoint(BigDecimal appPoint) {
    this.appPoint = appPoint;
  }

  public PointLedgerList rank(String rank) {
    this.rank = rank;
    return this;
  }

  /**
   * 积分百分比排行
   * @return rank
  */
  
  @Schema(name = "rank", example = "10%", description = "积分百分比排行", required = false)
  public String getRank() {
    return rank;
  }

  public void setRank(String rank) {
    this.rank = rank;
  }

  public PointLedgerList rankValue(String rankValue) {
    this.rankValue = rankValue;
    return this;
  }

  /**
   * 积分实际排行
   * @return rankValue
  */
  
  @Schema(name = "rankValue", example = "10", description = "积分实际排行", required = false)
  public String getRankValue() {
    return rankValue;
  }

  public void setRankValue(String rankValue) {
    this.rankValue = rankValue;
  }

  public PointLedgerList pointLedgers(List<PointLedger> pointLedgers) {
    this.pointLedgers = pointLedgers;
    return this;
  }

  public PointLedgerList addPointLedgersItem(PointLedger pointLedgersItem) {
    if (this.pointLedgers == null) {
      this.pointLedgers = new ArrayList<>();
    }
    this.pointLedgers.add(pointLedgersItem);
    return this;
  }

  /**
   * 积分流水数据
   * @return pointLedgers
  */
  @Valid 
  @Schema(name = "pointLedgers", description = "积分流水数据", required = false)
  public List<PointLedger> getPointLedgers() {
    return pointLedgers;
  }

  public void setPointLedgers(List<PointLedger> pointLedgers) {
    this.pointLedgers = pointLedgers;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PointLedgerList pointLedgerList = (PointLedgerList) o;
    return Objects.equals(this.customerId, pointLedgerList.customerId) &&
        Objects.equals(this.point, pointLedgerList.point) &&
        Objects.equals(this.available, pointLedgerList.available) &&
        Objects.equals(this.originalAvailable, pointLedgerList.originalAvailable) &&
        Objects.equals(this.todayPoint, pointLedgerList.todayPoint) &&
        Objects.equals(this.appPoint, pointLedgerList.appPoint) &&
        Objects.equals(this.rank, pointLedgerList.rank) &&
        Objects.equals(this.rankValue, pointLedgerList.rankValue) &&
        Objects.equals(this.pointLedgers, pointLedgerList.pointLedgers);
  }

  @Override
  public int hashCode() {
    return Objects.hash(customerId, point, available, originalAvailable, todayPoint, appPoint, rank, rankValue, pointLedgers);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PointLedgerList {\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("    available: ").append(toIndentedString(available)).append("\n");
    sb.append("    originalAvailable: ").append(toIndentedString(originalAvailable)).append("\n");
    sb.append("    todayPoint: ").append(toIndentedString(todayPoint)).append("\n");
    sb.append("    appPoint: ").append(toIndentedString(appPoint)).append("\n");
    sb.append("    rank: ").append(toIndentedString(rank)).append("\n");
    sb.append("    rankValue: ").append(toIndentedString(rankValue)).append("\n");
    sb.append("    pointLedgers: ").append(toIndentedString(pointLedgers)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

