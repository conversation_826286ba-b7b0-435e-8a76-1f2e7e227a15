package com.kikitrade.member.generated.api.v3;

import com.kikitrade.member.generated.model.v3.WebResultEdgenPoint;
import com.kikitrade.member.generated.model.v3.WebResultLadder;
import com.kikitrade.member.generated.model.v3.WebResultPointLedgerList;
import com.kikitrade.member.generated.model.v3.WebResultPointStaticList;
import com.kikitrade.member.generated.model.v3.WebResultTopLadder;
import com.kikitrade.member.generated.model.v3.WebResultTopRank;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Generated;

/**
 * A delegate to be called by the {@link MemberV3ApiController}}.
 * Implement this interface with a {@link org.springframework.stereotype.Service} annotated class.
 */
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public interface MemberV3ApiDelegate {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * GET /v3/member/points/extension : 插件获取edgen积分
     * 定制化根据twitter handles批量查询edgen积分
     *
     * @param saasId saasId (required)
     * @param handles  (required)
     * @return Successful operation (status code 200)
     * @see MemberV3Api#extensionPoints
     */
    default ResponseEntity<WebResultEdgenPoint> extensionPoints(String saasId,
        List<String> handles) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v3/member/bread/ladder : 积分天梯排行榜
     * 获取积分排行榜
     *
     * @param saasId  (required)
     * @param season  (required)
     * @param level 徽章等级 (optional)
     * @param limit 排行榜数量,范围：[0,200] (optional, default to 30)
     * @return Successful operation (status code 200)
     * @see MemberV3Api#globalLadder
     */
    default ResponseEntity<WebResultLadder> globalLadder(String saasId,
        String season,
        Integer level,
        Integer limit) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v3/member/bread/ladder/history : 积分历史排行榜
     * 获取积分历史排行榜
     *
     * @param saasId  (required)
     * @param season 查询赛季（不传默认为当前赛季） (required)
     * @param cycle 查询赛季周数（必传） (required)
     * @param level 徽章等级 (optional)
     * @param limit 排行榜数量,范围：[0,200] (optional, default to 30)
     * @return Successful operation (status code 200)
     * @see MemberV3Api#historyLadder
     */
    default ResponseEntity<WebResultLadder> historyLadder(String saasId,
        String season,
        String cycle,
        Integer level,
        Integer limit) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v3/member/points/daily : 每日积分
     * 每日积分
     *
     * @param saasId saasId (required)
     * @param idToken idToken (required)
     * @param handleName osp handleName (optional)
     * @param appId osp appId (optional)
     * @param chainId osp chainId (optional)
     * @return Successful operation (status code 200)
     * @see MemberV3Api#pointDaily
     */
    default ResponseEntity<WebResultPointStaticList> pointDaily(String saasId,
        String idToken,
        String handleName,
        String appId,
        String chainId) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v3/member/points/ledgers/{type} : 积分流水
     * 积分流水-已领取|已使用
     *
     * @param type 积分操作类型(transfer_in：获得积分,transfer_out：消耗积分,all：查询全部流水信息) (required)
     * @param offset 分页offset，默认0 (required)
     * @param limit 分页limit,默认10 (required)
     * @param saasId saasId (required)
     * @param businessTypes 业务类型 (optional)
     * @param assetType 资产类型 默认POINT (optional)
     * @return Successful operation (status code 200)
     * @see MemberV3Api#pointLedgersV3
     */
    default ResponseEntity<WebResultPointLedgerList> pointLedgersV3(String type,
        Integer offset,
        Integer limit,
        String saasId,
        List<String> businessTypes,
        String assetType) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v3/member/top/ladder : 积分天梯top榜
     * 积分天梯top榜
     *
     * @param saasId saasId (required)
     * @param idToken idToken (optional)
     * @param handleName osp handleName (optional)
     * @param appId osp appId (optional)
     * @param chainId osp chainId (optional)
     * @param assetType 资产类型 默认 POINT (optional, default to POINT)
     * @param limit  (optional, default to 100)
     * @param strategy 排序策略，fold：相同分数排名相同， unfold(默认)：相同分数排名不同 (optional, default to unfold)
     * @param businessType ladder business type (optional)
     * @return Successful operation (status code 200)
     * @see MemberV3Api#topLadder
     */
    default ResponseEntity<WebResultTopLadder> topLadder(String saasId,
        String idToken,
        String handleName,
        String appId,
        String chainId,
        String assetType,
        Integer limit,
        String strategy,
        String businessType) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

    /**
     * GET /v3/member/top/rank : 积分排行榜
     * 积分排行榜
     *
     * @param saasId saasId (required)
     * @param assetType 资产类型 默认 POINT (optional, default to POINT)
     * @param assetBusinessType 业务类型 (optional)
     * @return Successful operation (status code 200)
     * @see MemberV3Api#topRank
     */
    default ResponseEntity<WebResultTopRank> topRank(String saasId,
        String assetType,
        String assetBusinessType) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "null";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
