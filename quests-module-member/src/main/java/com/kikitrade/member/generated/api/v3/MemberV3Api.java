/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.0.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.kikitrade.member.generated.api.v3;

import com.kikitrade.member.generated.model.v3.WebResultEdgenPoint;
import com.kikitrade.member.generated.model.v3.WebResultLadder;
import com.kikitrade.member.generated.model.v3.WebResultPointLedgerList;
import com.kikitrade.member.generated.model.v3.WebResultPointStaticList;
import com.kikitrade.member.generated.model.v3.WebResultTopLadder;
import com.kikitrade.member.generated.model.v3.WebResultTopRank;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "MemberV3", description = "会员相关接口描述")
public interface MemberV3Api {

    default MemberV3ApiDelegate getDelegate() {
        return new MemberV3ApiDelegate() {};
    }

    /**
     * GET /v3/member/points/extension : 插件获取edgen积分
     * 定制化根据twitter handles批量查询edgen积分
     *
     * @param saasId saasId (required)
     * @param handles  (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "extensionPoints",
        summary = "插件获取edgen积分",
        tags = { "MemberV3" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultEdgenPoint.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v3/member/points/extension",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultEdgenPoint> extensionPoints(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "handles", description = "", required = true) @Valid @RequestParam(value = "handles", required = true) List<String> handles
    ) {
        return getDelegate().extensionPoints(saasId, handles);
    }


    /**
     * GET /v3/member/bread/ladder : 积分天梯排行榜
     * 获取积分排行榜
     *
     * @param saasId  (required)
     * @param season  (required)
     * @param level 徽章等级 (optional)
     * @param limit 排行榜数量,范围：[0,200] (optional, default to 30)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "globalLadder",
        summary = "积分天梯排行榜",
        tags = { "MemberV3" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultLadder.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v3/member/bread/ladder",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultLadder> globalLadder(
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "season", description = "", required = true) @Valid @RequestParam(value = "season", required = true, defaultValue = "season0") String season,
        @Parameter(name = "level", description = "徽章等级") @Valid @RequestParam(value = "level", required = false) Integer level,
        @Parameter(name = "limit", description = "排行榜数量,范围：[0,200]") @Valid @RequestParam(value = "limit", required = false, defaultValue = "30") Integer limit
    ) {
        return getDelegate().globalLadder(saasId, season, level, limit);
    }


    /**
     * GET /v3/member/bread/ladder/history : 积分历史排行榜
     * 获取积分历史排行榜
     *
     * @param saasId  (required)
     * @param season 查询赛季（不传默认为当前赛季） (required)
     * @param cycle 查询赛季周数（必传） (required)
     * @param level 徽章等级 (optional)
     * @param limit 排行榜数量,范围：[0,200] (optional, default to 30)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "historyLadder",
        summary = "积分历史排行榜",
        tags = { "MemberV3" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultLadder.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v3/member/bread/ladder/history",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultLadder> historyLadder(
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "season", description = "查询赛季（不传默认为当前赛季）", required = true) @Valid @RequestParam(value = "season", required = true, defaultValue = "season0") String season,
        @NotNull @Parameter(name = "cycle", description = "查询赛季周数（必传）", required = true) @Valid @RequestParam(value = "cycle", required = true) String cycle,
        @Parameter(name = "level", description = "徽章等级") @Valid @RequestParam(value = "level", required = false) Integer level,
        @Parameter(name = "limit", description = "排行榜数量,范围：[0,200]") @Valid @RequestParam(value = "limit", required = false, defaultValue = "30") Integer limit
    ) {
        return getDelegate().historyLadder(saasId, season, cycle, level, limit);
    }


    /**
     * GET /v3/member/points/daily : 每日积分
     * 每日积分
     *
     * @param saasId saasId (required)
     * @param idToken idToken (required)
     * @param handleName osp handleName (optional)
     * @param appId osp appId (optional)
     * @param chainId osp chainId (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "pointDaily",
        summary = "每日积分",
        tags = { "MemberV3" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultPointStaticList.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v3/member/points/daily",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultPointStaticList> pointDaily(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "idToken", description = "idToken", required = true) @RequestHeader(value = "idToken", required = true) String idToken,
        @Parameter(name = "handleName", description = "osp handleName") @Valid @RequestParam(value = "handleName", required = false) String handleName,
        @Parameter(name = "appId", description = "osp appId") @Valid @RequestParam(value = "appId", required = false) String appId,
        @Parameter(name = "chainId", description = "osp chainId") @Valid @RequestParam(value = "chainId", required = false) String chainId
    ) {
        return getDelegate().pointDaily(saasId, idToken, handleName, appId, chainId);
    }


    /**
     * GET /v3/member/points/ledgers/{type} : 积分流水
     * 积分流水-已领取|已使用
     *
     * @param type 积分操作类型(transfer_in：获得积分,transfer_out：消耗积分,all：查询全部流水信息) (required)
     * @param offset 分页offset，默认0 (required)
     * @param limit 分页limit,默认10 (required)
     * @param saasId saasId (required)
     * @param businessTypes 业务类型 (optional)
     * @param assetType 资产类型 默认POINT (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "pointLedgersV3",
        summary = "积分流水",
        tags = { "MemberV3" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultPointLedgerList.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v3/member/points/ledgers/{type}",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultPointLedgerList> pointLedgersV3(
        @Parameter(name = "type", description = "积分操作类型(transfer_in：获得积分,transfer_out：消耗积分,all：查询全部流水信息)", required = true) @PathVariable("type") String type,
        @NotNull @Parameter(name = "offset", description = "分页offset，默认0", required = true) @Valid @RequestParam(value = "offset", required = true, defaultValue = "0") Integer offset,
        @NotNull @Parameter(name = "limit", description = "分页limit,默认10", required = true) @Valid @RequestParam(value = "limit", required = true, defaultValue = "10") Integer limit,
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "businessTypes", description = "业务类型") @Valid @RequestParam(value = "businessTypes", required = false) List<String> businessTypes,
        @Parameter(name = "assetType", description = "资产类型 默认POINT") @Valid @RequestParam(value = "assetType", required = false) String assetType
    ) {
        return getDelegate().pointLedgersV3(type, offset, limit, saasId, businessTypes, assetType);
    }


    /**
     * GET /v3/member/top/ladder : 积分天梯top榜
     * 积分天梯top榜
     *
     * @param saasId saasId (required)
     * @param idToken idToken (optional)
     * @param handleName osp handleName (optional)
     * @param appId osp appId (optional)
     * @param chainId osp chainId (optional)
     * @param assetType 资产类型 默认 POINT (optional, default to POINT)
     * @param limit  (optional, default to 100)
     * @param strategy 排序策略，fold：相同分数排名相同， unfold(默认)：相同分数排名不同 (optional, default to unfold)
     * @param businessType ladder business type (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "topLadder",
        summary = "积分天梯top榜",
        tags = { "MemberV3" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultTopLadder.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v3/member/top/ladder",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultTopLadder> topLadder(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "idToken", description = "idToken") @RequestHeader(value = "idToken", required = false) String idToken,
        @Parameter(name = "handleName", description = "osp handleName") @Valid @RequestParam(value = "handleName", required = false) String handleName,
        @Parameter(name = "appId", description = "osp appId") @Valid @RequestParam(value = "appId", required = false) String appId,
        @Parameter(name = "chainId", description = "osp chainId") @Valid @RequestParam(value = "chainId", required = false) String chainId,
        @Parameter(name = "assetType", description = "资产类型 默认 POINT") @Valid @RequestParam(value = "assetType", required = false, defaultValue = "POINT") String assetType,
        @Parameter(name = "limit", description = "") @Valid @RequestParam(value = "limit", required = false, defaultValue = "100") Integer limit,
        @Parameter(name = "strategy", description = "排序策略，fold：相同分数排名相同， unfold(默认)：相同分数排名不同") @Valid @RequestParam(value = "strategy", required = false, defaultValue = "unfold") String strategy,
        @Parameter(name = "businessType", description = "ladder business type") @Valid @RequestParam(value = "businessType", required = false) String businessType
    ) {
        return getDelegate().topLadder(saasId, idToken, handleName, appId, chainId, assetType, limit, strategy, businessType);
    }


    /**
     * GET /v3/member/top/rank : 积分排行榜
     * 积分排行榜
     *
     * @param saasId saasId (required)
     * @param assetType 资产类型 默认 POINT (optional, default to POINT)
     * @param assetBusinessType 业务类型 (optional)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "topRank",
        summary = "积分排行榜",
        tags = { "MemberV3" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultTopRank.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/v3/member/top/rank",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultTopRank> topRank(
        @Parameter(name = "saas_id", description = "saasId", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @Parameter(name = "assetType", description = "资产类型 默认 POINT") @Valid @RequestParam(value = "assetType", required = false, defaultValue = "POINT") String assetType,
        @Parameter(name = "assetBusinessType", description = "业务类型") @Valid @RequestParam(value = "assetBusinessType", required = false) String assetBusinessType
    ) {
        return getDelegate().topRank(saasId, assetType, assetBusinessType);
    }

}
