package com.kikitrade.member.generated.model.s2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * 排行榜数据
 */

@Schema(name = "LadderItem", description = "排行榜数据")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class LadderItem {

  @JsonProperty("level")
  private Integer level;

  @JsonProperty("customerId")
  private String customerId;

  @JsonProperty("nickName")
  private String nickName;

  @JsonProperty("icon")
  private String icon;

  @JsonProperty("rank")
  private Integer rank;

  @JsonProperty("point")
  private BigDecimal point;

  public LadderItem level(Integer level) {
    this.level = level;
    return this;
  }

  /**
   * 徽章等级
   * @return level
  */
  
  @Schema(name = "level", description = "徽章等级", required = false)
  public Integer getLevel() {
    return level;
  }

  public void setLevel(Integer level) {
    this.level = level;
  }

  public LadderItem customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

  /**
   * 用户ID
   * @return customerId
  */
  
  @Schema(name = "customerId", example = "10023123123221111", description = "用户ID", required = false)
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public LadderItem nickName(String nickName) {
    this.nickName = nickName;
    return this;
  }

  /**
   * Get nickName
   * @return nickName
  */
  
  @Schema(name = "nickName", required = false)
  public String getNickName() {
    return nickName;
  }

  public void setNickName(String nickName) {
    this.nickName = nickName;
  }

  public LadderItem icon(String icon) {
    this.icon = icon;
    return this;
  }

  /**
   * Get icon
   * @return icon
  */
  
  @Schema(name = "icon", required = false)
  public String getIcon() {
    return icon;
  }

  public void setIcon(String icon) {
    this.icon = icon;
  }

  public LadderItem rank(Integer rank) {
    this.rank = rank;
    return this;
  }

  /**
   * 排名
   * @return rank
  */
  
  @Schema(name = "rank", example = "1", description = "排名", required = false)
  public Integer getRank() {
    return rank;
  }

  public void setRank(Integer rank) {
    this.rank = rank;
  }

  public LadderItem point(BigDecimal point) {
    this.point = point;
    return this;
  }

  /**
   * 积分数量
   * @return point
  */
  @Valid 
  @Schema(name = "point", example = "100", description = "积分数量", required = false)
  public BigDecimal getPoint() {
    return point;
  }

  public void setPoint(BigDecimal point) {
    this.point = point;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LadderItem ladderItem = (LadderItem) o;
    return Objects.equals(this.level, ladderItem.level) &&
        Objects.equals(this.customerId, ladderItem.customerId) &&
        Objects.equals(this.nickName, ladderItem.nickName) &&
        Objects.equals(this.icon, ladderItem.icon) &&
        Objects.equals(this.rank, ladderItem.rank) &&
        Objects.equals(this.point, ladderItem.point);
  }

  @Override
  public int hashCode() {
    return Objects.hash(level, customerId, nickName, icon, rank, point);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LadderItem {\n");
    sb.append("    level: ").append(toIndentedString(level)).append("\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    nickName: ").append(toIndentedString(nickName)).append("\n");
    sb.append("    icon: ").append(toIndentedString(icon)).append("\n");
    sb.append("    rank: ").append(toIndentedString(rank)).append("\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

