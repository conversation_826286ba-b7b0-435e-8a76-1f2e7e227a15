package com.kikitrade.member.generated.model.s2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.member.generated.model.s2.Ladder;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * LadderVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class LadderVO {

  @JsonProperty("myLadder")
  private Ladder myLadder;

  @JsonProperty("ladders")
  @Valid
  private List<Ladder> ladders = null;

  public LadderVO myLadder(Ladder myLadder) {
    this.myLadder = myLadder;
    return this;
  }

  /**
   * Get myLadder
   * @return myLadder
  */
  @Valid 
  @Schema(name = "myLadder", required = false)
  public Ladder getMyLadder() {
    return myLadder;
  }

  public void setMyLadder(Ladder myLadder) {
    this.myLadder = myLadder;
  }

  public LadderVO ladders(List<Ladder> ladders) {
    this.ladders = ladders;
    return this;
  }

  public LadderVO addLaddersItem(Ladder laddersItem) {
    if (this.ladders == null) {
      this.ladders = new ArrayList<>();
    }
    this.ladders.add(laddersItem);
    return this;
  }

  /**
   * Get ladders
   * @return ladders
  */
  @Valid 
  @Schema(name = "ladders", required = false)
  public List<Ladder> getLadders() {
    return ladders;
  }

  public void setLadders(List<Ladder> ladders) {
    this.ladders = ladders;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LadderVO ladderVO = (LadderVO) o;
    return Objects.equals(this.myLadder, ladderVO.myLadder) &&
        Objects.equals(this.ladders, ladderVO.ladders);
  }

  @Override
  public int hashCode() {
    return Objects.hash(myLadder, ladders);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LadderVO {\n");
    sb.append("    myLadder: ").append(toIndentedString(myLadder)).append("\n");
    sb.append("    ladders: ").append(toIndentedString(ladders)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

