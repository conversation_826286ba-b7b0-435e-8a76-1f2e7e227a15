package com.kikitrade.member.generated.model.v3;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.member.generated.model.v3.EdgenPointPointsInner;
import java.util.ArrayList;
import java.util.List;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * EdgenPoint
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class EdgenPoint {

  @JsonProperty("points")
  @Valid
  private List<EdgenPointPointsInner> points = null;

  public EdgenPoint points(List<EdgenPointPointsInner> points) {
    this.points = points;
    return this;
  }

  public EdgenPoint addPointsItem(EdgenPointPointsInner pointsItem) {
    if (this.points == null) {
      this.points = new ArrayList<>();
    }
    this.points.add(pointsItem);
    return this;
  }

  /**
   * Get points
   * @return points
  */
  @Valid 
  @Schema(name = "points", required = false)
  public List<EdgenPointPointsInner> getPoints() {
    return points;
  }

  public void setPoints(List<EdgenPointPointsInner> points) {
    this.points = points;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EdgenPoint edgenPoint = (EdgenPoint) o;
    return Objects.equals(this.points, edgenPoint.points);
  }

  @Override
  public int hashCode() {
    return Objects.hash(points);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EdgenPoint {\n");
    sb.append("    points: ").append(toIndentedString(points)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

