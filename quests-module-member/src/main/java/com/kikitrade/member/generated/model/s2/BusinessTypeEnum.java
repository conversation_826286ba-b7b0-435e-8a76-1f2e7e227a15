package com.kikitrade.member.generated.model.s2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonValue;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * businessType enum
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public enum BusinessTypeEnum {
  
  USER_BET("USER_BET"),
  
  CLAIM_TICKET("CLAIM_TICKET"),
  
  BUY_OJO("BUY_OJO"),
  
  WITHDRAW_OJO("WITHDRAW_OJO");

  private String value;

  BusinessTypeEnum(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  @JsonCreator
  public static BusinessTypeEnum fromValue(String value) {
    for (BusinessTypeEnum b : BusinessTypeEnum.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }
}

