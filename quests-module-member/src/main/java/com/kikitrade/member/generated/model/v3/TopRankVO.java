package com.kikitrade.member.generated.model.v3;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * TopRankVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TopRankVO {

  @JsonProperty("customerId")
  private String customerId;

  @JsonProperty("rank")
  private Integer rank;

  @JsonProperty("rankPercent")
  private String rankPercent;

  @JsonProperty("point")
  private BigDecimal point;

  public TopRankVO customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

  /**
   * 用户ID
   * @return customerId
  */
  
  @Schema(name = "customerId", example = "10023123123221111", description = "用户ID", required = false)
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public TopRankVO rank(Integer rank) {
    this.rank = rank;
    return this;
  }

  /**
   * 排名
   * @return rank
  */
  
  @Schema(name = "rank", example = "1", description = "排名", required = false)
  public Integer getRank() {
    return rank;
  }

  public void setRank(Integer rank) {
    this.rank = rank;
  }

  public TopRankVO rankPercent(String rankPercent) {
    this.rankPercent = rankPercent;
    return this;
  }

  /**
   * 排名百分比
   * @return rankPercent
  */
  
  @Schema(name = "rankPercent", description = "排名百分比", required = false)
  public String getRankPercent() {
    return rankPercent;
  }

  public void setRankPercent(String rankPercent) {
    this.rankPercent = rankPercent;
  }

  public TopRankVO point(BigDecimal point) {
    this.point = point;
    return this;
  }

  /**
   * 积分数量
   * @return point
  */
  @Valid 
  @Schema(name = "point", example = "100", description = "积分数量", required = false)
  public BigDecimal getPoint() {
    return point;
  }

  public void setPoint(BigDecimal point) {
    this.point = point;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TopRankVO topRankVO = (TopRankVO) o;
    return Objects.equals(this.customerId, topRankVO.customerId) &&
        Objects.equals(this.rank, topRankVO.rank) &&
        Objects.equals(this.rankPercent, topRankVO.rankPercent) &&
        Objects.equals(this.point, topRankVO.point);
  }

  @Override
  public int hashCode() {
    return Objects.hash(customerId, rank, rankPercent, point);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TopRankVO {\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    rank: ").append(toIndentedString(rank)).append("\n");
    sb.append("    rankPercent: ").append(toIndentedString(rankPercent)).append("\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

