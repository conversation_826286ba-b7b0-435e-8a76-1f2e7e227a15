/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (6.0.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.kikitrade.member.generated.api.s2;

import com.kikitrade.member.generated.model.s2.TransferOutRequest;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.member.generated.model.s2.WebResultLadder;
import com.kikitrade.member.generated.model.s2.WebResultTopLadder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
@Validated
@Tag(name = "MemberS2", description = "会员相关接口描述")
public interface MemberS2Api {

    default MemberS2ApiDelegate getDelegate() {
        return new MemberS2ApiDelegate() {};
    }

    /**
     * GET /member/ladder : 积分天梯排行榜
     * 获取积分排行榜
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cid 其他平台用户ID (required)
     * @param level 徽章等级 (optional)
     * @param limit 排行榜数量,范围：[0,200] (optional, default to 30)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "globalLadder",
        summary = "积分天梯排行榜",
        tags = { "MemberS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultLadder.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/member/ladder",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultLadder> globalLadder(
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "cid", description = "其他平台用户ID", required = true) @Valid @RequestParam(value = "cid", required = true) String cid,
        @Parameter(name = "level", description = "徽章等级") @Valid @RequestParam(value = "level", required = false) Integer level,
        @Parameter(name = "limit", description = "排行榜数量,范围：[0,200]") @Valid @RequestParam(value = "limit", required = false, defaultValue = "30") Integer limit
    ) {
        return getDelegate().globalLadder(authorization, saasId, cid, level, limit);
    }


    /**
     * GET /member/ladder/history : 积分历史排行榜
     * 获取积分历史排行榜
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cycle 查询赛季周数（必传） (required)
     * @param cid 其他平台用户ID (required)
     * @param level 徽章等级 (optional)
     * @param limit 排行榜数量,范围：[0,200] (optional, default to 30)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "historyLadder",
        summary = "积分历史排行榜",
        tags = { "MemberS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultLadder.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/member/ladder/history",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultLadder> historyLadder(
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "cycle", description = "查询赛季周数（必传）", required = true) @Valid @RequestParam(value = "cycle", required = true) String cycle,
        @NotNull @Parameter(name = "cid", description = "其他平台用户ID", required = true) @Valid @RequestParam(value = "cid", required = true) String cid,
        @Parameter(name = "level", description = "徽章等级") @Valid @RequestParam(value = "level", required = false) Integer level,
        @Parameter(name = "limit", description = "排行榜数量,范围：[0,200]") @Valid @RequestParam(value = "limit", required = false, defaultValue = "30") Integer limit
    ) {
        return getDelegate().historyLadder(authorization, saasId, cycle, cid, level, limit);
    }


    /**
     * GET /member/order/paid : 订单完成支付
     * 订单完成支付
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param orderId 订单id (required)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "orderPaid",
        summary = "订单完成支付",
        tags = { "MemberS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResult.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/member/order/paid",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResult> orderPaid(
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "orderId", description = "订单id", required = true) @Valid @RequestParam(value = "orderId", required = true) String orderId
    ) {
        return getDelegate().orderPaid(authorization, saasId, orderId);
    }


    /**
     * GET /member/top/ladder : 积分天梯top榜
     * 积分天梯top榜
     *
     * @param authorization  (required)
     * @param saasId  (required)
     * @param cid 业务方用户id (required)
     * @param assetType 资产类型 默认 POINT (optional, default to POINT)
     * @param ladderType 榜单类型 默认 History (optional, default to History)
     * @param limit  (optional, default to 100)
     * @param strategy 排序策略，fold(默认)：相同分数排名相同， unfold：相同分数排名不同 (optional, default to fold)
     * @return Successful operation (status code 200)
     */
    @Operation(
        operationId = "topLadder",
        summary = "积分天梯top榜",
        tags = { "MemberS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "Successful operation", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResultTopLadder.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/member/top/ladder",
        produces = { "application/json" }
    )
    default ResponseEntity<WebResultTopLadder> topLadder(
        @Parameter(name = "Authorization", description = "", required = true) @RequestHeader(value = "Authorization", required = true) String authorization,
        @Parameter(name = "saas_id", description = "", required = true) @RequestHeader(value = "saas_id", required = true) String saasId,
        @NotNull @Parameter(name = "cid", description = "业务方用户id", required = true) @Valid @RequestParam(value = "cid", required = true) String cid,
        @Parameter(name = "assetType", description = "资产类型 默认 POINT") @Valid @RequestParam(value = "assetType", required = false, defaultValue = "POINT") String assetType,
        @Parameter(name = "ladderType", description = "榜单类型 默认 History") @Valid @RequestParam(value = "ladderType", required = false, defaultValue = "History") String ladderType,
        @Parameter(name = "limit", description = "") @Valid @RequestParam(value = "limit", required = false, defaultValue = "100") Integer limit,
        @Parameter(name = "strategy", description = "排序策略，fold(默认)：相同分数排名相同， unfold：相同分数排名不同") @Valid @RequestParam(value = "strategy", required = false, defaultValue = "fold") String strategy
    ) {
        return getDelegate().topLadder(authorization, saasId, cid, assetType, ladderType, limit, strategy);
    }


    /**
     * POST /member/transferout : /member/transferout
     * 消耗积分
     *
     * @param transferOutRequest  (optional)
     * @return OK (status code 200)
     */
    @Operation(
        operationId = "transferOut",
        summary = "/member/transferout",
        tags = { "MemberS2" },
        responses = {
            @ApiResponse(responseCode = "200", description = "OK", content = {
                @Content(mediaType = "application/json", schema = @Schema(implementation = WebResult.class))
            })
        }
    )
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/member/transferout",
        produces = { "application/json" },
        consumes = { "application/json" }
    )
    default ResponseEntity<WebResult> transferOut(
        @Parameter(name = "TransferOutRequest", description = "") @Valid @RequestBody(required = false) TransferOutRequest transferOutRequest
    ) {
        return getDelegate().transferOut(transferOutRequest);
    }

}
