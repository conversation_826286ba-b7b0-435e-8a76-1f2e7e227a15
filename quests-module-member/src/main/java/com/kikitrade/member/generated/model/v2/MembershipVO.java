package com.kikitrade.member.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.math.BigDecimal;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * MembershipVO
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class MembershipVO {

  @JsonProperty("level")
  private String level;

  @JsonProperty("sort")
  private Integer sort;

  @JsonProperty("upgradePoint")
  private Integer upgradePoint;

  @JsonProperty("upgradeSuccessRate")
  private Integer upgradeSuccessRate;

  @JsonProperty("point")
  private BigDecimal point;

  @JsonProperty("maxCapacity")
  private Integer maxCapacity;

  @JsonProperty("capacity")
  private Integer capacity;

  @JsonProperty("productivity")
  private String productivity;

  @JsonProperty("expansionUsed")
  private Integer expansionUsed;

  @JsonProperty("expansionUnit")
  private Integer expansionUnit;

  public MembershipVO level(String level) {
    this.level = level;
    return this;
  }

  /**
   * 我的会员等级
   * @return level
  */
  
  @Schema(name = "level", description = "我的会员等级", required = false)
  public String getLevel() {
    return level;
  }

  public void setLevel(String level) {
    this.level = level;
  }

  public MembershipVO sort(Integer sort) {
    this.sort = sort;
    return this;
  }

  /**
   * 会员等级数字形式
   * @return sort
  */
  
  @Schema(name = "sort", description = "会员等级数字形式", required = false)
  public Integer getSort() {
    return sort;
  }

  public void setSort(Integer sort) {
    this.sort = sort;
  }

  public MembershipVO upgradePoint(Integer upgradePoint) {
    this.upgradePoint = upgradePoint;
    return this;
  }

  /**
   * 升级需要的积分
   * @return upgradePoint
  */
  
  @Schema(name = "upgradePoint", example = "100", description = "升级需要的积分", required = false)
  public Integer getUpgradePoint() {
    return upgradePoint;
  }

  public void setUpgradePoint(Integer upgradePoint) {
    this.upgradePoint = upgradePoint;
  }

  public MembershipVO upgradeSuccessRate(Integer upgradeSuccessRate) {
    this.upgradeSuccessRate = upgradeSuccessRate;
    return this;
  }

  /**
   * 升级成功概率
   * @return upgradeSuccessRate
  */
  
  @Schema(name = "upgradeSuccessRate", description = "升级成功概率", required = false)
  public Integer getUpgradeSuccessRate() {
    return upgradeSuccessRate;
  }

  public void setUpgradeSuccessRate(Integer upgradeSuccessRate) {
    this.upgradeSuccessRate = upgradeSuccessRate;
  }

  public MembershipVO point(BigDecimal point) {
    this.point = point;
    return this;
  }

  /**
   * 当前积分
   * @return point
  */
  @Valid 
  @Schema(name = "point", description = "当前积分", required = false)
  public BigDecimal getPoint() {
    return point;
  }

  public void setPoint(BigDecimal point) {
    this.point = point;
  }

  public MembershipVO maxCapacity(Integer maxCapacity) {
    this.maxCapacity = maxCapacity;
    return this;
  }

  /**
   * Get maxCapacity
   * @return maxCapacity
  */
  
  @Schema(name = "maxCapacity", required = false)
  public Integer getMaxCapacity() {
    return maxCapacity;
  }

  public void setMaxCapacity(Integer maxCapacity) {
    this.maxCapacity = maxCapacity;
  }

  public MembershipVO capacity(Integer capacity) {
    this.capacity = capacity;
    return this;
  }

  /**
   * Get capacity
   * @return capacity
  */
  
  @Schema(name = "capacity", required = false)
  public Integer getCapacity() {
    return capacity;
  }

  public void setCapacity(Integer capacity) {
    this.capacity = capacity;
  }

  public MembershipVO productivity(String productivity) {
    this.productivity = productivity;
    return this;
  }

  /**
   * 黄金生产效率，返回值默认追加了单位
   * @return productivity
  */
  
  @Schema(name = "productivity", description = "黄金生产效率，返回值默认追加了单位", required = false)
  public String getProductivity() {
    return productivity;
  }

  public void setProductivity(String productivity) {
    this.productivity = productivity;
  }

  public MembershipVO expansionUsed(Integer expansionUsed) {
    this.expansionUsed = expansionUsed;
    return this;
  }

  /**
   * 扩容积分消耗
   * @return expansionUsed
  */
  
  @Schema(name = "expansionUsed", description = "扩容积分消耗", required = false)
  public Integer getExpansionUsed() {
    return expansionUsed;
  }

  public void setExpansionUsed(Integer expansionUsed) {
    this.expansionUsed = expansionUsed;
  }

  public MembershipVO expansionUnit(Integer expansionUnit) {
    this.expansionUnit = expansionUnit;
    return this;
  }

  /**
   * 每次扩容增量
   * @return expansionUnit
  */
  
  @Schema(name = "expansionUnit", description = "每次扩容增量", required = false)
  public Integer getExpansionUnit() {
    return expansionUnit;
  }

  public void setExpansionUnit(Integer expansionUnit) {
    this.expansionUnit = expansionUnit;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MembershipVO membershipVO = (MembershipVO) o;
    return Objects.equals(this.level, membershipVO.level) &&
        Objects.equals(this.sort, membershipVO.sort) &&
        Objects.equals(this.upgradePoint, membershipVO.upgradePoint) &&
        Objects.equals(this.upgradeSuccessRate, membershipVO.upgradeSuccessRate) &&
        Objects.equals(this.point, membershipVO.point) &&
        Objects.equals(this.maxCapacity, membershipVO.maxCapacity) &&
        Objects.equals(this.capacity, membershipVO.capacity) &&
        Objects.equals(this.productivity, membershipVO.productivity) &&
        Objects.equals(this.expansionUsed, membershipVO.expansionUsed) &&
        Objects.equals(this.expansionUnit, membershipVO.expansionUnit);
  }

  @Override
  public int hashCode() {
    return Objects.hash(level, sort, upgradePoint, upgradeSuccessRate, point, maxCapacity, capacity, productivity, expansionUsed, expansionUnit);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MembershipVO {\n");
    sb.append("    level: ").append(toIndentedString(level)).append("\n");
    sb.append("    sort: ").append(toIndentedString(sort)).append("\n");
    sb.append("    upgradePoint: ").append(toIndentedString(upgradePoint)).append("\n");
    sb.append("    upgradeSuccessRate: ").append(toIndentedString(upgradeSuccessRate)).append("\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("    maxCapacity: ").append(toIndentedString(maxCapacity)).append("\n");
    sb.append("    capacity: ").append(toIndentedString(capacity)).append("\n");
    sb.append("    productivity: ").append(toIndentedString(productivity)).append("\n");
    sb.append("    expansionUsed: ").append(toIndentedString(expansionUsed)).append("\n");
    sb.append("    expansionUnit: ").append(toIndentedString(expansionUnit)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

