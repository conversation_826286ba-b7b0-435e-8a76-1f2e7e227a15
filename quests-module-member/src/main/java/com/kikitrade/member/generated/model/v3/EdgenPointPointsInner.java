package com.kikitrade.member.generated.model.v3;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * EdgenPointPointsInner
 */

@JsonTypeName("EdgenPoint_points_inner")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class EdgenPointPointsInner {

  @JsonProperty("point")
  private String point;

  @JsonProperty("pointPercentage")
  private String pointPercentage;

  @JsonProperty("handle")
  private String handle;

  @JsonProperty("userType")
  private Integer userType;

  public EdgenPointPointsInner point(String point) {
    this.point = point;
    return this;
  }

  /**
   * 积分数量
   * @return point
  */
  
  @Schema(name = "point", example = "100.2", description = "积分数量", required = false)
  public String getPoint() {
    return point;
  }

  public void setPoint(String point) {
    this.point = point;
  }

  public EdgenPointPointsInner pointPercentage(String pointPercentage) {
    this.pointPercentage = pointPercentage;
    return this;
  }

  /**
   * 积分占比
   * @return pointPercentage
  */
  
  @Schema(name = "pointPercentage", example = "0.2=20%", description = "积分占比", required = false)
  public String getPointPercentage() {
    return pointPercentage;
  }

  public void setPointPercentage(String pointPercentage) {
    this.pointPercentage = pointPercentage;
  }

  public EdgenPointPointsInner handle(String handle) {
    this.handle = handle;
    return this;
  }

  /**
   * twitter handle
   * @return handle
  */
  
  @Schema(name = "handle", example = "edgen", description = "twitter handle", required = false)
  public String getHandle() {
    return handle;
  }

  public void setHandle(String handle) {
    this.handle = handle;
  }

  public EdgenPointPointsInner userType(Integer userType) {
    this.userType = userType;
    return this;
  }

  /**
   * 用户类型 1=资深用户
   * @return userType
  */
  
  @Schema(name = "userType", example = "0", description = "用户类型 1=资深用户", required = false)
  public Integer getUserType() {
    return userType;
  }

  public void setUserType(Integer userType) {
    this.userType = userType;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EdgenPointPointsInner edgenPointPointsInner = (EdgenPointPointsInner) o;
    return Objects.equals(this.point, edgenPointPointsInner.point) &&
        Objects.equals(this.pointPercentage, edgenPointPointsInner.pointPercentage) &&
        Objects.equals(this.handle, edgenPointPointsInner.handle) &&
        Objects.equals(this.userType, edgenPointPointsInner.userType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(point, pointPercentage, handle, userType);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EdgenPointPointsInner {\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("    pointPercentage: ").append(toIndentedString(pointPercentage)).append("\n");
    sb.append("    handle: ").append(toIndentedString(handle)).append("\n");
    sb.append("    userType: ").append(toIndentedString(userType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

