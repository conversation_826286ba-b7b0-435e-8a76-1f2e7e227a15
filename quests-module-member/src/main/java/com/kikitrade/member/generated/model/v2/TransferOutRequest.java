package com.kikitrade.member.generated.model.v2;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.kikitrade.member.generated.model.v2.AssetTypeEnum;
import com.kikitrade.member.generated.model.v2.BusinessTypeEnum;
import org.openapitools.jackson.nullable.JsonNullable;
import java.time.OffsetDateTime;
import javax.validation.Valid;
import javax.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import javax.annotation.Generated;

/**
 * 积分消耗请求
 */

@Schema(name = "TransferOutRequest", description = "积分消耗请求")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class TransferOutRequest {

  @JsonProperty("businessType")
  private BusinessTypeEnum businessType;

  @JsonProperty("businessId")
  private String businessId;

  @JsonProperty("type")
  private AssetTypeEnum type;

  @JsonProperty("amount")
  private String amount;

  public TransferOutRequest businessType(BusinessTypeEnum businessType) {
    this.businessType = businessType;
    return this;
  }

  /**
   * Get businessType
   * @return businessType
  */
  @Valid 
  @Schema(name = "businessType", required = false)
  public BusinessTypeEnum getBusinessType() {
    return businessType;
  }

  public void setBusinessType(BusinessTypeEnum businessType) {
    this.businessType = businessType;
  }

  public TransferOutRequest businessId(String businessId) {
    this.businessId = businessId;
    return this;
  }

  /**
   * 业务id
   * @return businessId
  */
  
  @Schema(name = "businessId", description = "业务id", required = false)
  public String getBusinessId() {
    return businessId;
  }

  public void setBusinessId(String businessId) {
    this.businessId = businessId;
  }

  public TransferOutRequest type(AssetTypeEnum type) {
    this.type = type;
    return this;
  }

  /**
   * Get type
   * @return type
  */
  @Valid 
  @Schema(name = "type", required = false)
  public AssetTypeEnum getType() {
    return type;
  }

  public void setType(AssetTypeEnum type) {
    this.type = type;
  }

  public TransferOutRequest amount(String amount) {
    this.amount = amount;
    return this;
  }

  /**
   * 数量
   * @return amount
  */
  
  @Schema(name = "amount", description = "数量", required = false)
  public String getAmount() {
    return amount;
  }

  public void setAmount(String amount) {
    this.amount = amount;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TransferOutRequest transferOutRequest = (TransferOutRequest) o;
    return Objects.equals(this.businessType, transferOutRequest.businessType) &&
        Objects.equals(this.businessId, transferOutRequest.businessId) &&
        Objects.equals(this.type, transferOutRequest.type) &&
        Objects.equals(this.amount, transferOutRequest.amount);
  }

  @Override
  public int hashCode() {
    return Objects.hash(businessType, businessId, type, amount);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TransferOutRequest {\n");
    sb.append("    businessType: ").append(toIndentedString(businessType)).append("\n");
    sb.append("    businessId: ").append(toIndentedString(businessId)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    amount: ").append(toIndentedString(amount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

