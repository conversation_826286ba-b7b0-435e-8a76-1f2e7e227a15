package com.kikitrade.member.delegate;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.asset.api.RemoteAssetOperateService;
import com.kikitrade.asset.api.RemoteAssetService;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.asset.model.constant.AssetType;
import com.kikitrade.asset.model.exception.AssetException;
import com.kikitrade.asset.model.request.AssetTransferOutRequest;
import com.kikitrade.asset.model.response.AssetOperateResponse;
import com.kikitrade.asset.model.response.AssetResponseCode;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.kweb.config.QuestWebProperties;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.kweb.utils.ResponseEntityUtil;
import com.kikitrade.member.api.RemoteMemberService;
import com.kikitrade.member.generated.api.s2.MemberS2ApiDelegate;
import com.kikitrade.member.generated.model.s2.*;
import com.kikitrade.member.model.QuestsLadderDTO;
import com.kikitrade.member.model.TopLadderDTO;
import com.kikitrade.member.model.request.QuestLadderRequest;
import com.kikitrade.member.model.request.TopLadderRequest;
import com.kikitrade.member.model.response.QuestLadderResponse;
import com.kikitrade.member.model.response.ResponseCode;
import com.kikitrade.order.model.OrderEventDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/5/24 15:49
 */
@Service
@Slf4j
public class MemberS2ApiDelegateImpl implements MemberS2ApiDelegate {

    @DubboReference
    private RemoteMemberService remoteMemberService;
    @Resource
    private OnsProducer onsProducer;
    @Resource
    private QuestWebProperties questWebProperties;
    @DubboReference
    private RemoteAssetOperateService remoteAssetOperateService;
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;

    @Override
    public ResponseEntity<WebResultTopLadder> topLadder(String authorization, String saasId, String cid, String assetType, String ladderType, Integer limit, String strategy) {
        TopLadderRequest topLadderRequest = new TopLadderRequest();
        topLadderRequest.setSaasId(saasId);
        topLadderRequest.setCid(cid);
        topLadderRequest.setAssetType(assetType);
        topLadderRequest.setLadderType(ladderType);
        topLadderRequest.setLimit(limit);
        topLadderRequest.setStrategy(strategy == null ? "fold" : strategy);
        TopLadderDTO ladderDTO = remoteMemberService.topLadder(topLadderRequest);
        LadderVO ladderVO = new LadderVO();
        ladderVO.setMyLadder(BeanUtil.copyProperties(ladderDTO.getMyLadder(), Ladder::new));
        ladderVO.setLadders(BeanUtil.copyProperties(ladderDTO.getLadders(), Ladder::new));

        WebResultTopLadder result = new WebResultTopLadder();
        result.setResponseEnum(WebResponseEnum.SUCCESS);
        result.setObj(ladderVO);
        return ResponseEntityUtil.result(result);
    }

    @Override
    public ResponseEntity<WebResult> orderPaid(String authorization, String saasId, String orderId) {
        OrderEventDTO orderEventDTO = new OrderEventDTO();
        orderEventDTO.setOrderId(orderId);
        SendResult send = onsProducer.send(questWebProperties.getOrderStatusTopic(), JSON.toJSONString(orderEventDTO));
        log.info("orderPaid send topic:{},{},{}", send.getTopic(), send.getMessageId(), orderId);
        return ResponseEntityUtil.result(WebResponseEnum.SUCCESS);
    }

    /**
     * POST /member/transferout : /member/transferout
     * 消耗积分
     *
     * @param transferOutRequest (optional)
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<WebResult> transferOut(TransferOutRequest transferOutRequest) {
        try{
            log.info("s2s transferOut: {}", transferOutRequest);
            if (Objects.isNull(transferOutRequest.getType())) {
                transferOutRequest.setType(AssetTypeEnum.POINT);
            }
            CustomerBindDTO customerBindDTO = remoteCustomerBindService.findById(transferOutRequest.getSaasId(), transferOutRequest.getCustomerId());
            if(customerBindDTO == null){
                return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_NOT_FOUND);
            }
            AssetTransferOutRequest request = new AssetTransferOutRequest();
            BeanUtil.copyProperties(transferOutRequest,request);
            request.setCustomerId(customerBindDTO.getUid());
            request.setSaasId(customerBindDTO.getSaasId());
            request.setBusinessType(AssetBusinessType.valueOf(transferOutRequest.getBusinessType()));
            request.setType(AssetType.valueOf(transferOutRequest.getType().name()));
            request.setAmount(new BigDecimal(transferOutRequest.getAmount()));
            AssetOperateResponse response = remoteAssetOperateService.transferOut(request);
            AssetResponseCode code = response.getCode();
            if(code.isSuccess()){
                return ResponseEntityUtil.result(WebResponseEnum.SUCCESS);
            }
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }catch (AssetException ex){
            log.error("s2s transferOut error:{}", transferOutRequest, ex);
            if(ex.getCode() == AssetResponseCode.INSUFFICIENT_BALANCE){
                log.error("transferOut error:{}", transferOutRequest, ex);
                return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_INSUFFICIENT_BALANCE);
            }
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }catch (Exception ex){
            log.error("s2s transferOut error:{}", transferOutRequest, ex);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
    }

    /**
     * 徽章排行榜
     *
     * @param saasId (required)
     * @param level  徽章等级 (optional)
     * @param limit  排行榜数量,范围：[0,200] (optional, default to 30)
     * @return
     */
    @Override
    public ResponseEntity<WebResultLadder> globalLadder(String authorization, String saasId, String cid, Integer level, Integer limit) {
        try {
            //本赛季开始时间
            Map<String, QuestWebProperties.SeasonTime> zeekSeasonTime = questWebProperties.getZeekSeasonTime();
            Optional<Map.Entry<String, QuestWebProperties.SeasonTime>> optionalEntry = zeekSeasonTime.entrySet().stream().filter(s -> OffsetDateTime.now().toEpochSecond() >= s.getValue().getStartTime() && s.getValue().getEndTime() > OffsetDateTime.now().toEpochSecond()).findFirst();
            if (optionalEntry.isEmpty()) {
                log.error("globalLadder fail, not to season time");
                return ResponseEntityUtil.result(WebResponseEnum.QUESTS_LADDER_CALCULATING);
            }
            Map.Entry<String, QuestWebProperties.SeasonTime> seasonTimeEntry = optionalEntry.get();
            LocalDateTime seasonStartTime = new Date(seasonTimeEntry.getValue().getStartTime() * 1000).toInstant().atOffset(ZoneOffset.UTC).toLocalDate().atTime(0, 0, 0);
            long firstLadderTime = Date.from(seasonStartTime.with(TemporalAdjusters.next(DayOfWeek.THURSDAY)).toInstant(ZoneOffset.UTC)).getTime();

            //计算获取cycle
            long c = 1;
            if (OffsetDateTime.now().toEpochSecond() * 1000L >= firstLadderTime) {
                c = ((OffsetDateTime.now().toEpochSecond() * 1000L - firstLadderTime) / 1000 / 60 / 60 / 24 / 7) + 2;
            }
            String cycle = String.format("%02d", c);

            QuestLadderRequest request = new QuestLadderRequest();
            CustomerBindDTO bindDTO = remoteCustomerBindService.findById(saasId, cid);
            if (bindDTO == null) {
                log.error("s2TasksVerify fail, CustomerBindDTO not exist, cid={}", cid);
                return ResponseEntityUtil.result(WebResponseEnum.AUTH_CODE_INVALID);
            }
            request.setCustomerId(bindDTO.getUid());
            request.setCid(bindDTO.getCid());
            request.setSaasId(saasId);
            request.setSeason(getCurrentSeasonName());
            request.setCycle(cycle);
            request.setLevel(level);
            request.setLimit(limit);
            log.info("globalLadder:{}", request);
            QuestLadderResponse response = remoteMemberService.questsLadders(request);
            GlobalLadder ladder = new GlobalLadder();
            //排行榜状态
            if (response.getCode() == ResponseCode.QUESTS_LADDER_CALCULATING) {
                return ResponseEntityUtil.result(WebResponseEnum.QUESTS_LADDER_CALCULATING);
            }
            ladder.setCalculating(response.getMyLadder().getCalculating());
            Date now = new Date();
            LocalDateTime last;
            last = now.toInstant().atOffset(ZoneOffset.UTC).toLocalDate().atTime(0, 0, 0);
            long time = Date.from(last.with(TemporalAdjusters.next(DayOfWeek.THURSDAY)).toInstant(ZoneOffset.UTC)).getTime();
            if (time > seasonTimeEntry.getValue().getEndTime() * 1000) {
                time = seasonTimeEntry.getValue().getEndTime() * 1000;
            }

            ladder.setEndTime(time);
            ladder.setCycle((int) c);
            ladder.setMyLadder(toResponse(response.getMyLadder()));
            ladder.setGlobalLadder(response.getLadders().stream().map(this::toResponse).collect(Collectors.toList()));
            WebResultLadder webResult = new WebResultLadder();
            webResult.setResponseEnum(WebResponseEnum.SUCCESS);
            webResult.setObj(ladder);
            return ResponseEntityUtil.result(webResult);
        } catch (Exception ex) {
            log.error("globalLadder exception", ex);
            return null;
        }
    }

    @Override
    public ResponseEntity<WebResultLadder> historyLadder(String authorization, String saasId, String cycleString, String cid,
                                                         Integer level, Integer limit) {
        WebResultLadder webResult = new WebResultLadder();
        try {
            //第一次结算时间
            int cycle = Integer.parseInt(cycleString);
            if (cycle < 1) {
                log.error("historyLadder request cycle error, cycle={}", cycle);
                webResult.setResponseEnum(WebResponseEnum.QUESTS_LADDER_CALCULATING);
                return ResponseEntityUtil.result(webResult);
            }
            CustomerBindDTO bindDTO = remoteCustomerBindService.findById(saasId, cid);
            if (bindDTO == null) {
                log.error("s2TasksVerify fail, CustomerBindDTO not exist, cid={}", cid);
                return ResponseEntityUtil.result(WebResponseEnum.AUTH_CODE_INVALID);
            }
            String currentSeasonName = getCurrentSeasonName();
            log.info("historyLadder request. customerId:{}, cid:{}, currentSeasonName:{}", bindDTO.getUid(),
                    bindDTO.getCid(), currentSeasonName);
            QuestLadderResponse response =
                    getRemoteHistoryLadders(bindDTO, saasId, currentSeasonName, cycleString, level);
            if (response == null || response.getCode() == ResponseCode.QUESTS_LADDER_CALCULATING) {
                webResult.setResponseEnum(WebResponseEnum.QUESTS_LADDER_CALCULATING);
                return ResponseEntityUtil.result(webResult);
            }
            //本赛季开始时间
            Map<String, QuestWebProperties.SeasonTime> zeekSeasonTime = questWebProperties.getZeekSeasonTime();
            Optional<Map.Entry<String, QuestWebProperties.SeasonTime>> optionalEntry = zeekSeasonTime.entrySet().stream().filter(s -> OffsetDateTime.now().toEpochSecond() >= s.getValue().getStartTime() && s.getValue().getEndTime() > OffsetDateTime.now().toEpochSecond()).findFirst();
            if (optionalEntry.isEmpty()) {
                log.error("historyLadder fail, not to season time");
                return ResponseEntityUtil.result(WebResponseEnum.QUESTS_LADDER_CALCULATING);
            }
            Map.Entry<String, QuestWebProperties.SeasonTime> seasonTimeEntry = optionalEntry.get();
            LocalDateTime seasonStartTime = new Date(seasonTimeEntry.getValue().getStartTime() * 1000).toInstant().atOffset(ZoneOffset.UTC).toLocalDate().atTime(0, 0, 0);
            long firstLadderTime = Date.from(seasonStartTime.with(TemporalAdjusters.next(DayOfWeek.THURSDAY)).toInstant(ZoneOffset.UTC)).getTime();

            GlobalLadder ladder = new GlobalLadder();

            ladder.setStartTime(cycle == 1 ? seasonStartTime.toEpochSecond(ZoneOffset.UTC) * 1000 : firstLadderTime + ((cycle - 2) * TimeUnit.DAYS.toMillis(7)));
            ladder.setEndTime(cycle == 1 ? firstLadderTime : firstLadderTime + ((cycle - 1) * TimeUnit.DAYS.toMillis(7)));
            ladder.setCycle(cycle);
            ladder.setCalculating(false);
            ladder.setMyLadder(toResponse(response.getMyLadder()));
            ladder.setGlobalLadder(response.getLadders().stream().map(this::toResponse).collect(Collectors.toList()));
            webResult.setResponseEnum(WebResponseEnum.SUCCESS);
            webResult.setObj(ladder);
            return ResponseEntityUtil.result(webResult);
        } catch (Exception ex) {
            log.error("historyLadder exception", ex);
            webResult.setResponseEnum(WebResponseEnum.SYSTEM_ERROR);
            return null;
        }
    }

    private String getCurrentSeasonName() {
        Map<String, QuestWebProperties.SeasonTime> zeekSeasonTime = questWebProperties.getZeekSeasonTime();
        Optional<Map.Entry<String, QuestWebProperties.SeasonTime>> optionalEntry = zeekSeasonTime.entrySet().stream().filter(s -> OffsetDateTime.now().toEpochSecond() >= s.getValue().getStartTime() && s.getValue().getEndTime() > OffsetDateTime.now().toEpochSecond()).findFirst();
        if (optionalEntry.isEmpty()) {
            return "season1";
        }
        return optionalEntry.get().getKey();

    }

    private LadderItem toResponse(QuestsLadderDTO ladderDTO) {
        LadderItem item = new LadderItem();
        item.setCustomerId(ladderDTO.getCustomerId());
        item.setLevel(ladderDTO.getLevel());
        item.setPoint(ladderDTO.getPoint().setScale(1, RoundingMode.DOWN));
        item.setRank(ladderDTO.getRank());
        item.setIcon(ladderDTO.getIcon());
        item.setNickName(ladderDTO.getNickName());
        return item;
    }

    private QuestLadderResponse getRemoteHistoryLadders(CustomerBindDTO customer, String saasId, String season, String cycle, Integer level) {
        QuestLadderRequest request = new QuestLadderRequest();
        String cycleString = String.format("%02d", Integer.parseInt(cycle));
        if (season.equals("season0")) {
            //历史数据
            cycleString = String.format("2024%02d", Integer.parseInt(cycle));
        }
        request.setCustomerId(customer.getUid());
        request.setCid(customer.getCid());
        request.setSaasId(saasId);
        request.setSeason(season);
        request.setCycle(cycleString);
        request.setLevel(level);
        log.info("getRemoteHistoryLadders request:{}", JSON.toJSONString(request));
        QuestLadderResponse response = remoteMemberService.questsHistoryLadders(request);
        log.info("getRemoteHistoryLadders response:{}", JSON.toJSONString(response));
        return remoteMemberService.questsHistoryLadders(request);
    }
}
