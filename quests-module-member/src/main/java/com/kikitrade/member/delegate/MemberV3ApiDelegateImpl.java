package com.kikitrade.member.delegate;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.kikitrade.asset.api.RemoteAssetService;
import com.kikitrade.asset.model.AssetDTO;
import com.kikitrade.asset.model.AssetDailySnapshotDTO;
import com.kikitrade.asset.model.AssetLedgerReadonlyDTO;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.asset.model.constant.AssetCategory;
import com.kikitrade.asset.model.constant.AssetOperateType;
import com.kikitrade.asset.model.constant.AssetType;
import com.kikitrade.asset.model.request.AssetDailySnapshotRequest;
import com.kikitrade.asset.model.request.AssetLedgersListRequest;
import com.kikitrade.asset.model.response.AssetDailyIncrementResponse;
import com.kikitrade.asset.model.response.AssetLaddersResponse;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.kweb.config.QuestWebProperties;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.interceptor.CustomerHolder;
import com.kikitrade.kweb.model.customer.QCustomer;
import com.kikitrade.kweb.service.KwebBaseService;
import com.kikitrade.kweb.utils.ResponseEntityUtil;
import com.kikitrade.member.api.RemoteMemberService;
import com.kikitrade.member.generated.api.v3.MemberV3ApiDelegate;
import com.kikitrade.member.generated.model.v3.*;
import com.kikitrade.member.model.EdgenLadderDTO;
import com.kikitrade.member.model.QuestsLadderDTO;
import com.kikitrade.member.model.TopLadderDTO;
import com.kikitrade.member.model.TopRankDTO;
import com.kikitrade.member.model.request.EdgenLadderRequest;
import com.kikitrade.member.model.request.QuestLadderRequest;
import com.kikitrade.member.model.request.TopLadderRequest;
import com.kikitrade.member.model.response.QuestLadderResponse;
import com.kikitrade.member.model.response.ResponseCode;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.kikitrade.member.model.constant.MemberConstant;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/9/10 21:12
 */
@Service
@Slf4j
public class MemberV3ApiDelegateImpl implements MemberV3ApiDelegate {

    @Resource
    private QuestWebProperties questWebProperties;
    @DubboReference
    private RemoteMemberService remoteMemberService;
    @DubboReference
    private RemoteAssetService remoteAssetService;
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;
    @Resource
    private KwebBaseService kwebBaseService;

    @Override
    public ResponseEntity<WebResultTopRank> topRank(String saasId, String assetType, String assetBusinessType) {
        TopRankDTO topRankDTO = null;
        CustomerBindDTO customerBindDTO = CustomerHolder.qcustomer();
        if(assetBusinessType != null){
            topRankDTO = remoteMemberService.topRankBusiness(saasId, assetType, customerBindDTO.getUid(), assetBusinessType);
        }else{
            topRankDTO = remoteMemberService.topRank(saasId, assetType, customerBindDTO.getUid());
        }
        TopRankVO rankVO = new TopRankVO();
        rankVO.setCustomerId(customerBindDTO.getUid());
        rankVO.setRank(topRankDTO.getRankValue());
        rankVO.setRankPercent(topRankDTO.getRankPercent());
        rankVO.setPoint(topRankDTO.getPoint());

        WebResultTopRank webResult = new WebResultTopRank();
        webResult.setResponseEnum(WebResponseEnum.SUCCESS);
        webResult.setObj(rankVO);
        return ResponseEntityUtil.result(webResult);
    }

    /**
     * GET /v3/member/bread/ladder : 积分天梯排行榜
     * 获取积分排行榜
     *
     * @param saasId (required)
     * @param season (required)
     * @param level  徽章等级 (optional)
     * @param limit  排行榜数量,范围：[0,200] (optional, default to 30)
     */
    @Override
    public ResponseEntity<WebResultLadder> globalLadder(String saasId, String season, Integer level, Integer limit) {
        Map<String, QuestWebProperties.SeasonTime> seasonTime = questWebProperties.getBreadSeasonTime();//单位s
        Optional<Map.Entry<String, QuestWebProperties.SeasonTime>> optionalEntry = seasonTime.entrySet().stream().filter(s -> OffsetDateTime.now().toEpochSecond() >= s.getValue().getStartTime() && s.getValue().getEndTime() > OffsetDateTime.now().toEpochSecond()).findFirst();
        if(optionalEntry.isEmpty()){
            return ResponseEntityUtil.result(WebResponseEnum.QUESTS_LADDER_CALCULATING);
        }

        //第一次结算时间
        Map.Entry<String, QuestWebProperties.SeasonTime> seasonTimeEntry = optionalEntry.get();
        LocalDateTime seasonStartTime = new Date(seasonTimeEntry.getValue().getStartTime() * 1000).toInstant().atOffset(ZoneOffset.UTC).toLocalDate().atTime(0, 0, 0);
        long firstLadderTime = Date.from(seasonStartTime.with(TemporalAdjusters.next(DayOfWeek.WEDNESDAY)).toInstant(ZoneOffset.UTC)).getTime();

        //计算获取cycle
        long c = 1;
        if(OffsetDateTime.now().toEpochSecond() * 1000L >= firstLadderTime){
            c = ((OffsetDateTime.now().toEpochSecond() * 1000L  - firstLadderTime)/1000 / 60 / 60 / 24 / 7) + 2;
        }
        String cycle = String.format("%02d", c);

        QuestLadderRequest request = new QuestLadderRequest();
        CustomerBindDTO qcustomer = CustomerHolder.qcustomer();
        request.setCustomerId(qcustomer.getUid());
        request.setCid(qcustomer.getCid());
        request.setSaasId(saasId);
        request.setSeason(seasonTimeEntry.getKey());
        request.setCycle(cycle);
        request.setLevel(level);
        request.setLimit(limit);
        log.info("globalLadder:{}", request);
        QuestLadderResponse response = remoteMemberService.questsLadders(request);
        Ladder ladder = new Ladder();
        //排行榜状态
        if(response.getCode() == ResponseCode.QUESTS_LADDER_CALCULATING){
            return ResponseEntityUtil.result(WebResponseEnum.QUESTS_LADDER_CALCULATING);
        }
        ladder.setCalculating(false);
        Date now = new Date();
        LocalDateTime last;
        last = now.toInstant().atOffset(ZoneOffset.UTC).toLocalDate().atTime(0, 0, 0);
        long time = Date.from(last.with(TemporalAdjusters.next(DayOfWeek.WEDNESDAY)).toInstant(ZoneOffset.UTC)).getTime();

        ladder.setEndTime(time);
        ladder.setCycle((int)c);
        ladder.setMyLadder(toResponse(response.getMyLadder()));
        ladder.setGlobalLadder(response.getLadders().stream().map(this::toResponse).collect(Collectors.toList()));
        WebResultLadder webResult = new WebResultLadder();
        webResult.setResponseEnum(WebResponseEnum.SUCCESS);
        webResult.setObj(ladder);
        return ResponseEntityUtil.result(webResult);
    }

    /**
     * GET /v3/member/bread/ladder/history : 积分历史排行榜
     * 获取积分历史排行榜
     *
     * @param saasId (required)
     * @param season 查询赛季（不传默认为当前赛季） (required)
     * @param cycle  查询赛季周数（必传） (required)
     * @param level  徽章等级 (optional)
     * @param limit  排行榜数量,范围：[0,200] (optional, default to 30)
     * @return Successful operation (status code 200)
     */
    @Override
    public ResponseEntity<WebResultLadder> historyLadder(String saasId, String season, String cycle, Integer level, Integer limit) {
        return MemberV3ApiDelegate.super.historyLadder(saasId, season, cycle, level, limit);
    }

    /**
     * GET /v3/member/points/ledgers/{type} : 积分流水
     * 积分流水-已领取|已使用
     *
     * @param type          积分操作类型(transfer_in：获得积分,transfer_out：消耗积分,all：查询全部流水信息) (required)
     * @param offset        分页offset，默认0 (required)
     * @param limit         分页limit,默认10 (required)
     * @param saasId        saasId (required)
     * @param businessTypes 业务类型 (optional)
     * @return Successful operation (status code 200)
     */
    @Override
    public ResponseEntity<WebResultPointLedgerList> pointLedgersV3(String type, Integer offset, Integer limit, String saasId, List<String> businessTypes, String assetType) {
        AssetType assetTypeEnum = AssetType.POINT;
        if (assetType != null) {
            assetTypeEnum = AssetType.valueOf(assetType);
        }
        QCustomer customer = CustomerHolder.qcustomer();
        WebResultPointLedgerList result = new WebResultPointLedgerList();
        PointLedgerList point = new PointLedgerList();
        AssetDTO asset = remoteAssetService.asset(saasId, customer.getUid(), assetTypeEnum, AssetCategory.NORMAL);
        point.setOriginalAvailable(asset.getAvailable());
        point.setAvailable(add(asset.getAssetType(), asset.getAvailable(), customer.getSaasId(), customer.getUid()));

        if(limit != null && limit > 0){
            AssetLedgersListRequest listRequest = new AssetLedgersListRequest();
            listRequest.setCustomerId(customer.getUid());
            listRequest.setAssetCategory(AssetCategory.NORMAL);
            listRequest.setSaasId(customer.getSaasId());
            if(!"all".equals(type)){
                listRequest.setAssetOperateType(AssetOperateType.valueOf(type.toUpperCase()));
            }
            listRequest.setAssetType(assetTypeEnum);
            if (!CollectionUtils.isEmpty(businessTypes)) {
                listRequest.setBusinessTypes(businessTypes.stream().map(AssetBusinessType::valueOf).collect(Collectors.toList()));
            }
            listRequest.setOffset(offset);
            listRequest.setLimit(limit);
            AssetLaddersResponse response = remoteAssetService.assetLadders(listRequest);
            List<AssetLedgerReadonlyDTO> assetLedgers = response.getAssetLedgers();
            List<PointLedger> pointLedgers = new ArrayList<>();
            for(AssetLedgerReadonlyDTO ledger : assetLedgers){
                PointLedger pointLedger = new PointLedger();
                pointLedger.setId(ledger.getId());
                pointLedger.setAmount(ledger.getAmount());
                pointLedger.setCreated(ledger.getCreated());
                pointLedger.setBusinessType(AssetBusinessType.valueOf(ledger.getBusinessType()).getDesc());
                pointLedger.setMenu(ledger.getMenu());
                pointLedger.setDescI18n(ledger.getDescI18n());
                pointLedger.setOperateType(ledger.getOperateType().intValue());
                pointLedgers.add(pointLedger);
            }
            point.setPointLedgers(pointLedgers);
        }

        result.setResponseEnum(WebResponseEnum.SUCCESS);
        result.setObj(point);
        return ResponseEntityUtil.result(result);
    }

    @Override
    public ResponseEntity<WebResultPointStaticList> pointDaily(String saasId, String idToken, String handleName, String appId, String chainId) {
        CustomerBindDTO customer;
        if(handleName != null && appId != null){
            customer = kwebBaseService.getCustomerByHandleName(saasId, appId, handleName, chainId);
        }else{
            customer = CustomerHolder.qcustomer();
        }
        if(customer == null){
            return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_NOT_FOUND);
        }
        WebResultPointStaticList result = new WebResultPointStaticList();
        PointStaticList staticList = new PointStaticList();

        AssetDTO asset = remoteAssetService.asset(customer.getSaasId(),customer.getUid(), AssetType.AURA, AssetCategory.NORMAL);

        AssetDailySnapshotRequest request = new AssetDailySnapshotRequest();
        request.setId(asset.getId());

        List<String> snapshotDates = IntStream.rangeClosed(1, 2)
                .mapToObj(i -> LocalDate.now().minusDays(i))
                .map(date -> date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .collect(Collectors.toList());

        request.setSnapshotDates(snapshotDates);
        List<AssetDailySnapshotDTO> yesterdaySnapshot = remoteAssetService.listAssetSnapShot(request);
        //每天的available
        Map<String, AssetDailySnapshotDTO> snapshotDTOMap = yesterdaySnapshot.stream().collect(Collectors.toMap(AssetDailySnapshotDTO::getDate, Function.identity()));
        //补全缺失的日期
        snapshotDates.stream().forEach(date -> {
            if(!snapshotDTOMap.containsKey(date)){
                AssetDailySnapshotDTO snapshotDTO = new AssetDailySnapshotDTO();
                snapshotDTO.setAvailable(BigDecimal.ZERO);
                snapshotDTOMap.put(date, snapshotDTO);
            }
        });
        //计算昨日收益
        staticList.setTodayReceived((snapshotDTOMap.get(snapshotDates.get(1)).getAvailable().subtract(snapshotDTOMap.get(snapshotDates.get(0)).getAvailable())).abs());
        result.setResponseEnum(WebResponseEnum.SUCCESS);
        result.setObj(staticList);
        return ResponseEntityUtil.result(result);
    }

    @Override
    public ResponseEntity<WebResultTopLadder> topLadder(String saasId, String idToken, String handleName, String appId, String chainId, String assetType, Integer limit, String strategy,
                                                        String businessType) {
        CustomerBindDTO customer;
        if(handleName != null && appId != null){
            customer = kwebBaseService.getCustomerByHandleName(saasId, appId, handleName, chainId);
        }else{
            customer = CustomerHolder.qcustomer();
        }
        if(customer == null){
            return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_NOT_FOUND);
        }
        TopLadderRequest topLadderRequest = new TopLadderRequest();
        topLadderRequest.setSaasId(saasId);
        topLadderRequest.setCid(customer.getCid());
        topLadderRequest.setAssetType(assetType);
        topLadderRequest.setLimit(limit);
        topLadderRequest.setStrategy(strategy == null ? "unfold" : strategy);
        if (businessType != null) {
            topLadderRequest.setBusinessType(MemberConstant.BusinessType.valueOf(businessType));
        }

        TopLadderDTO ladderDTO = remoteMemberService.topLadder(topLadderRequest);
        LadderVO ladderVO = new LadderVO();
        ladderVO.setMyLadder(BeanUtil.copyProperties(ladderDTO.getMyLadder(), TopLadder::new));
        ladderVO.setLadders(BeanUtil.copyProperties(ladderDTO.getLadders(), TopLadder::new));

        WebResultTopLadder result = new WebResultTopLadder();
        result.setResponseEnum(WebResponseEnum.SUCCESS);
        result.setObj(ladderVO);
        return ResponseEntityUtil.result(result);
    }

    @Override
    public ResponseEntity<WebResultEdgenPoint> extensionPoints(String saasId, List<String> handles) {
        EdgenLadderRequest request = new EdgenLadderRequest();
        request.setSaasId(saasId);
        request.setTwitterHandles(handles);
        List<EdgenLadderDTO> ladders = remoteMemberService.edgenLadderByHandles(request);

        List<EdgenPointPointsInner> points = new ArrayList<>();
        for (EdgenLadderDTO ladder : ladders) {
            EdgenPointPointsInner point = new EdgenPointPointsInner();
            point.setHandle(ladder.getTwitterHandle());
            point.setPoint(null == ladder.getTotalPoint() ? "0" : ladder.getTotalPoint().stripTrailingZeros().toPlainString());
            point.setPointPercentage(null == ladder.getRankingPercentage() ? "1" : ladder.getRankingPercentage().stripTrailingZeros().toPlainString());
            point.setUserType(ladder.getUserType());
            points.add(point);
        }
        EdgenPoint edgenPoint = new EdgenPoint();
        edgenPoint.setPoints(points);

        WebResultEdgenPoint result = new WebResultEdgenPoint();
        result.setObj(edgenPoint);
        result.setResponseEnum(WebResponseEnum.SUCCESS);
        return ResponseEntityUtil.result(result);
    }

    public static void main(String[] args) {
        LocalDateTime now = LocalDateTime.now();
        List<String> snapshotDates = IntStream.range(0, 8)
                .mapToObj(i -> now.minusHours(i * 4).withMinute(0).withSecond(0).withNano(0))
                .map(dateTime -> {
                    int hour = dateTime.getHour();
                    int adjustedHour = (hour / 4) * 4;
                    return dateTime.withHour(adjustedHour).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH"));
                })
        .collect(Collectors.toList());
        Collections.reverse(snapshotDates);
        System.out.println(snapshotDates);
    }

    private LadderItem toResponse(QuestsLadderDTO ladderDTO){
        LadderItem item = new LadderItem();
        item.setCustomerId(ladderDTO.getCustomerId());
        item.setLevel(ladderDTO.getLevel());
        item.setPoint(ladderDTO.getPoint().setScale(1, RoundingMode.DOWN));
        item.setRank(ladderDTO.getRank());
        item.setIcon(ladderDTO.getIcon());
        item.setNickName(ladderDTO.getNickName());
        return item;
    }

    private BigDecimal add(Integer assetType, BigDecimal available, String saasId, String uid){
        TCustomerDTO tCustomerDTO = remoteCustomerBindService.findTCustomerByUid(saasId, uid);
        if(tCustomerDTO == null){
            return available;
        }
        return available.add(vipAdd(available, tCustomerDTO)).add(pfpAdd(available, tCustomerDTO));
    }

    private BigDecimal vipAdd(BigDecimal originalAvailable, TCustomerDTO customer){
        if(customer != null && BooleanUtils.isTrue(customer.getVip())){
            return originalAvailable.multiply(new BigDecimal(0.2).multiply(new BigDecimal(customer.getVipCount())));
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal pfpAdd(BigDecimal originalAvailable, TCustomerDTO customer) {
        if (customer.getPfpCount() == null) {
            return BigDecimal.ZERO;
        }
        return originalAvailable.multiply(new BigDecimal(customer.getPfpCount()));
    }
}
