package com.kikitrade.member.delegate;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.kikitrade.asset.api.RemoteAssetOperateService;
import com.kikitrade.asset.api.RemoteAssetService;
import com.kikitrade.asset.model.AssetDTO;
import com.kikitrade.asset.model.AssetLedgerReadonlyDTO;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.asset.model.constant.AssetCategory;
import com.kikitrade.asset.model.constant.AssetOperateType;
import com.kikitrade.asset.model.constant.AssetType;
import com.kikitrade.asset.model.exception.AssetException;
import com.kikitrade.asset.model.request.AssetLedgersListRequest;
import com.kikitrade.asset.model.request.AssetLedgersStatRequest;
import com.kikitrade.asset.model.request.AssetTransferOutRequest;
import com.kikitrade.asset.model.response.AssetLaddersResponse;
import com.kikitrade.asset.model.response.AssetLaddersStatResponse;
import com.kikitrade.asset.model.response.AssetOperateResponse;
import com.kikitrade.asset.model.response.AssetResponseCode;
import com.kikitrade.framework.common.util.BeanUtil;
import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import com.kikitrade.kweb.config.QuestWebProperties;
import com.kikitrade.kweb.constants.WebResponseEnum;
import com.kikitrade.kweb.interceptor.CustomerHolder;
import com.kikitrade.kweb.model.common.WebResult;
import com.kikitrade.kweb.model.customer.QCustomer;
import com.kikitrade.kweb.utils.ResponseEntityUtil;
import com.kikitrade.member.api.RemoteMemberCastleService;
import com.kikitrade.member.api.RemoteMemberService;
import com.kikitrade.member.generated.api.v2.MemberV2ApiDelegate;
import com.kikitrade.member.generated.model.v2.AssetTypeEnum;
import com.kikitrade.member.generated.model.v2.Ladder;
import com.kikitrade.member.generated.model.v2.LadderItem;
import com.kikitrade.member.generated.model.v2.MemberOrderVO;
import com.kikitrade.member.generated.model.v2.MembershipVO;
import com.kikitrade.member.generated.model.v2.OrderVO;
import com.kikitrade.member.generated.model.v2.PointLedger;
import com.kikitrade.member.generated.model.v2.PointLedgerList;
import com.kikitrade.member.generated.model.v2.TransferOutRequest;
import com.kikitrade.member.generated.model.v2.WebResultAssetVO;
import com.kikitrade.member.generated.model.v2.WebResultLadder;
import com.kikitrade.member.generated.model.v2.WebResultMemberOrderVO;
import com.kikitrade.member.generated.model.v2.WebResultMembershipVO;
import com.kikitrade.member.generated.model.v2.WebResultOrderVO;
import com.kikitrade.member.generated.model.v2.WebResultPointLedgerList;
import com.kikitrade.member.generated.model.v2.*;
import com.kikitrade.member.model.MemberProductItemDTO;
import com.kikitrade.member.model.QuestsLadderDTO;
import com.kikitrade.member.model.request.QuestLadderRequest;
import com.kikitrade.member.model.response.QuestLadderResponse;
import com.kikitrade.member.model.response.ResponseCode;
import com.kikitrade.order.api.RemoteOrderService;
import com.kikitrade.order.model.OrderDTO;
import com.kikitrade.order.model.constant.OrderEventEnum;
import com.kikitrade.order.model.exception.OrderException;
import com.kikitrade.order.model.exception.OrderExceptionCode;
import com.kikitrade.order.model.request.PlaceOrderRequest;
import com.kikitrade.order.model.response.PlaceOrderResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/2/21 19:51
 */
@Service
@Slf4j
public class MemberV2ApiDelegateImpl implements MemberV2ApiDelegate {

    @DubboReference
    private RemoteMemberCastleService remoteMemberCastleService;
    @DubboReference
    private RemoteAssetService remoteAssetService;
    @DubboReference
    private RemoteMemberService remoteMemberService;
    @DubboReference
    private RemoteOrderService remoteOrderService;
    @Resource
    private QuestWebProperties questWebProperties;
    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;
    @DubboReference
    private RemoteAssetOperateService remoteAssetOperateService;

    @Override
    public ResponseEntity<WebResultPointLedgerList> pointLedgersV1(String type, Integer offset, Integer limit, String saasId) {
        QCustomer customer = CustomerHolder.qcustomer();
        AssetLedgersStatRequest statRequest = new AssetLedgersStatRequest();
        statRequest.setCustomerId(customer.getUid());
        statRequest.setAssetType(AssetType.POINT);
        statRequest.setAssetBusinessType(AssetBusinessType.POST);
        statRequest.setAssetCategory(AssetCategory.NORMAL);
        statRequest.setSaasId(customer.getSaasId());
        statRequest.setAssetOperateType(AssetOperateType.TRANSFER_IN);
        statRequest.setStart(Date.from(LocalDateTimeUtil.beginOfDay(LocalDateTimeUtil.now()).toInstant(ZoneOffset.UTC)));
        statRequest.setEnd(Date.from(LocalDateTimeUtil.endOfDay(LocalDateTimeUtil.now()).toInstant(ZoneOffset.UTC)));
        AssetLaddersStatResponse statResponse = remoteAssetService.statAssetLedgers(statRequest);

        WebResultPointLedgerList result = new WebResultPointLedgerList();
        PointLedgerList point = new PointLedgerList();
        point.setPoint(statResponse.getSumPoint());
        point.setTodayPoint(statResponse.getDatePoint());
        point.setAppPoint(statResponse.getAppPoint());
        point.setRank(statResponse.getRank());

        if(limit != null && limit > 0){
            AssetLedgersListRequest listRequest = new AssetLedgersListRequest();
            listRequest.setCustomerId(customer.getUid());
            listRequest.setAssetCategory(AssetCategory.NORMAL);
            listRequest.setSaasId(customer.getSaasId());
            listRequest.setAssetOperateType(AssetOperateType.TRANSFER_IN);
            listRequest.setAssetType(AssetType.POINT);
            listRequest.setOffset(offset);
            listRequest.setLimit(limit);
            AssetLaddersResponse response = remoteAssetService.assetLadders(listRequest);
            List<AssetLedgerReadonlyDTO> assetLedgers = response.getAssetLedgers();
            List<PointLedger> pointLedgers = new ArrayList<>();
            for(AssetLedgerReadonlyDTO ledger : assetLedgers){
                PointLedger pointLedger = new PointLedger();
                pointLedger.setId(ledger.getId());
                pointLedger.setAmount(ledger.getAmount());
                pointLedger.setCreated(ledger.getCreated());
                pointLedger.setBusinessType(AssetBusinessType.valueOf(ledger.getBusinessType()).getDesc());
                pointLedger.setMenu(ledger.getMenu());
                pointLedger.setDescI18n(ledger.getDescI18n());
                pointLedgers.add(pointLedger);
            }
            point.setPointLedgers(pointLedgers);
        }

        result.setResponseEnum(WebResponseEnum.SUCCESS);
        result.setObj(point);
        return ResponseEntityUtil.result(result);
    }

    @Override
    public ResponseEntity<WebResultPointLedgerList> pointLedgers(String type, Integer offset, Integer limit, String saasId) {
        QCustomer customer = CustomerHolder.qcustomer();
        AssetLedgersStatRequest statRequest = new AssetLedgersStatRequest();
        statRequest.setCustomerId(customer.getUid());
        statRequest.setAssetType(AssetType.POINT);
        statRequest.setAssetBusinessType(AssetBusinessType.POST);
        statRequest.setAssetCategory(AssetCategory.NORMAL);
        statRequest.setSaasId(customer.getSaasId());
        statRequest.setAssetOperateType(AssetOperateType.TRANSFER_IN);
        statRequest.setStart(Date.from(LocalDateTimeUtil.beginOfDay(LocalDateTimeUtil.now()).toInstant(ZoneOffset.UTC)));
        statRequest.setEnd(Date.from(LocalDateTimeUtil.endOfDay(LocalDateTimeUtil.now()).toInstant(ZoneOffset.UTC)));
        AssetLaddersStatResponse statResponse = remoteAssetService.statAssetLedgers(statRequest);

        WebResultPointLedgerList result = new WebResultPointLedgerList();
        PointLedgerList point = new PointLedgerList();
        point.setPoint(statResponse.getSumPoint());
        point.setTodayPoint(statResponse.getDatePoint());
        point.setAppPoint(statResponse.getAppPoint());
        point.setRank(statResponse.getRank());
        point.setRankValue(statResponse.getRankValue());

        if(limit != null && limit > 0){
            AssetLedgersListRequest listRequest = new AssetLedgersListRequest();
            listRequest.setCustomerId(customer.getUid());
            listRequest.setAssetCategory(AssetCategory.NORMAL);
            listRequest.setSaasId(customer.getSaasId());
            listRequest.setAssetOperateType(AssetOperateType.TRANSFER_IN);
            listRequest.setAssetType(AssetType.POINT);
            listRequest.setOffset(offset);
            listRequest.setLimit(limit);
            AssetLaddersResponse response = remoteAssetService.assetLadders(listRequest);
            List<AssetLedgerReadonlyDTO> assetLedgers = response.getAssetLedgers();
            List<PointLedger> pointLedgers = new ArrayList<>();
            for(AssetLedgerReadonlyDTO ledger : assetLedgers){
                PointLedger pointLedger = new PointLedger();
                pointLedger.setId(ledger.getId());
                pointLedger.setAmount(ledger.getAmount());
                pointLedger.setCreated(ledger.getCreated());
                pointLedger.setBusinessType(AssetBusinessType.valueOf(ledger.getBusinessType()).getDesc());
                pointLedger.setMenu(ledger.getMenu());
                pointLedger.setDescI18n(ledger.getDescI18n());
                pointLedger.setOperateType(ledger.getOperateType().intValue());
                pointLedgers.add(pointLedger);
            }
            point.setPointLedgers(pointLedgers);
        }

        result.setResponseEnum(WebResponseEnum.SUCCESS);
        result.setObj(point);
        return ResponseEntityUtil.result(result);
    }

    /**
     * 徽章排行榜
     * @param saasId  (required)
     * @param season  (required)
     * @param level 徽章等级 (optional)
     * @param limit 排行榜数量,范围：[0,200] (optional, default to 30)
     * @return
     */
    @Override
    public ResponseEntity<WebResultLadder> globalLadder(String saasId, String season, Integer level, Integer limit) {
        try {
            //本赛季开始时间
            LocalDateTime seasonStartTime =
                OffsetDateTime.now(ZoneOffset.UTC).toLocalDate().with(TemporalAdjusters.firstDayOfMonth())
                    .atTime(0, 0, 0);
            long firstLadderTime = Date.from(seasonStartTime.with(TemporalAdjusters.next(DayOfWeek.WEDNESDAY)).toInstant(ZoneOffset.UTC)).getTime();

            //计算获取cycle
            long c = 1;
            if (OffsetDateTime.now().toEpochSecond() * 1000L >= firstLadderTime) {
                c = ((OffsetDateTime.now().toEpochSecond() * 1000L - firstLadderTime) / 1000 / 60 / 60 / 24 / 7) + 2;
            }
            String cycle = String.format("%02d", c);

            QuestLadderRequest request = new QuestLadderRequest();
            CustomerBindDTO qcustomer = CustomerHolder.qcustomer();
            request.setCustomerId(qcustomer.getUid());
            request.setCid(qcustomer.getCid());
            request.setSaasId(saasId);
            request.setSeason(getCurrentSeasonName());
            request.setCycle(cycle);
            request.setLevel(level);
            request.setLimit(limit);
            log.info("globalLadder:{}", request);
            QuestLadderResponse response = remoteMemberService.questsLadders(request);
            Ladder ladder = new Ladder();
            //排行榜状态
            if(response.getCode() == ResponseCode.QUESTS_LADDER_CALCULATING){
                return ResponseEntityUtil.result(WebResponseEnum.QUESTS_LADDER_CALCULATING);
            }
            ladder.setCalculating(false);
            Date now = new Date();
            LocalDateTime last;
            last = now.toInstant().atOffset(ZoneOffset.UTC).toLocalDate().atTime(0, 0, 0);
            long time = Date.from(last.with(TemporalAdjusters.next(DayOfWeek.WEDNESDAY)).toInstant(ZoneOffset.UTC)).getTime();

            ladder.setEndTime(time);
            ladder.setCycle((int)c);
            ladder.setMyLadder(toResponse(response.getMyLadder()));
            ladder.setGlobalLadder(response.getLadders().stream().map(this::toResponse).collect(Collectors.toList()));
            WebResultLadder webResult = new WebResultLadder();
            webResult.setResponseEnum(WebResponseEnum.SUCCESS);
            webResult.setObj(ladder);
            return ResponseEntityUtil.result(webResult);
        }catch (Exception ex){
            log.error("globalLadder exception", ex);
            return null;
        }
    }

    /**
     * @param type 查询用户某个资产, 传nft (required)
     * @return
     */
    @Override
    public ResponseEntity<WebResultAssetVO> memberAsset(String type) {
        return MemberV2ApiDelegate.super.memberAsset(type);
    }

    /**
     * 查询当前用户会员信息
     * @param saasId saasId (required)
     * @return
     */
    @Override
    public ResponseEntity<WebResultMembershipVO> membership(String saasId) {
        QCustomer qcustomer = CustomerHolder.qcustomer();
        try{
            MemberProductItemDTO memberCastle = remoteMemberCastleService.memberCastle(saasId, qcustomer.getUid());
            MembershipVO membershipVO = new MembershipVO();
            membershipVO.setMaxCapacity(memberCastle.getCapacity());
            membershipVO.setCapacity(memberCastle.getProduction().intValue());
            membershipVO.setLevel(memberCastle.getLevel());
            membershipVO.setProductivity(memberCastle.getProductivity().multiply(BigDecimal.valueOf(60)).setScale(1, RoundingMode.HALF_DOWN).toPlainString());
            membershipVO.setSort(memberCastle.getSort());
            membershipVO.setUpgradeSuccessRate(memberCastle.getUpgradeSuccessRate());
            membershipVO.setUpgradePoint(memberCastle.getUpgradePoint());
            membershipVO.setExpansionUnit(memberCastle.getExpansionUnit());
            membershipVO.setExpansionUsed(memberCastle.getExpansionUsed());
            membershipVO.setPoint(getAvailable(qcustomer));

            WebResultMembershipVO webResult = new WebResultMembershipVO();
            webResult.setResponseEnum(WebResponseEnum.SUCCESS);
            webResult.setObj(membershipVO);
            return ResponseEntityUtil.result(webResult);
        }catch (Exception ex){
            log.error("membership:{}", ex, qcustomer);
            return ResponseEntityUtil.systemError();
        }

    }

    private BigDecimal getAvailable(QCustomer qcustomer){
        AssetLedgersListRequest listRequest = new AssetLedgersListRequest();
        listRequest.setCustomerId(qcustomer.getUid());
        listRequest.setAssetCategory(AssetCategory.NORMAL);
        listRequest.setSaasId(qcustomer.getSaasId());
        listRequest.setAssetOperateType(AssetOperateType.TRANSFER_IN);
        listRequest.setAssetType(AssetType.POINT);
        listRequest.setOffset(0);
        listRequest.setLimit(1);
        AssetDTO asset = remoteAssetService.asset(qcustomer.getSaasId(), qcustomer.getUid(), AssetType.POINT, AssetCategory.NORMAL);
        if(asset != null){
            return asset.getAvailable();
        }
        return BigDecimal.ZERO;
    }


    @Override
    public ResponseEntity<WebResultMemberOrderVO> memberBusinessOrder(String saasId, String orderId) {
        OrderDTO order = remoteOrderService.getOrder(orderId);
        if(order == null){
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_PARAMETER_INVALID);
        }

        MemberOrderVO orderVO = new MemberOrderVO();
        orderVO.setStatus(order.getStatus().name());

        if( "success".equals(order.getStatus().name()) && OrderEventEnum.lottery.equals(order.getEvent())){
            JSONObject pa=JSONObject.parseObject(order.getParam());
            orderVO.setAmount(pa.getString("amount"));
        }

        WebResultMemberOrderVO webResult = new WebResultMemberOrderVO();
        webResult.setResponseEnum(WebResponseEnum.SUCCESS);
        webResult.setObj(orderVO);
        return ResponseEntityUtil.result(webResult);
    }

    /**
     * 会员升级
     * @param saasId saasId (required)
     * @return
     */
    @Override
    public ResponseEntity<WebResultOrderVO> upgrade(String saasId) {
        QCustomer qcustomer = CustomerHolder.qcustomer();
        WebResultOrderVO webResult = new WebResultOrderVO();
        webResult.setResponseEnum(WebResponseEnum.SUCCESS);
        try{
            OrderVO orderVO = buildOrder(saasId, OrderEventEnum.upgrade, qcustomer);
            webResult.setObj(orderVO);
            return ResponseEntityUtil.result(webResult);
        }catch (OrderException ex){
            if(ex.getCode() == OrderExceptionCode.ASSET_NOT_ENOUGH){
                webResult.setResponseEnum(WebResponseEnum.POINT_NOT_ENOUGH);
                return ResponseEntityUtil.result(webResult);
            }else if(ex.getCode() == OrderExceptionCode.ORDER_PADDING){
                webResult.setResponseEnum(WebResponseEnum.MEMBER_UPGRADING);
                return ResponseEntityUtil.result(webResult);
            }
        }
        webResult.setResponseEnum(WebResponseEnum.SYSTEM_ERROR);
        return ResponseEntityUtil.result(webResult);
    }

    /**
     * 会员城堡仓库扩容
     * @param code 需要兑换黄金的业务code，本次传kingdom (required)
     * @param saasId saasId (required)
     * @return
     */
    @Override
    public ResponseEntity<WebResultOrderVO> v2MemberCodeExpansionPost(String code, String saasId) {
        QCustomer qcustomer = CustomerHolder.qcustomer();
        WebResultOrderVO webResult = new WebResultOrderVO();
        webResult.setResponseEnum(WebResponseEnum.SUCCESS);
        try{
            OrderVO orderVO = buildOrder(saasId, OrderEventEnum.expansion, qcustomer);
            webResult.setObj(orderVO);
            return ResponseEntityUtil.result(webResult);
        }catch (OrderException ex){
            if(ex.getCode() == OrderExceptionCode.ASSET_NOT_ENOUGH){
                webResult.setResponseEnum(WebResponseEnum.POINT_NOT_ENOUGH);
                return ResponseEntityUtil.result(webResult);
            }else if(ex.getCode() == OrderExceptionCode.ORDER_PADDING){
                webResult.setResponseEnum(WebResponseEnum.MEMBER_UPGRADING);
                return ResponseEntityUtil.result(webResult);
            }
        }
        webResult.setResponseEnum(WebResponseEnum.SYSTEM_ERROR);
        return ResponseEntityUtil.result(webResult);
    }

    /**
     * 城堡收割积分
     * @param code 需要兑换黄金的业务code，本次传kingdom (required)
     * @param saasId saasId (required)
     * @return
     */
    @Override
    public ResponseEntity<WebResultOrderVO> v2MemberCodeHarvestPost(String code, String saasId) {
        QCustomer qcustomer = CustomerHolder.qcustomer();
        WebResultOrderVO webResult = new WebResultOrderVO();
        webResult.setResponseEnum(WebResponseEnum.SUCCESS);
        try{
            OrderVO orderVO = buildOrder(saasId, OrderEventEnum.harvest, qcustomer);
            webResult.setObj(orderVO);
            return ResponseEntityUtil.result(webResult);
        }catch (OrderException ex){
            if(ex.getCode() == OrderExceptionCode.ORDER_PADDING){
                webResult.setResponseEnum(WebResponseEnum.MEMBER_UPGRADING);
                return ResponseEntityUtil.result(webResult);
            }
        }
        webResult.setResponseEnum(WebResponseEnum.SYSTEM_ERROR);
        return ResponseEntityUtil.result(webResult);
    }

    private OrderVO buildOrder(String saasId, OrderEventEnum event ,QCustomer qcustomer) throws OrderException{
        try{
            MemberProductItemDTO productItemDTO = remoteMemberCastleService.memberCastle(saasId, qcustomer.getUid());
            PlaceOrderRequest orderRequest = new PlaceOrderRequest();
            orderRequest.setCustomerId(qcustomer.getUid());
            orderRequest.setSaasId(saasId);
            orderRequest.setEvent(event);
            orderRequest.setParam(JSON.toJSONString(productItemDTO));
            PlaceOrderResponse response = remoteOrderService.placeOrder(orderRequest);

            OrderVO orderVO = new OrderVO();
            orderVO.setOrderId(response.getOrderId());
            return orderVO;
        }catch (OrderException ex){
            log.error("buildOrder error:{}", qcustomer, ex);
            throw ex;
        }
    }

    @Override
    public ResponseEntity<WebResultLadder> historyLadder(String saasId, String season, String cycleString,
        Integer level, Integer limit) {
        WebResultLadder webResult = new WebResultLadder();
        try {
            //第一次结算时间
            int cycle = Integer.parseInt(cycleString);
            if (cycle < 1) {
                log.error("historyLadder request cycle error, cycle={}", cycle);
                webResult.setResponseEnum(WebResponseEnum.QUESTS_LADDER_CALCULATING);
                return ResponseEntityUtil.result(webResult);
            }
            CustomerBindDTO qcustomer = CustomerHolder.qcustomer();
            String currentSeasonName = getCurrentSeasonName();
            log.info("historyLadder request. customerId:{}, cid:{}, currentSeasonName:{}", qcustomer.getUid(),
                qcustomer.getCid(), currentSeasonName);
            QuestLadderResponse response =
                getRemoteHistoryLadders(qcustomer, saasId, currentSeasonName, cycleString, level);
            if (response == null || response.getCode() == ResponseCode.QUESTS_LADDER_CALCULATING) {
                webResult.setResponseEnum(WebResponseEnum.QUESTS_LADDER_CALCULATING);
                return ResponseEntityUtil.result(webResult);
            }
            //本赛季开始时间
            LocalDateTime seasonStartTime = OffsetDateTime.now(ZoneOffset.UTC).toLocalDate().with(TemporalAdjusters.firstDayOfMonth()).atTime(0, 0, 0);
            Ladder ladder = new Ladder();
            long firstLadderTime = Date.from(seasonStartTime.with(TemporalAdjusters.next(DayOfWeek.WEDNESDAY)).toInstant(ZoneOffset.UTC)).getTime();

            ladder.setStartTime(cycle == 1 ? seasonStartTime.toEpochSecond(ZoneOffset.UTC) * 1000 : firstLadderTime + ((cycle - 2) * TimeUnit.DAYS.toMillis(7)));
            ladder.setEndTime(cycle == 1 ? firstLadderTime : firstLadderTime + ((cycle - 1) * TimeUnit.DAYS.toMillis(7)));
            ladder.setCycle(cycle);
            ladder.setCalculating(false);
            ladder.setMyLadder(toResponse(response.getMyLadder()));
            ladder.setGlobalLadder(response.getLadders().stream().map(this::toResponse).collect(Collectors.toList()));
            webResult.setResponseEnum(WebResponseEnum.SUCCESS);
            webResult.setObj(ladder);
            return ResponseEntityUtil.result(webResult);
        }catch (Exception ex){
            log.error("historyLadder exception", ex);
            webResult.setResponseEnum(WebResponseEnum.SYSTEM_ERROR);
            return null;
        }
    }

    private QuestLadderResponse getRemoteHistoryLadders(CustomerBindDTO customer, String saasId, String season, String cycle, Integer level){
        QuestLadderRequest request = new QuestLadderRequest();
        String cycleString = String.format("%02d", Integer.parseInt(cycle));
        if(season.equals("season0")){
            //历史数据
            cycleString = String.format("2024%02d", Integer.parseInt(cycle));
        }
        request.setCustomerId(customer.getUid());
        request.setCid(customer.getCid());
        request.setSaasId(saasId);
        request.setSeason(season);
        request.setCycle(cycleString);
        request.setLevel(level);
        log.info("getRemoteHistoryLadders request:{}", JSON.toJSONString(request));
        QuestLadderResponse response = remoteMemberService.questsHistoryLadders(request);
        log.info("getRemoteHistoryLadders response:{}", JSON.toJSONString(response));
        return response;
    }

    private LadderItem toResponse(QuestsLadderDTO ladderDTO){
        LadderItem item = new LadderItem();
        item.setCustomerId(ladderDTO.getCustomerId());
        item.setLevel(ladderDTO.getLevel());
        item.setPoint(ladderDTO.getPoint().setScale(1, RoundingMode.DOWN));
        item.setRank(ladderDTO.getRank());
        item.setIcon(ladderDTO.getIcon());
        item.setNickName(ladderDTO.getNickName());
        return item;
    }

    private BigDecimal add(BigDecimal available, String saasId, String uid){
        TCustomerDTO tCustomerDTO = remoteCustomerBindService.findTCustomerByUid(saasId, uid);
        if(tCustomerDTO == null){
            return available;
        }
        return available.add(vipAdd(available, tCustomerDTO)).add(pfpAdd(available, tCustomerDTO));
    }

    private BigDecimal vipAdd(BigDecimal originalAvailable, TCustomerDTO customer){
        if(customer != null && BooleanUtils.isTrue(customer.getVip())){
            return originalAvailable.multiply(new BigDecimal(0.2).multiply(new BigDecimal(customer.getVipCount())));
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal pfpAdd(BigDecimal originalAvailable, TCustomerDTO customer){
        if(customer.getPfpCount() == null){
            return BigDecimal.ZERO;
        }
        return originalAvailable.multiply(new BigDecimal(customer.getPfpCount()));
    }

    private String getCurrentSeasonName() {
        Long season1StartTime = questWebProperties.getSeasonStartTime();
        LocalDate season1LocalDate = new Date(season1StartTime).toInstant().atOffset(ZoneOffset.UTC).toLocalDate();
        LocalDateTime currentSeasonStartTime =
            OffsetDateTime.now(ZoneOffset.UTC).toLocalDate().with(TemporalAdjusters.firstDayOfMonth()).atTime(0, 0, 0);
        //判断与season1间隔几个月，得知当前第几赛季
        int seasonIdx = Period.between(season1LocalDate, currentSeasonStartTime.toLocalDate()).getYears() * 12 + Period.between(season1LocalDate, currentSeasonStartTime.toLocalDate()).getMonths() + 1;
        return "season" + seasonIdx;
    }

    /**
     * POST /member/transferout : /member/transferout
     * 消耗积分
     *
     * @param JWT_TOKEN          JWT_TOKEN (required)
     * @param saasId             (required)
     * @param transferOutRequest (optional)
     * @return OK (status code 200)
     */
    @Override
    public ResponseEntity<WebResult> transferOut(String JWT_TOKEN, String saasId, TransferOutRequest transferOutRequest) {
        try{
            log.info("v2 transferOut: {}", transferOutRequest);
            if (Objects.isNull(transferOutRequest.getType())) {
                transferOutRequest.setType(AssetTypeEnum.POINT);
            }
            AssetTransferOutRequest request = new AssetTransferOutRequest();
            BeanUtil.copyProperties(transferOutRequest,request);
            request.setCustomerId(CustomerHolder.qcustomer().getUid());
            request.setSaasId(saasId);
            request.setBusinessType(AssetBusinessType.valueOf(transferOutRequest.getBusinessType().name()));
            request.setType(AssetType.valueOf(transferOutRequest.getType().name()));
            request.setAmount(new BigDecimal(transferOutRequest.getAmount()));
            AssetOperateResponse response = remoteAssetOperateService.transferOut(request);
            AssetResponseCode code = response.getCode();
            if(code.isSuccess()){
                return ResponseEntityUtil.result(WebResponseEnum.SUCCESS);
            }
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }catch (AssetException ex){
            log.error("v2 transferOut error:{}", transferOutRequest, ex);
            if(ex.getCode() == AssetResponseCode.INSUFFICIENT_BALANCE){
                log.error("transferOut error:{}", transferOutRequest, ex);
                return ResponseEntityUtil.result(WebResponseEnum.CUSTOMER_INSUFFICIENT_BALANCE);
            }
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }catch (Exception ex){
            log.error("v2 transferOut error:{}", transferOutRequest, ex);
            return ResponseEntityUtil.result(WebResponseEnum.SYSTEM_ERROR);
        }
    }
}
