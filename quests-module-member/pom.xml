<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.kikitrade</groupId>
        <artifactId>quests-web</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <groupId>com.kikitrade</groupId>
    <artifactId>quests-module-member</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>quests-module-member</name>

    <properties>
        <java-version>17</java-version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.property.path>..</project.property.path>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>quests-module-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.kikitrade</groupId>
            <artifactId>kmember-api</artifactId>
        </dependency>
    </dependencies>
    <profiles>
        <profile>
            <id>local</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.openapitools</groupId>
                        <artifactId>openapi-generator-maven-plugin</artifactId>
                        <version>6.0.0</version>
                        <executions>
                            <execution>
                                <id>member-v2</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.basedir}/src/main/resources/api/member-v2.yaml</inputSpec>
                                    <generatorName>spring</generatorName>
                                    <apiPackage>com.kikitrade.member.generated.api.v2</apiPackage>
                                    <modelPackage>com.kikitrade.member.generated.model.v2</modelPackage>
                                    <generateApiTests>false</generateApiTests>
                                    <generateModelTests>false</generateModelTests>
                                    <addTestCompileSourceRoot>false</addTestCompileSourceRoot>
                                    <output>.</output>
                                    <importMappings>
                                        <importMapping>WebResult=com.kikitrade.kweb.model.common.WebResult</importMapping>
                                        <importMapping>Date=java.util.Date</importMapping>
                                        <importMapping>Map=java.util.Map</importMapping>
                                    </importMappings>
                                    <configOptions>
                                        <delegatePattern>true</delegatePattern>
                                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                        <useTags>true</useTags>
                                    </configOptions>
                                </configuration>
                            </execution>
                            <execution>
                                <id>member-s2</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.basedir}/src/main/resources/api/member-s2.yaml</inputSpec>
                                    <generatorName>spring</generatorName>
                                    <apiPackage>com.kikitrade.member.generated.api.s2</apiPackage>
                                    <modelPackage>com.kikitrade.member.generated.model.s2</modelPackage>
                                    <generateApiTests>false</generateApiTests>
                                    <generateModelTests>false</generateModelTests>
                                    <addTestCompileSourceRoot>false</addTestCompileSourceRoot>
                                    <output>.</output>
                                    <importMappings>
                                        <importMapping>WebResult=com.kikitrade.kweb.model.common.WebResult</importMapping>
                                        <importMapping>Date=java.util.Date</importMapping>
                                        <importMapping>Map=java.util.Map</importMapping>
                                    </importMappings>
                                    <configOptions>
                                        <delegatePattern>true</delegatePattern>
                                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                        <useTags>true</useTags>
                                    </configOptions>
                                </configuration>
                            </execution>
                            <execution>
                                <id>member-v3</id>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <inputSpec>${project.basedir}/src/main/resources/api/member-v3.yaml</inputSpec>
                                    <generatorName>spring</generatorName>
                                    <apiPackage>com.kikitrade.member.generated.api.v3</apiPackage>
                                    <modelPackage>com.kikitrade.member.generated.model.v3</modelPackage>
                                    <generateApiTests>false</generateApiTests>
                                    <generateModelTests>false</generateModelTests>
                                    <addTestCompileSourceRoot>false</addTestCompileSourceRoot>
                                    <output>.</output>
                                    <importMappings>
                                        <importMapping>WebResult=com.kikitrade.kweb.model.common.WebResult</importMapping>
                                        <importMapping>Date=java.util.Date</importMapping>
                                        <importMapping>Map=java.util.Map</importMapping>
                                    </importMappings>
                                    <configOptions>
                                        <delegatePattern>true</delegatePattern>
                                        <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                        <useTags>true</useTags>
                                    </configOptions>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
